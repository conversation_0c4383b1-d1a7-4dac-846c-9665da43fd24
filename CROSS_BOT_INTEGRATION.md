# Cross-Bot Integration: Manager <PERSON><PERSON> & Staff Manager

This document outlines the new cross-bot integration features that allow the Manager <PERSON> and Staff Manager to work together for comprehensive staff activity tracking.

## Overview

The integration enables:
- **Ban Monitoring**: Staff Manager monitors BattleMetrics for bans and tracks which staff member issued them
- **Ticket Claim Tracking**: Manager <PERSON><PERSON> notifies Staff Manager when tickets are claimed by staff
- **Message Activity Tracking**: Both bots track staff message activity
- **Shared Statistics**: Unified statistics database accessible by both bots

## Features Implemented

### 1. Enhanced Ban Monitoring (Staff Manager)

**File**: `Staff Manager/utils/banMonitorService.js`

- Monitors BattleMetrics API for new bans every minute
- Attempts to identify which staff member issued each ban
- Updates staff statistics when bans are attributed
- Sends webhook notifications for staff ban activity

**Key Methods**:
- `startMonitoring()`: Begins ban monitoring
- `checkForNewBans()`: Fetches and processes new bans
- `identifyStaffMember()`: Attempts to determine ban issuer
- `recordStaffBan()`: Records ban activity for staff member

### 2. Cross-Bot Communication (Both Bots)

**Files**: 
- `Staff Manager/utils/crossBotCommunication.js`
- `Manager Bot/utils/staffManagerIntegration.js`

Handles communication between the two bots for:
- Ticket claim notifications
- Message activity tracking
- Ban activity notifications
- Staff statistics synchronization

### 3. Shared Statistics Database

**Files**:
- `Staff Manager/models/sharedStaffStats.js`
- `Manager Bot/models/sharedStaffStats.js`

Unified statistics model tracking:
- **Tickets**: Total/weekly/monthly claimed tickets
- **Messages**: Total/weekly/monthly messages sent
- **Bans**: Total/weekly/monthly bans issued
- **Voice Activity**: Total/weekly/monthly voice minutes
- **In-Game Activity**: Total/weekly/monthly in-game minutes

### 4. Staff Ban Activity Tracking

**File**: `Staff Manager/models/staffBanActivity.js`

Detailed tracking of individual ban actions:
- Staff member who issued the ban
- Player information (Steam ID, name, BM ID)
- Ban details (reason, server, expiration)
- Timestamps and processing status

### 5. Enhanced Commands

#### Staff Manager Commands:
- `/staffactivity` - View comprehensive staff activity statistics
  - Individual staff member stats
  - All staff overview with rankings
  - Timeframe options (week/month/all time)

#### Manager Bot Commands:
- `/staffstats` - View staff statistics from both bots
  - Cross-bot data integration
  - Top performer rankings
  - Combined activity metrics

## Configuration

### Webhook URLs

Both bots need webhook URLs configured for notifications:

**Staff Manager** (`config.yml`):
```yaml
Webhooks:
  BanStaffWebhook: "YOUR_WEBHOOK_URL"
  TicketClaimWebhook: "YOUR_WEBHOOK_URL"
```

**Manager Bot** (`config.yml`):
```yaml
Webhooks:
  TicketClaimWebhook: "YOUR_WEBHOOK_URL"
  BanStaffWebhook: "YOUR_WEBHOOK_URL"
```

### Database Connection

Both bots must connect to the same MongoDB database with the `mrk` database name.

## How It Works

### Ticket Claim Flow:
1. Staff member claims ticket in Manager Bot
2. Manager Bot updates its own statistics
3. Manager Bot notifies Staff Manager via shared database
4. Staff Manager updates staff activity records
5. Optional webhook notification sent

### Ban Detection Flow:
1. Staff Manager monitors BattleMetrics API
2. New ban detected and processed
3. System attempts to identify staff member who issued ban
4. If identified, staff statistics are updated
5. Ban activity is recorded with full details
6. Optional webhook notification sent

### Message Activity Flow:
1. Staff member sends message in Manager Bot
2. Manager Bot checks if user is staff
3. If staff, updates shared statistics
4. Staff Manager can access updated statistics

## Statistics Tracking

### Automatic Resets:
- **Weekly**: Every Monday at midnight
- **Monthly**: First day of each month

### Tracked Metrics:
- **Tickets Claimed**: Only tickets actually claimed by staff
- **Messages Sent**: All messages in Manager Bot channels
- **Bans Issued**: Bans attributed to staff members
- **Voice Activity**: Time spent in voice channels (Staff Manager)
- **In-Game Activity**: Time spent on game servers (Staff Manager)

## Ban Attribution Methods

The system uses multiple methods to identify ban issuers:

1. **Discord Mentions**: Looks for `<@userID>` in ban notes
2. **Name Matching**: Searches for staff names in ban notes/reasons
3. **Signature Patterns**: Recognizes common staff signatures
4. **Future Enhancement**: RCON log integration

## API Endpoints

### Staff Statistics:
- Get individual staff stats
- Get all staff rankings
- Reset weekly/monthly statistics
- Sync staff member data

## Error Handling

- Graceful degradation if one bot is offline
- Retry mechanisms for API failures
- Comprehensive error logging
- Webhook failure handling

## Monitoring

### Logs:
- Ban monitoring activity
- Cross-bot communication
- Statistics updates
- Error conditions

### Webhooks:
- Ticket claim notifications
- Ban activity alerts
- Error notifications

## Future Enhancements

1. **RCON Integration**: Direct server log monitoring for ban attribution
2. **Real-time Sync**: WebSocket communication between bots
3. **Advanced Analytics**: Trend analysis and performance metrics
4. **Mobile Dashboard**: Web interface for staff statistics
5. **Automated Reports**: Weekly/monthly staff performance reports

## Troubleshooting

### Common Issues:

1. **Statistics Not Updating**:
   - Check database connection
   - Verify shared model compatibility
   - Check error logs

2. **Ban Attribution Failing**:
   - Verify BattleMetrics API token
   - Check ban note formatting
   - Review staff member database

3. **Cross-Bot Communication Issues**:
   - Ensure both bots use same database
   - Check webhook configurations
   - Verify staff role IDs match

### Debug Commands:
- Check database connectivity
- Verify staff member records
- Test webhook delivery
- Monitor API rate limits

## Security Considerations

- API tokens stored securely in config files
- Database access restricted to bot applications
- Webhook URLs should be kept private
- Staff data privacy maintained

## Performance

- Ban monitoring: 1-minute intervals
- Database queries optimized with indexes
- Webhook delivery with retry logic
- Memory usage monitoring

This integration provides comprehensive staff activity tracking across both bot systems, enabling better management and recognition of staff performance.
