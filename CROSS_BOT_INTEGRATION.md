# Cross-Bot Integration: Manager <PERSON><PERSON> & Staff Manager

This document outlines the new cross-bot integration features that allow the Manager <PERSON> and Staff Manager to work together for comprehensive staff activity tracking.

## Overview

The integration enables:
- **Ban Tracking**: Staff Manager tracks bans back to staff members using BattleMetrics emails
- **Ticket Claim Tracking**: Staff Manager monitors the existing ticket model for claims by searching admin Discord IDs in the `claimUser` field
- **Message Activity Tracking**: Both bots track staff message activity
- **Shared Statistics**: Unified statistics database accessible by both bots

## Key Implementation Details

### Ban Attribution Method
- Uses **BattleMetrics emails** to match bans to staff members
- Staff members must have their BattleMetrics email configured in the system
- Separate from the existing public ban monitor (which continues to work normally)

### Ticket Claim Tracking Method
- Uses the **existing ticket model** from Manager Bot
- Searches for admin Discord IDs in the `claimUser` field
- Only counts tickets that are actually claimed by registered staff members

## Features Implemented

### 1. Staff Ban Tracking (Staff Manager)

**File**: `Staff Manager/utils/staffBanTracker.js`

- Monitors BattleMetrics API for new bans every 2 minutes
- Uses BattleMetrics emails to identify which staff member issued each ban
- Records detailed ban activity with staff attribution
- Updates staff statistics when bans are attributed

**Key Methods**:
- `startTracking()`: Begins staff ban tracking
- `trackStaffBans()`: Fetches and processes new bans
- `findStaffByBattleMetricsEmail()`: Matches bans to staff using emails
- `recordStaffBan()`: Records ban activity for staff member

### 1a. Public Ban Monitoring (Staff Manager)

**File**: `Staff Manager/utils/banMonitorService.js`

- Continues to handle public ban notifications (unchanged)
- Separate from staff attribution system
- Sends bans to public channels as before

### 2. Ticket Claim Tracking (Staff Manager)

**File**: `Staff Manager/utils/ticketClaimTracker.js`

- Monitors the existing ticket model from Manager Bot every minute
- Searches for admin Discord IDs in the `claimUser` field
- Only counts tickets claimed by registered staff members
- Updates staff statistics when tickets are claimed

**Key Methods**:
- `startTracking()`: Begins ticket claim tracking
- `trackTicketClaims()`: Searches ticket model for new claims
- `processClaimedTicket()`: Processes individual ticket claims
- `syncAllClaimedTickets()`: Manual sync of all historical claims

### 3. Cross-Bot Communication (Both Bots)

**Files**:
- `Staff Manager/utils/crossBotCommunication.js`
- `Manager Bot/utils/staffManagerIntegration.js`

Handles communication between the two bots for:
- Ticket claim notifications
- Message activity tracking
- Ban activity notifications
- Staff statistics synchronization

### 3. Shared Statistics Database

**Files**:
- `Staff Manager/models/sharedStaffStats.js`
- `Manager Bot/models/sharedStaffStats.js`

Unified statistics model tracking:
- **Tickets**: Total/weekly/monthly claimed tickets
- **Messages**: Total/weekly/monthly messages sent
- **Bans**: Total/weekly/monthly bans issued
- **Voice Activity**: Total/weekly/monthly voice minutes
- **In-Game Activity**: Total/weekly/monthly in-game minutes

### 4. Staff Ban Activity Tracking

**File**: `Staff Manager/models/staffBanActivity.js`

Detailed tracking of individual ban actions:
- Staff member who issued the ban
- Player information (Steam ID, name, BM ID)
- Ban details (reason, server, expiration)
- Timestamps and processing status

### 5. Enhanced Commands

#### Staff Manager Commands:
- `/staffactivity` - View comprehensive staff activity statistics
  - Individual staff member stats
  - All staff overview with rankings
  - Timeframe options (week/month/all time)

- `/managestaffemail` - Manage staff BattleMetrics emails for ban attribution
  - Set staff member BattleMetrics emails
  - Remove emails
  - List all staff emails
  - View individual email settings

- `/syncstaffstats` - Manually sync and manage staff statistics
  - Sync all claimed tickets from database
  - Check recent ban tracking status
  - Reset weekly statistics
  - Check system status

#### Manager Bot Commands:
- `/staffstats` - View staff statistics from both bots
  - Cross-bot data integration
  - Top performer rankings
  - Combined activity metrics

## Configuration

### Webhook URLs

Both bots need webhook URLs configured for notifications:

**Staff Manager** (`config.yml`):
```yaml
Webhooks:
  BanStaffWebhook: "YOUR_WEBHOOK_URL"
  TicketClaimWebhook: "YOUR_WEBHOOK_URL"
```

**Manager Bot** (`config.yml`):
```yaml
Webhooks:
  TicketClaimWebhook: "YOUR_WEBHOOK_URL"
  BanStaffWebhook: "YOUR_WEBHOOK_URL"
```

### Database Connection

Both bots must connect to the same MongoDB database with the `mrk` database name.

### Staff Member Setup

1. **Add Staff Members**: Ensure all staff members are registered in the Staff Manager
2. **Set BattleMetrics Emails**: Use `/managestaffemail set` to configure each staff member's BattleMetrics email
3. **Verify Setup**: Use `/managestaffemail list` to confirm all emails are configured

### Initial Sync

1. **Sync Historical Tickets**: Run `/syncstaffstats tickets` to sync all existing claimed tickets
2. **Check System Status**: Run `/syncstaffstats status` to verify all tracking systems are running
3. **Monitor Logs**: Check bot logs for successful initialization of tracking systems

## How It Works

### Ticket Claim Flow:
1. Staff member claims ticket in Manager Bot (sets `claimUser` field)
2. Staff Manager's ticket claim tracker detects the claim via database monitoring
3. System verifies the claimer is a registered staff member
4. Staff statistics are updated in shared database
5. Optional webhook notification sent

### Ban Detection Flow:
1. Staff Manager's ban tracker monitors BattleMetrics API every 2 minutes
2. New ban detected and detailed ban information retrieved
3. System extracts admin email from BattleMetrics ban metadata
4. Email is matched to registered staff member's BattleMetrics email
5. If matched, staff statistics are updated and ban is recorded
6. Optional webhook notification sent with attribution details

### Message Activity Flow:
1. Staff member sends message in Manager Bot
2. Manager Bot checks if user is staff
3. If staff, updates shared statistics
4. Staff Manager can access updated statistics

## Statistics Tracking

### Automatic Resets:
- **Weekly**: Every Monday at midnight
- **Monthly**: First day of each month

### Tracked Metrics:
- **Tickets Claimed**: Only tickets actually claimed by staff
- **Messages Sent**: All messages in Manager Bot channels
- **Bans Issued**: Bans attributed to staff members
- **Voice Activity**: Time spent in voice channels (Staff Manager)
- **In-Game Activity**: Time spent on game servers (Staff Manager)

## Ban Attribution Methods

The system uses BattleMetrics email matching for accurate ban attribution:

1. **Primary Method - BattleMetrics Email**:
   - Extracts admin email from BattleMetrics ban metadata
   - Matches email to staff member's configured BattleMetrics email
   - Most accurate method for attribution

2. **Email Sources Checked**:
   - `ban.attributes.metadata.admin_email`
   - `ban.attributes.admin_email`
   - `ban.relationships.admin.data.attributes.email`
   - Included user data with email field

3. **Requirements**:
   - Staff members must have BattleMetrics emails configured
   - BattleMetrics must include admin email in ban metadata
   - Staff member must be registered and active in system

## API Endpoints

### Staff Statistics:
- Get individual staff stats
- Get all staff rankings
- Reset weekly/monthly statistics
- Sync staff member data

## Error Handling

- Graceful degradation if one bot is offline
- Retry mechanisms for API failures
- Comprehensive error logging
- Webhook failure handling

## Monitoring

### Logs:
- Ban monitoring activity
- Cross-bot communication
- Statistics updates
- Error conditions

### Webhooks:
- Ticket claim notifications
- Ban activity alerts
- Error notifications

## Future Enhancements

1. **RCON Integration**: Direct server log monitoring for ban attribution
2. **Real-time Sync**: WebSocket communication between bots
3. **Advanced Analytics**: Trend analysis and performance metrics
4. **Mobile Dashboard**: Web interface for staff statistics
5. **Automated Reports**: Weekly/monthly staff performance reports

## Troubleshooting

### Common Issues:

1. **Statistics Not Updating**:
   - Check database connection
   - Verify shared model compatibility
   - Check error logs

2. **Ban Attribution Failing**:
   - Verify BattleMetrics API token
   - Check ban note formatting
   - Review staff member database

3. **Cross-Bot Communication Issues**:
   - Ensure both bots use same database
   - Check webhook configurations
   - Verify staff role IDs match

### Debug Commands:
- Check database connectivity
- Verify staff member records
- Test webhook delivery
- Monitor API rate limits

## Security Considerations

- API tokens stored securely in config files
- Database access restricted to bot applications
- Webhook URLs should be kept private
- Staff data privacy maintained

## Performance

- Ban monitoring: 1-minute intervals
- Database queries optimized with indexes
- Webhook delivery with retry logic
- Memory usage monitoring

This integration provides comprehensive staff activity tracking across both bot systems, enabling better management and recognition of staff performance.
