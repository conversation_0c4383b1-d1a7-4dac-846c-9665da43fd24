{"name": "staff-manager", "version": "1.0.0", "description": "Staff management bot for game servers", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.2", "cheerio": "^1.0.0", "discord.js": "^14.14.1", "humanize-duration": "^3.31.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mongoose": "^8.0.3", "node-fetch": "^3.3.2", "rustrcon": "^1.0.2", "ws": "^8.18.2", "yaml": "^2.3.4"}, "devDependencies": {"nodemon": "^3.0.2"}}