const { Client, Collection, GatewayIntentBits, Partials } = require('discord.js');
const fs = require('fs');
const yaml = require('yaml');
const mongoose = require('mongoose');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);

// Create client instance
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.DirectMessages
    ],
    partials: [
        Partials.Channel,
        Partials.Message,
        Partials.User,
        Partials.GuildMember,
        Partials.Reaction
    ]
});

// Global variables
client.commands = new Collection();
client.slashCommands = new Collection();
client.config = {};
client.locals = {};
client.userSelection = {};

// Load configuration
async function loadConfig() {
    try {
        const configFile = await readFile('./config.yml', 'utf8');
        client.config = yaml.parse(configFile);
        console.log('Configuration loaded successfully');

        const localsFile = await readFile('./locals.yml', 'utf8');
        client.locals = yaml.parse(localsFile);
        console.log('Localization loaded successfully');
    } catch (error) {
        console.error('Error loading configuration:', error);
        process.exit(1);
    }
}

// Connect to MongoDB
async function connectToDatabase() {
    try {
        await mongoose.connect(client.config.Database.MongoURI, {
            dbName: 'mrk',
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('Error connecting to MongoDB:', error);
        process.exit(1);
    }
}

// Load event handlers
function loadEvents() {
    const eventFiles = fs.readdirSync('./events').filter(file => file.endsWith('.js'));

    for (const file of eventFiles) {
        const event = require(`./events/${file}`);
        const eventName = file.split('.')[0];

        if (event.once) {
            client.once(eventName, (...args) => event(client, ...args));
        } else {
            client.on(eventName, (...args) => event(client, ...args));
        }

        console.log(`Loaded event: ${eventName}`);
    }
}

// Load slash commands
function loadSlashCommands() {
    // Load commands from slashCommands directory
    const slashCommandFolders = fs.readdirSync('./slashCommands');

    for (const folder of slashCommandFolders) {
        const commandFiles = fs.readdirSync(`./slashCommands/${folder}`).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            const command = require(`./slashCommands/${folder}/${file}`);

            if (!command.enabled) continue;

            client.slashCommands.set(command.data.name, command);
            console.log(`Loaded slash command: ${command.data.name}`);
        }
    }

    // Load commands from commands directory (if it exists)
    if (fs.existsSync('./commands')) {
        const commandFiles = fs.readdirSync('./commands').filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            const command = require(`./commands/${file}`);

            // Skip if command is disabled
            if (command.enabled === false) continue;

            client.slashCommands.set(command.data.name, command);
            console.log(`Loaded command: ${command.data.name}`);
        }
    }
}

// Initialize the bot
async function init() {
    try {
        await loadConfig();
        await connectToDatabase();
        loadEvents();
        loadSlashCommands();

        // Initialize ban monitoring service
        const BanMonitorService = require('./utils/banMonitorService');
        client.banMonitor = new BanMonitorService(client);

        // Initialize cross-bot communication
        const CrossBotCommunication = require('./utils/crossBotCommunication');
        client.crossBot = new CrossBotCommunication(client);

        // Login to Discord
        await client.login(client.config.BotSettings.Token);
    } catch (error) {
        console.error('Error initializing bot:', error);
        process.exit(1);
    }
}

// Start the bot
init();

// Handle process errors
process.on('unhandledRejection', error => {
    console.error('Unhandled promise rejection:', error);
});

process.on('uncaughtException', error => {
    console.error('Uncaught exception:', error);
});

// Graceful shutdown
async function gracefulShutdown(signal) {
    console.log(`Received ${signal}. Shutting down gracefully...`);

    try {
        // Stop ban monitoring
        if (client.banMonitor) {
            console.log('Stopping ban monitoring...');
            client.banMonitor.stopMonitoring();
        }

        // Close RCON connections
        if (client.rconConnections) {
            console.log('Closing RCON connections...');
            for (const [serverName, rcon] of Object.entries(client.rconConnections)) {
                try {
                    await rcon.close();
                    console.log(`Closed RCON connection to ${serverName}`);
                } catch (error) {
                    console.error(`Error closing RCON connection to ${serverName}:`, error);
                }
            }
        }
        console.log('RCON connections closed');

        // Close database connection
        await mongoose.connection.close();
        console.log('Database connection closed');

        // Destroy Discord client
        client.destroy();
        console.log('Discord client destroyed');

        console.log('Shutdown complete');
        process.exit(0);
    } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
    }
}

// Listen for termination signals
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

module.exports = client;
