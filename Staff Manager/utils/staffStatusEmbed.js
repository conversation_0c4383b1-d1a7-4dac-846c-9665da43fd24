const { EmbedBuilder } = require('discord.js');
const StaffMember = require('../models/staffMember');
const moment = require('moment');
const { checkOnlineStaffOnBattleMetrics } = require('./battlemetricsStaffChecker');

/**
 * Update the staff status embeds in the designated channel
 * @param {Object} client - Discord client
 * @returns {Promise<void>}
 */
async function updateStaffStatusEmbed(client) {
    try {
        // Get the status channel
        const statusChannelId = client.config.Channels.statusChannelId ||
                               client.config.StaffManagement.ActivityTracking.OnlineStaffChannelID;

        if (!statusChannelId) {
            console.warn('No status channel ID configured');
            return;
        }

        const guild = client.guilds.cache.get(client.config.BotSettings.StaffGuild || client.config.BotSettings.GuildID);

        if (!guild) {
            console.warn('Guild not found');
            return;
        }

        const statusChannel = guild.channels.cache.get(statusChannelId);

        if (!statusChannel) {
            console.warn('Status channel not found');
            return;
        }

        // Get all active staff members
        const staffMembers = await StaffMember.find({ active: true });

        if (!staffMembers || staffMembers.length === 0) {
            console.warn('No active staff members found');

            // Create empty embed
            const emptyEmbed = new EmbedBuilder()
                .setTitle(client.locals.Staff.StaffStatus.Title || 'Staff Status')
                .setDescription('No active staff members found')
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setTimestamp()
                .setFooter({ text: `${client.locals.Staff.StaffStatus.LastUpdated || 'Last updated'}: ${moment().format('MMMM Do YYYY, h:mm:ss a')}` });

            // Find existing status message or create new one
            const messages = await statusChannel.messages.fetch({ limit: 10 });
            const statusMessage = messages.find(m => m.author.id === client.user.id && m.embeds.length > 0);

            if (statusMessage) {
                await statusMessage.edit({ embeds: [emptyEmbed] });
            } else {
                await statusChannel.send({ embeds: [emptyEmbed] });
            }

            return;
        }

        // Create embeds
        const onlineEmbed = new EmbedBuilder()
            .setTitle(client.locals.Staff.StaffStatus.Title || 'Online Staff Status')
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
            .setFooter({ text: `${client.locals.Staff.StaffStatus.LastUpdated || 'Last updated'}: ${moment().format('MMMM Do YYYY, h:mm:ss a')}` });

        const offlineEmbed = new EmbedBuilder()
            .setTitle(client.locals.Staff.StaffStatus.OfflineStaff || 'Offline Staff')
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
            .setFooter({ text: `${client.locals.Staff.StaffStatus.LastUpdated || 'Last updated'}: ${moment().format('MMMM Do YYYY, h:mm:ss a')}` });

        // Track online staff
        const onlineStaff = [];
        const offlineStaff = [...staffMembers];

        // Check BattleMetrics for online staff
        console.log('Checking BattleMetrics for online staff...');
        const { onlineStaffByServer, staffOnline } = await checkOnlineStaffOnBattleMetrics(staffMembers, client.config.Servers);

        console.log('BattleMetrics results:', JSON.stringify({
            staffOnlineCount: staffOnline.length,
            onlineStaffByServer: Object.keys(onlineStaffByServer).map(server => ({
                server,
                count: onlineStaffByServer[server].length,
                staff: onlineStaffByServer[server].map(s => s.name)
            }))
        }));

        // Add staff found on BattleMetrics to online staff list
        for (const staff of staffOnline) {
            console.log(`Adding staff member from BattleMetrics to online list: ${staff.name} (${staff.id})`);
            if (!onlineStaff.some(s => s.id === staff.id)) {
                onlineStaff.push(staff);
                const index = offlineStaff.findIndex(s => s._id === staff.id);
                if (index !== -1) {
                    offlineStaff.splice(index, 1);
                }
            }
        }

        console.log(`Found ${staffOnline.length} staff members online on BattleMetrics`);

        // Check each server for online staff
        for (const server of client.config.Servers) {
            try {
                let isConnected = false;

                if (!client.rconConnections) {
                    console.warn(`RCON connections not initialized yet.`);
                    onlineEmbed.addFields({
                        name: `🔴 ${server.long_name || server.name}`,
                        value: 'RCON connections not initialized yet.',
                        inline: false
                    });
                    continue;
                }

                const rcon = client.rconConnections[server.name];
                console.log(`Checking connection for ${server.name}:`, {
                    hasRcon: !!rcon,
                    isConnected: rcon ? rcon.connected : false
                });

                if (rcon && rcon.connected) {
                    isConnected = true;
                    console.log(`Connection to ${server.name} is active.`);
                } else {
                    console.warn(`No active connection for ${server.name}.`);
                    if (rcon) {
                        console.log(`Attempting to reconnect to ${server.name}...`);
                        try {
                            rcon.connect();
                        } catch (error) {
                            console.error(`Error reconnecting to ${server.name}:`, error);
                        }
                    }
                }

                if (!isConnected) {
                    console.warn(`No active connection for ${server.name} after retries.`);

                    // Check if we have BattleMetrics data for this server
                    const bmStaffOnServer = onlineStaffByServer[server.name] || [];
                    if (bmStaffOnServer.length > 0) {
                        console.log(`Found ${bmStaffOnServer.length} staff members on ${server.name} from BattleMetrics despite RCON being offline`);

                        let staffInfo = '';
                        for (const staff of bmStaffOnServer) {
                            const playTimeFormatted = formatPlayTime(staff.playTime);
                            if (staff.flag) {
                                staffInfo += `${staff.flag} • <@${staff.id}> - *${playTimeFormatted}*\n`;
                            } else {
                                staffInfo += `• <@${staff.id}> - *${playTimeFormatted}*\n`;
                            }

                            // Add to online staff list if not already there
                            if (!onlineStaff.some(s => s.id === staff.id)) {
                                onlineStaff.push({
                                    id: staff.id,
                                    name: staff.name,
                                    server: server.name,
                                    playTime: staff.playTime || 0,
                                    region: staff.region || 'Unknown',
                                    flag: staff.flag
                                });

                                // Remove from offline staff
                                const index = offlineStaff.findIndex(s => s._id === staff.id);
                                if (index !== -1) {
                                    offlineStaff.splice(index, 1);
                                }
                            }
                        }

                        onlineEmbed.addFields({
                            name: `🟢 ${server.long_name || server.name}`,
                            value: staffInfo,
                            inline: false
                        });
                    } else {
                        onlineEmbed.addFields({
                            name: `🔴 ${server.long_name || server.name}`,
                            value: client.locals.Staff.StaffStatus.ServerOffline || 'Server is currently offline.',
                            inline: false
                        });
                    }
                    continue;
                }

                console.log(`Sending status command to ${server.name}...`);
                let statusMessage;
                let staffOnServer = [];

                try {
                    console.log(`Sending status command to ${server.name} with 5 retries and 30s timeout...`);
                    const identifier = `status_${Math.floor(Math.random() * 900000) + 100000}`;
                    const response = await rcon.runCommand(
                        { ip: rcon.ip, rcon_port: rcon.port, rcon_password: rcon.password, name: rcon.nickname },
                        'status',
                        identifier,
                        5
                    );

                    if (!response) {
                        console.error(`No response from ${server.name} for status command.`);
                        throw new Error('No response from server');
                    }

                    console.log(`Received response from ${server.name}:`, response.substring(0, 100) + (response.length > 100 ? '...' : ''));

                    let responseData;
                    try {
                        responseData = JSON.parse(response);
                    } catch (parseError) {
                        console.error(`Error parsing response from ${server.name}:`, parseError);
                        console.error(`Raw response:`, response);
                        throw new Error(`Error parsing response: ${parseError.message}`);
                    }

                    statusMessage = responseData.Message;

                    if (!statusMessage) {
                        console.error(`No message in response from ${server.name}.`);
                        throw new Error('No message in response');
                    }

                    console.log(`Successfully got status from ${server.name}`);
                    isConnected = true;

                    const players = parsePlayerList(statusMessage);
                    console.log(`Parsed ${players.length} players from status response for ${server.name}`);

                    for (const player of players) {
                        const steamId = player.steamId;
                        const staffMember = staffMembers.find(s => s.steam_id === steamId);

                        if (staffMember) {
                            staffOnServer.push({
                                id: staffMember._id,
                                name: staffMember.discord_name,
                                playTime: player.playTime || 0,
                                region: staffMember.region_info?.region || 'Unknown',
                                flag: getRegionFlag(staffMember.region_info),
                                source: 'RCON'
                            });

                            const index = offlineStaff.findIndex(s => s._id === staffMember._id);
                            if (index !== -1) {
                                offlineStaff.splice(index, 1);
                            }

                            if (!onlineStaff.some(s => s.id === staffMember._id)) {
                                onlineStaff.push({
                                    id: staffMember._id,
                                    name: staffMember.discord_name,
                                    server: server.name,
                                    playTime: player.playTime || 0,
                                    region: staffMember.region_info?.region || 'Unknown',
                                    flag: getRegionFlag(staffMember.region_info),
                                    source: 'RCON'
                                });
                            }
                        }
                    }

                    if (onlineStaffByServer[server.name] && onlineStaffByServer[server.name].length > 0) {
                        for (const bmStaff of onlineStaffByServer[server.name]) {
                            if (!staffOnServer.some(s => s.id === bmStaff.id)) {
                                staffOnServer.push(bmStaff);
                                const index = offlineStaff.findIndex(s => s._id === bmStaff.id);
                                if (index !== -1) {
                                    offlineStaff.splice(index, 1);
                                }

                                if (!onlineStaff.some(s => s.id === bmStaff.id)) {
                                    onlineStaff.push({
                                        id: bmStaff.id,
                                        name: bmStaff.name,
                                        server: server.name,
                                        playTime: bmStaff.playTime || 0,
                                        region: bmStaff.region || 'Unknown',
                                        flag: bmStaff.flag,
                                        source: 'BattleMetrics'
                                    });
                                }
                            }
                        }
                    }

                    let staffInfo = '';
                    if (staffOnServer.length > 0) {
                        for (const staff of staffOnServer) {
                            const playTimeFormatted = formatPlayTime(staff.playTime);
                            // Don't show the source to avoid visual clutter
                            const source = ''; // staff.source ? ` [${staff.source}]` : '';
                            if (staff.flag) {
                                staffInfo += `${staff.flag} • <@${staff.id}> - *${playTimeFormatted}*${source}\n`;
                            } else {
                                staffInfo += `• <@${staff.id}> - *${playTimeFormatted}*${source}\n`;
                            }
                        }
                    } else {
                        // Also check BattleMetrics data for this server
                        const bmStaffOnServer = onlineStaffByServer[server.name] || [];
                        if (bmStaffOnServer.length > 0) {
                            console.log(`Found ${bmStaffOnServer.length} staff members on ${server.name} from BattleMetrics`);
                            console.log('BattleMetrics staff data:', JSON.stringify(bmStaffOnServer));

                            for (const staff of bmStaffOnServer) {
                                console.log(`Adding staff member to embed: ${staff.name} (${staff.id})`);
                                const playTimeFormatted = formatPlayTime(staff.playTime);
                                if (staff.flag) {
                                    staffInfo += `${staff.flag} • <@${staff.id}> - *${playTimeFormatted}* [BM]\n`;
                                } else {
                                    staffInfo += `• <@${staff.id}> - *${playTimeFormatted}* [BM]\n`;
                                }
                            }
                        } else {
                            staffInfo = client.locals.Staff.StaffStatus.NoStaffOnline || 'No staff members are currently online.';
                        }
                    }

                    onlineEmbed.addFields({
                        name: `🟢 ${server.long_name || server.name}`,
                        value: staffInfo,
                        inline: false
                    });

                } catch (error) {
                    console.error(`Error getting player list from ${server.name}:`, error);
                    onlineEmbed.addFields({
                        name: `🔴 ${server.long_name || server.name}`,
                        value: `Failed to get player list: ${error.message}`,
                        inline: false
                    });
                    continue;
                }
            } catch (error) {
                console.error(`Error checking server ${server.name}:`, error);
                onlineEmbed.addFields({
                    name: `🔴 ${server.long_name || server.name}`,
                    value: `Error: ${error.message}`,
                    inline: false
                });
            }
        }

        // Add offline staff to offline embed
        let offlineStaffInfo = '';
        if (offlineStaff.length > 0) {
            for (const staff of offlineStaff) {
                const flag = getRegionFlag(staff.region_info);
                if (flag) {
                    offlineStaffInfo += `${flag} • <@${staff._id}>\n`;
                } else {
                    offlineStaffInfo += `• <@${staff._id}>\n`;
                }
            }
        } else {
            offlineStaffInfo = 'All staff members are online!';
        }

        offlineEmbed.setDescription(offlineStaffInfo);
        offlineEmbed.setImage(client.config.StaffManagement.Images.StaffStatusEmbed)

        // Set summary in online embed
        onlineEmbed.setDescription(
            `**${client.locals.Staff.StaffStatus.TotalOnline || 'Total Online'}:** ${onlineStaff.length}\n` +
            `**${client.locals.Staff.StaffStatus.TotalOffline || 'Total Offline'}:** ${offlineStaff.length}`
        );

        // Find existing status message or create new one
        const messages = await statusChannel.messages.fetch({ limit: 10 });
        const statusMessage = messages.find(m => m.author.id === client.user.id && m.embeds.length > 0);

        if (statusMessage) {
            await statusMessage.edit({ embeds: [onlineEmbed, offlineEmbed] });
        } else {
            await statusChannel.send({ embeds: [onlineEmbed, offlineEmbed] });
        }
    } catch (error) {
        console.error('Error updating staff status embeds:', error);
    }
}

/**
 * Parse player list from status command
 * @param {string} statusMessage - Status command output
 * @returns {Array} - Array of player objects
 */
function parsePlayerList(statusMessage) {
    const players = [];

    try {
        console.log('Parsing player list from status message:');
        console.log(statusMessage);

        // Split status message into lines
        const lines = statusMessage.split('\n');
        console.log(`Status message has ${lines.length} lines`);

        // Find player list section
        let playerListStarted = false;

        for (const line of lines) {
            if (line.includes('steamid')) {
                playerListStarted = true;
                console.log('Found player list section starting with:', line);
                continue;
            }

            if (!playerListStarted) continue;

            console.log('Processing player line:', line);

            const match = line.match(/\d+\s+"([^"]+)"\s+(\d+)\s+(\d+:\d+:\d+)/);

            if (match) {
                const playerName = match[1];
                const steamId = match[2];
                const playTimeStr = match[3];

                console.log(`Found player: ${playerName}, SteamID: ${steamId}, PlayTime: ${playTimeStr}`);

                const playTime = convertPlayTimeToMinutes(playTimeStr);

                players.push({
                    name: playerName,
                    steamId,
                    playTime
                });
            } else if (line.trim()) {
                console.log(`Line didn't match player format: ${line}`);
            }
        }

        console.log(`Parsed ${players.length} players from status message`);
    } catch (error) {
        console.error('Error parsing player list:', error);
        console.error(error.stack);
    }

    return players;
}

/**
 * Convert play time string to minutes
 * @param {string} playTimeStr - Play time string (HH:MM:SS)
 * @returns {number} - Play time in minutes
 */
function convertPlayTimeToMinutes(playTimeStr) {
    try {
        const [hours, minutes, seconds] = playTimeStr.split(':').map(Number);
        return hours * 60 + minutes + seconds / 60;
    } catch (error) {
        console.error('Error converting play time:', error);
        return 0;
    }
}

/**
 * Format play time in minutes to a readable string
 * @param {number} minutes - Play time in minutes
 * @returns {string} - Formatted play time
 */
function formatPlayTime(minutes) {
    try {
        const hours = Math.floor(minutes / 60);
        const mins = Math.floor(minutes % 60);

        if (hours > 0) {
            return `${hours}h ${mins}m`;
        } else {
            return `${mins}m`;
        }
    } catch (error) {
        console.error('Error formatting play time:', error);
        return '0m';
    }
}

/**
 * Get flag emoji for region
 * @param {Object} regionInfo - Region information object
 * @returns {string} - Flag emoji
 */
function getRegionFlag(regionInfo) {
    if (regionInfo && regionInfo.flag) {
        return regionInfo.flag;
    }

    const region = regionInfo?.region;
    if (!region) return '';

    const regionFlags = {
        'na': '🇺🇸',
        'eu': '🇪🇺',
        'au': '🇦🇺',
        'sa': '🇧🇷',
        'as': '🇯🇵',
        'af': '🇿🇦',
        'uk': '🇬🇧',
        'ca': '🇨🇦',
        'us': '🇺🇸',
        'gb': '🇬🇧',
        'de': '🇩🇪',
        'fr': '🇫🇷',
        'es': '🇪🇸',
        'it': '🇮🇹',
        'ru': '🇷🇺',
        'cn': '🇨🇳',
        'jp': '🇯🇵',
        'kr': '🇰🇷',
        'br': '🇧🇷',
        'mx': '🇲🇽',
        'au': '🇦🇺',
        'nz': '🇳🇿',
        'ar': '🇦🇷',
        'at': '🇾🇹',
        'be': '🇧🇪',
        'ch': '🇨🇭',
        'cl': '🇨🇱',
        'co': '🇨🇴',
        'dk': '🇩🇰',
        'fi': '🇫🇮',
        'gr': '🇬🇷',
        'hu': '🇭🇺',
        'id': '🇮🇩',
        'ie': '🇮🇪',
        'il': '🇮🇱',
        'in': '🇮🇳',
        'nl': '🇳🇱',
        'no': '🇳🇴',
        'pe': '🇵🇪',
        'ph': '🇵🇭',
        'pl': '🇵🇱',
        'pt': '🇵🇹',
        'ro': '🇷🇴',
        'se': '🇸🇪',
        'sg': '🇸🇬',
        'th': '🇹🇭',
        'tr': '🇹🇷',
        'ua': '🇺🇦',
        'vn': '🇻🇳',
        'za': '🇿🇦'
    };

    return regionFlags[region.toLowerCase()] || '';
}

module.exports = { updateStaffStatusEmbed };