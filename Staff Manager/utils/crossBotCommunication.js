const SharedStaffStats = require('../models/sharedStaffStats');
const StaffActivity = require('../models/staffActivity');
const StaffMember = require('../models/staffMember');
const { WebhookClient, EmbedBuilder } = require('discord.js');

/**
 * Utility class for handling communication and data sharing between Manager <PERSON> and Staff Manager
 */
class CrossBotCommunication {
    constructor(client) {
        this.client = client;
    }

    /**
     * Handle ticket claim notification from Manager <PERSON><PERSON>
     * This should be called when a ticket is claimed in Manager Bo<PERSON>
     */
    async handleTicketClaim(staffDiscordId, ticketData) {
        try {
            console.log(`Processing ticket claim for staff member: ${staffDiscordId}`);
            
            // Verify the staff member exists
            const staffMember = await StaffMember.findById(staffDiscordId);
            if (!staffMember) {
                console.log(`Staff member ${staffDiscordId} not found in Staff Manager database`);
                return false;
            }

            // Update shared statistics
            await SharedStaffStats.updateTicketStats(staffDiscordId);
            
            // Update legacy staff activity model
            await StaffActivity.findOneAndUpdate(
                { _id: staffDiscordId },
                {
                    $inc: {
                        'tickets.total': 1,
                        'tickets.weekly': 1
                    },
                    $set: {
                        lastUpdated: new Date()
                    }
                },
                { upsert: true }
            );

            // Send notification if configured
            await this.sendTicketClaimNotification(staffMember, ticketData);

            console.log(`Successfully processed ticket claim for ${staffMember.discord_name}`);
            return true;

        } catch (error) {
            console.error('Error handling ticket claim:', error);
            return false;
        }
    }

    /**
     * Handle message activity from Manager Bot
     * This should be called when staff members send messages in Manager Bot
     */
    async handleMessageActivity(staffDiscordId, messageCount = 1, channelType = 'general') {
        try {
            // Verify the staff member exists
            const staffMember = await StaffMember.findById(staffDiscordId);
            if (!staffMember) {
                return false;
            }

            // Update shared statistics
            await SharedStaffStats.updateMessageStats(staffDiscordId, messageCount);
            
            // Update legacy staff activity model
            await StaffActivity.findOneAndUpdate(
                { _id: staffDiscordId },
                {
                    $inc: {
                        'messages.total': messageCount,
                        'messages.weekly': messageCount
                    },
                    $set: {
                        lastUpdated: new Date()
                    }
                },
                { upsert: true }
            );

            return true;

        } catch (error) {
            console.error('Error handling message activity:', error);
            return false;
        }
    }

    /**
     * Handle ban activity notification from Manager Bot
     * This should be called when a ban is detected in Manager Bot
     */
    async handleBanActivity(staffDiscordId, banData) {
        try {
            console.log(`Processing ban activity for staff member: ${staffDiscordId}`);
            
            // Verify the staff member exists
            const staffMember = await StaffMember.findById(staffDiscordId);
            if (!staffMember) {
                console.log(`Staff member ${staffDiscordId} not found in Staff Manager database`);
                return false;
            }

            // Update shared statistics
            await SharedStaffStats.updateBanStats(staffDiscordId);
            
            // Update legacy staff activity model
            await StaffActivity.findOneAndUpdate(
                { _id: staffDiscordId },
                {
                    $inc: {
                        'bans.total': 1,
                        'bans.weekly': 1
                    },
                    $set: {
                        lastUpdated: new Date()
                    }
                },
                { upsert: true }
            );

            console.log(`Successfully processed ban activity for ${staffMember.discord_name}`);
            return true;

        } catch (error) {
            console.error('Error handling ban activity:', error);
            return false;
        }
    }

    /**
     * Get comprehensive staff statistics
     */
    async getStaffStatistics(staffDiscordId, timeframe = 'all') {
        try {
            const staffMember = await StaffMember.findById(staffDiscordId);
            if (!staffMember) {
                return null;
            }

            const sharedStats = await SharedStaffStats.findOne({ staffDiscordId });
            const legacyStats = await StaffActivity.findById(staffDiscordId);

            // Combine statistics from both sources
            const stats = {
                staffMember: staffMember,
                tickets: {
                    total: sharedStats?.tickets?.totalClaimed || legacyStats?.tickets?.total || 0,
                    weekly: sharedStats?.tickets?.weeklyClaimed || legacyStats?.tickets?.weekly || 0,
                    monthly: sharedStats?.tickets?.monthlyClaimed || 0,
                    lastClaimed: sharedStats?.tickets?.lastTicketClaimed || null
                },
                messages: {
                    total: sharedStats?.messages?.total || legacyStats?.messages?.total || 0,
                    weekly: sharedStats?.messages?.weekly || legacyStats?.messages?.weekly || 0,
                    monthly: sharedStats?.messages?.monthly || 0,
                    lastMessage: sharedStats?.messages?.lastMessage || null
                },
                bans: {
                    total: sharedStats?.bans?.total || legacyStats?.bans?.total || 0,
                    weekly: sharedStats?.bans?.weekly || legacyStats?.bans?.weekly || 0,
                    monthly: sharedStats?.bans?.monthly || 0,
                    lastBan: sharedStats?.bans?.lastBan || null
                },
                voice: {
                    totalMinutes: sharedStats?.voice?.totalMinutes || legacyStats?.voice?.total || 0,
                    weeklyMinutes: sharedStats?.voice?.weeklyMinutes || legacyStats?.voice?.weekly || 0,
                    monthlyMinutes: sharedStats?.voice?.monthlyMinutes || 0,
                    lastActivity: sharedStats?.voice?.lastVoiceActivity || null
                },
                ingame: {
                    totalMinutes: sharedStats?.ingame?.totalMinutes || legacyStats?.ingame?.total || 0,
                    weeklyMinutes: sharedStats?.ingame?.weeklyMinutes || legacyStats?.ingame?.weekly || 0,
                    monthlyMinutes: sharedStats?.ingame?.monthlyMinutes || 0,
                    lastActivity: sharedStats?.ingame?.lastIngameActivity || null
                },
                lastUpdated: sharedStats?.lastUpdated || legacyStats?.lastUpdated || null
            };

            return stats;

        } catch (error) {
            console.error('Error getting staff statistics:', error);
            return null;
        }
    }

    /**
     * Send ticket claim notification
     */
    async sendTicketClaimNotification(staffMember, ticketData) {
        try {
            const config = this.client.config;
            const webhookUrl = config.Webhooks?.TicketClaimWebhook;
            
            if (!webhookUrl) {
                return; // No webhook configured
            }

            const embed = new EmbedBuilder()
                .setTitle('🎫 Ticket Claimed')
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffMember._id}>`, inline: true },
                    { name: 'Ticket Type', value: ticketData.ticketType || 'Unknown', inline: true },
                    { name: 'Channel', value: `<#${ticketData.channelId}>`, inline: true }
                ])
                .setTimestamp();

            const webhook = new WebhookClient({ url: webhookUrl });
            await webhook.send({ embeds: [embed] });

        } catch (error) {
            console.error('Error sending ticket claim notification:', error);
        }
    }

    /**
     * Sync staff member data between bots
     */
    async syncStaffMember(discordId, discordName, steamId) {
        try {
            // Update or create staff member record
            const staffMember = await StaffMember.findOneAndUpdate(
                { _id: discordId },
                {
                    discord_name: discordName,
                    steam_id: steamId,
                    updated_at: new Date()
                },
                { upsert: true, new: true }
            );

            // Ensure activity records exist
            await StaffActivity.findOneAndUpdate(
                { _id: discordId },
                {
                    $setOnInsert: {
                        messages: { weekly: 0, total: 0 },
                        voice: { weekly: 0, total: 0 },
                        ingame: { weekly: 0, total: 0 },
                        tickets: { weekly: 0, total: 0 },
                        bans: { weekly: 0, total: 0 }
                    }
                },
                { upsert: true }
            );

            await SharedStaffStats.findOneAndUpdate(
                { staffDiscordId: discordId },
                {
                    $setOnInsert: {
                        tickets: { totalClaimed: 0, weeklyClaimed: 0, monthlyClaimed: 0 },
                        messages: { total: 0, weekly: 0, monthly: 0 },
                        bans: { total: 0, weekly: 0, monthly: 0 },
                        voice: { totalMinutes: 0, weeklyMinutes: 0, monthlyMinutes: 0 },
                        ingame: { totalMinutes: 0, weeklyMinutes: 0, monthlyMinutes: 0 }
                    }
                },
                { upsert: true }
            );

            return staffMember;

        } catch (error) {
            console.error('Error syncing staff member:', error);
            return null;
        }
    }

    /**
     * Reset weekly statistics for all staff members
     */
    async resetWeeklyStats() {
        try {
            console.log('Resetting weekly statistics...');
            
            // Reset shared stats
            await SharedStaffStats.updateMany(
                {},
                {
                    $set: {
                        'tickets.weeklyClaimed': 0,
                        'messages.weekly': 0,
                        'bans.weekly': 0,
                        'voice.weeklyMinutes': 0,
                        'ingame.weeklyMinutes': 0,
                        weeklyResetDate: new Date()
                    }
                }
            );

            // Reset legacy stats
            await StaffActivity.updateMany(
                {},
                {
                    $set: {
                        'tickets.weekly': 0,
                        'messages.weekly': 0,
                        'bans.weekly': 0,
                        'voice.weekly': 0,
                        'ingame.weekly': 0,
                        lastUpdated: new Date()
                    }
                }
            );

            console.log('Weekly statistics reset completed');
            return true;

        } catch (error) {
            console.error('Error resetting weekly stats:', error);
            return false;
        }
    }

    /**
     * Get all staff members with their current statistics
     */
    async getAllStaffStats() {
        try {
            const staffMembers = await StaffMember.find({ active: true });
            const statsPromises = staffMembers.map(staff => this.getStaffStatistics(staff._id));
            const allStats = await Promise.all(statsPromises);
            
            return allStats.filter(stats => stats !== null);

        } catch (error) {
            console.error('Error getting all staff stats:', error);
            return [];
        }
    }
}

module.exports = CrossBotCommunication;
