const StaffMember = require('../models/staffMember');
const SharedStaffStats = require('../models/sharedStaffStats');
const StaffActivity = require('../models/staffActivity');
const { EmbedBuilder, WebhookClient } = require('discord.js');

/**
 * Ticket Claim Tracker - Tracks ticket claims using the existing ticket model
 * Searches for admin Discord IDs in the claimUser field
 */
class TicketClaimTracker {
    constructor(client) {
        this.client = client;
        this.isTracking = false;
        this.trackingInterval = null;
        this.lastChecked = new Date();
        this.checkInterval = 60000; // 1 minute
        this.processedTickets = new Set(); // Track processed tickets to avoid duplicates
    }

    /**
     * Start tracking ticket claims
     */
    async startTracking() {
        if (this.isTracking) {
            console.log('Ticket claim tracking is already running');
            return;
        }

        console.log('Starting ticket claim tracking...');
        this.isTracking = true;
        
        // Initial check
        await this.trackTicketClaims();
        
        // Set up interval
        this.trackingInterval = setInterval(async () => {
            try {
                await this.trackTicketClaims();
            } catch (error) {
                console.error('Error in ticket claim tracking interval:', error);
            }
        }, this.checkInterval);
        
        console.log(`Ticket claim tracking started with ${this.checkInterval / 1000}s interval`);
    }

    /**
     * Stop tracking
     */
    stopTracking() {
        if (this.trackingInterval) {
            clearInterval(this.trackingInterval);
            this.trackingInterval = null;
        }
        this.isTracking = false;
        console.log('Ticket claim tracking stopped');
    }

    /**
     * Track ticket claims by searching the existing ticket model
     */
    async trackTicketClaims() {
        try {
            // Import the ticket model from Manager Bot
            // We need to use the same database connection
            const mongoose = require('mongoose');
            
            // Define the ticket schema if not already defined
            let TicketModel;
            try {
                TicketModel = mongoose.model('ticket');
            } catch (error) {
                // Model doesn't exist, create it
                const ticketSchema = new mongoose.Schema({
                    guildID: String,
                    sourceGuildID: String,
                    channelID: String,
                    open: Boolean,
                    userID: String,
                    username: String,
                    avatar: String,
                    ticketType: String,
                    button: String,
                    msgID: String,
                    claimed: Boolean,
                    claimUser: String, // This is what we're looking for
                    messages: Number,
                    lastMessageSent: Date,
                    lastMessageSentUser: String,
                    status: String,
                    closeUserID: String,
                    archiveMsgID: String,
                    questions: [{
                        customId: String,
                        required: Boolean,
                        question: String,
                        style: String,
                        response: String,
                    }],
                    ticketCreationDate: Date,
                    closedAt: Date,
                    identifier: String,
                    closeReason: String,
                    closeNotificationTime: Number,
                    closeNotificationMsgID: String,
                    closeNotificationUserID: String,
                    transcriptID: String,
                    webhookToken: String,
                    webhookID: String,
                }, {
                    timestamps: true,
                });
                
                TicketModel = mongoose.model('ticket', ticketSchema);
            }

            // Find tickets that are claimed and have been updated since last check
            const query = {
                claimed: true,
                claimUser: { $exists: true, $ne: null, $ne: '' },
                updatedAt: { $gte: this.lastChecked }
            };

            const claimedTickets = await TicketModel.find(query).sort({ updatedAt: -1 });

            console.log(`Found ${claimedTickets.length} claimed tickets since last check`);

            // Process each claimed ticket
            for (const ticket of claimedTickets) {
                await this.processClaimedTicket(ticket);
            }

            this.lastChecked = new Date();

        } catch (error) {
            console.error('Error tracking ticket claims:', error);
        }
    }

    /**
     * Process a claimed ticket and update staff statistics
     */
    async processClaimedTicket(ticket) {
        try {
            const claimUserId = ticket.claimUser;
            
            // Skip if we've already processed this ticket
            const ticketKey = `${ticket._id}_${claimUserId}`;
            if (this.processedTickets.has(ticketKey)) {
                return;
            }

            // Check if the claim user is a staff member
            const staffMember = await StaffMember.findById(claimUserId);
            if (!staffMember) {
                console.log(`Claim user ${claimUserId} is not a registered staff member`);
                return;
            }

            console.log(`Processing ticket claim: ${ticket.channelID} claimed by ${staffMember.discord_name}`);

            // Update staff statistics
            await this.updateStaffTicketStats(claimUserId, ticket);

            // Send notification
            await this.sendTicketClaimNotification(staffMember, ticket);

            // Mark as processed
            this.processedTickets.add(ticketKey);

            // Clean up old processed tickets (keep only last 1000)
            if (this.processedTickets.size > 1000) {
                const ticketsArray = Array.from(this.processedTickets);
                this.processedTickets = new Set(ticketsArray.slice(-500));
            }

        } catch (error) {
            console.error('Error processing claimed ticket:', error);
        }
    }

    /**
     * Update staff statistics for ticket claims
     */
    async updateStaffTicketStats(staffDiscordId, ticket) {
        try {
            // Update shared stats
            await SharedStaffStats.updateTicketStats(staffDiscordId);
            
            // Update legacy staff activity model
            await StaffActivity.findOneAndUpdate(
                { _id: staffDiscordId },
                {
                    $inc: {
                        'tickets.total': 1,
                        'tickets.weekly': 1
                    },
                    $set: {
                        lastUpdated: new Date()
                    }
                },
                { upsert: true }
            );

            console.log(`Updated ticket stats for staff member ${staffDiscordId}`);

        } catch (error) {
            console.error('Error updating staff ticket stats:', error);
        }
    }

    /**
     * Send notification about ticket claim
     */
    async sendTicketClaimNotification(staffMember, ticket) {
        try {
            const config = this.client.config;
            const webhookUrl = config.Webhooks?.TicketClaimWebhook;
            
            if (!webhookUrl) {
                return; // No webhook configured
            }

            const embed = new EmbedBuilder()
                .setTitle('🎫 Ticket Claim Tracked')
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffMember._id}>`, inline: true },
                    { name: 'Ticket Type', value: ticket.ticketType || 'Unknown', inline: true },
                    { name: 'Channel', value: `<#${ticket.channelID}>`, inline: true },
                    { name: 'Ticket Creator', value: `<@${ticket.userID}>`, inline: true },
                    { name: 'Created', value: ticket.ticketCreationDate ? new Date(ticket.ticketCreationDate).toLocaleDateString() : 'Unknown', inline: true },
                    { name: 'Messages', value: `${ticket.messages || 0}`, inline: true }
                ])
                .setTimestamp()
                .setFooter({ text: 'Ticket Claim Tracking System' });

            const webhook = new WebhookClient({ url: webhookUrl });
            await webhook.send({ embeds: [embed] });

        } catch (error) {
            console.error('Error sending ticket claim notification:', error);
        }
    }

    /**
     * Get ticket claim statistics for a staff member
     */
    async getStaffTicketStats(staffDiscordId, timeframe = 'all') {
        try {
            const mongoose = require('mongoose');
            let TicketModel;
            
            try {
                TicketModel = mongoose.model('ticket');
            } catch (error) {
                return { total: 0, tickets: [] };
            }

            const query = {
                claimed: true,
                claimUser: staffDiscordId
            };
            
            if (timeframe === 'week') {
                const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                query.updatedAt = { $gte: weekAgo };
            } else if (timeframe === 'month') {
                const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                query.updatedAt = { $gte: monthAgo };
            }

            const tickets = await TicketModel.find(query).sort({ updatedAt: -1 });
            
            return {
                total: tickets.length,
                tickets: tickets
            };

        } catch (error) {
            console.error('Error getting staff ticket stats:', error);
            return { total: 0, tickets: [] };
        }
    }

    /**
     * Get all staff ticket statistics
     */
    async getAllStaffTicketStats() {
        try {
            const staffMembers = await StaffMember.find({ active: true });
            const statsPromises = staffMembers.map(async (staff) => {
                const stats = await this.getStaffTicketStats(staff._id);
                return {
                    staffId: staff._id,
                    staffName: staff.discord_name,
                    totalTickets: stats.total,
                    recentTickets: stats.tickets.slice(0, 5)
                };
            });

            const allStats = await Promise.all(statsPromises);
            return allStats.filter(stats => stats.totalTickets > 0);

        } catch (error) {
            console.error('Error getting all staff ticket stats:', error);
            return [];
        }
    }

    /**
     * Manual sync - check all claimed tickets and update stats
     */
    async syncAllClaimedTickets() {
        try {
            console.log('Starting manual sync of all claimed tickets...');
            
            const mongoose = require('mongoose');
            let TicketModel;
            
            try {
                TicketModel = mongoose.model('ticket');
            } catch (error) {
                console.log('Ticket model not available for sync');
                return;
            }

            // Find all claimed tickets
            const claimedTickets = await TicketModel.find({
                claimed: true,
                claimUser: { $exists: true, $ne: null, $ne: '' }
            });

            console.log(`Found ${claimedTickets.length} total claimed tickets to sync`);

            let processed = 0;
            for (const ticket of claimedTickets) {
                const staffMember = await StaffMember.findById(ticket.claimUser);
                if (staffMember) {
                    // Don't send notifications during sync
                    await this.updateStaffTicketStats(ticket.claimUser, ticket);
                    processed++;
                }
            }

            console.log(`Synced ${processed} claimed tickets for staff members`);

        } catch (error) {
            console.error('Error syncing claimed tickets:', error);
        }
    }
}

module.exports = TicketClaimTracker;
