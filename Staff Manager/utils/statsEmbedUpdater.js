const { EmbedBuilder } = require('discord.js');
const SharedStaffStats = require('../models/sharedStaffStats');
const StaffBanActivity = require('../models/staffBanActivity');
const StaffMember = require('../models/staffMember');

/**
 * Stats Embed Updater - Creates and updates comprehensive staff statistics embeds
 */
class StatsEmbedUpdater {
    constructor(client) {
        this.client = client;
        this.isUpdating = false;
        this.updateInterval = null;
        this.updateFrequency = 300000; // 5 minutes
        this.statsChannelId = null;
        this.statsMessageId = null;
    }

    /**
     * Initialize the stats embed updater
     */
    async initialize() {
        try {
            // Get stats channel from config
            this.statsChannelId = this.client.config.StaffManagement?.StatsEmbed?.ChannelID;

            if (!this.statsChannelId) {
                console.warn('No stats channel ID configured in StaffManagement.StatsEmbed.ChannelID');
                return false;
            }

            console.log(`Initializing stats embed updater for channel: ${this.statsChannelId}`);

            // Initial update
            await this.updateStatsEmbed();

            // Start interval updates
            this.startUpdating();

            return true;
        } catch (error) {
            console.error('Error initializing stats embed updater:', error);
            return false;
        }
    }

    /**
     * Start automatic updates
     */
    startUpdating() {
        if (this.isUpdating) {
            console.log('Stats embed updater is already running');
            return;
        }

        this.isUpdating = true;
        this.updateInterval = setInterval(async () => {
            try {
                await this.updateStatsEmbed();
            } catch (error) {
                console.error('Error in stats embed update interval:', error);
            }
        }, this.updateFrequency);

        console.log(`Stats embed updater started with ${this.updateFrequency / 1000}s interval`);
    }

    /**
     * Stop automatic updates
     */
    stopUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.isUpdating = false;
        console.log('Stats embed updater stopped');
    }

    /**
     * Update the stats embed in the designated channel
     */
    async updateStatsEmbed() {
        try {
            const guild = this.client.guilds.cache.get(this.client.config.BotSettings.GuildID);
            if (!guild) {
                console.error('Guild not found for stats embed');
                return;
            }

            const channel = guild.channels.cache.get(this.statsChannelId);
            if (!channel) {
                console.error(`Stats channel not found: ${this.statsChannelId}`);
                return;
            }

            // Generate stats embeds
            const embeds = await this.generateStatsEmbeds();

            // Find existing message or create new one
            let message = null;
            if (this.statsMessageId) {
                try {
                    message = await channel.messages.fetch(this.statsMessageId);
                } catch (error) {
                    console.log('Stats message not found, will create new one');
                    this.statsMessageId = null;
                }
            }

            if (message) {
                // Update existing message
                await message.edit({ embeds });
                console.log('Updated existing stats embed');
            } else {
                // Create new message
                const newMessage = await channel.send({ embeds });
                this.statsMessageId = newMessage.id;
                console.log('Created new stats embed');
            }

        } catch (error) {
            console.error('Error updating stats embed:', error);
        }
    }

    /**
     * Generate comprehensive stats embeds
     */
    async generateStatsEmbeds() {
        try {
            // Get all staff statistics
            const allStats = await SharedStaffStats.find({}).sort({ 'tickets.totalClaimed': -1 });
            const staffMembers = await StaffMember.find({ active: true });

            // Get recent ban activity
            const recentBans = await StaffBanActivity.find({
                issuedAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
            }).sort({ issuedAt: -1 }).limit(10);

            const embeds = [];

            // Main stats embed
            const mainEmbed = await this.createMainStatsEmbed(allStats, staffMembers);
            embeds.push(mainEmbed);

            // Top performers embed
            const topPerformersEmbed = await this.createTopPerformersEmbed(allStats, staffMembers);
            embeds.push(topPerformersEmbed);

            // Recent activity embed
            const recentActivityEmbed = await this.createRecentActivityEmbed(recentBans, allStats);
            embeds.push(recentActivityEmbed);

            return embeds;

        } catch (error) {
            console.error('Error generating stats embeds:', error);
            return [this.createErrorEmbed()];
        }
    }

    /**
     * Create main statistics embed
     */
    async createMainStatsEmbed(allStats, staffMembers) {
        const totalStats = allStats.reduce((acc, stats) => ({
            tickets: acc.tickets + (stats.tickets?.totalClaimed || 0),
            weeklyTickets: acc.weeklyTickets + (stats.tickets?.weeklyClaimed || 0),
            messages: acc.messages + (stats.messages?.total || 0),
            weeklyMessages: acc.weeklyMessages + (stats.messages?.weekly || 0),
            bans: acc.bans + (stats.bans?.total || 0),
            weeklyBans: acc.weeklyBans + (stats.bans?.weekly || 0),
            voiceHours: acc.voiceHours + Math.round((stats.voice?.totalMinutes || 0) / 60),
            weeklyVoiceHours: acc.weeklyVoiceHours + Math.round((stats.voice?.weeklyMinutes || 0) / 60),
            ingameHours: acc.ingameHours + Math.round((stats.ingame?.totalMinutes || 0) / 60),
            weeklyIngameHours: acc.weeklyIngameHours + Math.round((stats.ingame?.weeklyMinutes || 0) / 60)
        }), {
            tickets: 0, weeklyTickets: 0, messages: 0, weeklyMessages: 0,
            bans: 0, weeklyBans: 0, voiceHours: 0, weeklyVoiceHours: 0,
            ingameHours: 0, weeklyIngameHours: 0
        });

        const embed = new EmbedBuilder()
            .setTitle('📊 Staff Team Statistics')
            .setColor('#2196F3')
            .setDescription('Comprehensive overview of staff team performance')
            .addFields([
                {
                    name: '👥 Team Overview',
                    value: `**Active Staff:** ${staffMembers.length}\n**Tracked Members:** ${allStats.length}`,
                    inline: true
                },
                {
                    name: '🎫 Tickets (All Time)',
                    value: `**Total:** ${totalStats.tickets}\n**This Week:** ${totalStats.weeklyTickets}`,
                    inline: true
                },
                {
                    name: '💬 Messages (All Time)',
                    value: `**Total:** ${totalStats.messages.toLocaleString()}\n**This Week:** ${totalStats.weeklyMessages.toLocaleString()}`,
                    inline: true
                },
                {
                    name: '🔨 Bans (All Time)',
                    value: `**Total:** ${totalStats.bans}\n**This Week:** ${totalStats.weeklyBans}`,
                    inline: true
                },
                {
                    name: '🎤 Voice Activity',
                    value: `**Total:** ${totalStats.voiceHours.toLocaleString()}h\n**This Week:** ${totalStats.weeklyVoiceHours.toLocaleString()}h`,
                    inline: true
                },
                {
                    name: '🎮 In-Game Activity',
                    value: `**Total:** ${totalStats.ingameHours.toLocaleString()}h\n**This Week:** ${totalStats.weeklyIngameHours.toLocaleString()}h`,
                    inline: true
                }
            ])
            .setTimestamp()
            .setFooter({ text: 'Updates every 5 minutes • Staff Manager' });

        // Add thumbnail if configured
        const thumbnailUrl = this.client.config.StaffManagement?.Images?.StaffStatusEmbed;
        if (thumbnailUrl) {
            embed.setThumbnail(thumbnailUrl);
        }

        return embed;
    }

    /**
     * Create top performers embed
     */
    async createTopPerformersEmbed(allStats, staffMembers) {
        // Sort by weekly activity
        const weeklyPerformers = allStats
            .map(stats => {
                const staffMember = staffMembers.find(s => s._id === stats.staffDiscordId);
                if (!staffMember) return null;

                const weeklyScore = (stats.tickets?.weeklyClaimed || 0) * 3 +
                                 (stats.messages?.weekly || 0) * 0.1 +
                                 (stats.bans?.weekly || 0) * 2 +
                                 Math.round((stats.voice?.weeklyMinutes || 0) / 60) * 0.5 +
                                 Math.round((stats.ingame?.weeklyMinutes || 0) / 60) * 0.5;

                return {
                    staffId: stats.staffDiscordId,
                    name: staffMember.discord_name,
                    weeklyScore,
                    tickets: stats.tickets?.weeklyClaimed || 0,
                    messages: stats.messages?.weekly || 0,
                    bans: stats.bans?.weekly || 0,
                    totalHours: Math.round(((stats.voice?.weeklyMinutes || 0) + (stats.ingame?.weeklyMinutes || 0)) / 60)
                };
            })
            .filter(performer => performer && performer.weeklyScore > 0)
            .sort((a, b) => b.weeklyScore - a.weeklyScore)
            .slice(0, 10);

        const embed = new EmbedBuilder()
            .setTitle('🏆 Top Weekly Performers')
            .setColor('#FFD700')
            .setDescription('Staff members with highest weekly activity scores')
            .setTimestamp();

        if (weeklyPerformers.length === 0) {
            embed.addFields([{
                name: 'No Activity',
                value: 'No staff activity recorded this week',
                inline: false
            }]);
        } else {
            const topPerformersList = weeklyPerformers.map((performer, index) => {
                const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                return `${medal} <@${performer.staffId}> - Score: ${performer.weeklyScore.toFixed(1)}\n` +
                       `   🎫 ${performer.tickets} tickets • 💬 ${performer.messages} msgs • 🔨 ${performer.bans} bans • ⏱️ ${performer.totalHours}h`;
            }).join('\n\n');

            embed.addFields([{
                name: 'Weekly Rankings',
                value: topPerformersList.length > 1024 ? topPerformersList.substring(0, 1021) + '...' : topPerformersList,
                inline: false
            }]);
        }

        return embed;
    }

    /**
     * Create recent activity embed
     */
    async createRecentActivityEmbed(recentBans, allStats) {
        const embed = new EmbedBuilder()
            .setTitle('📈 Recent Activity')
            .setColor('#4CAF50')
            .setDescription('Latest staff activity and achievements')
            .setTimestamp();

        // Recent bans
        if (recentBans.length > 0) {
            const banList = recentBans.slice(0, 5).map(ban =>
                `• <@${ban.staffDiscordId}> banned **${ban.playerName}** (${new Date(ban.issuedAt).toLocaleDateString()})`
            ).join('\n');

            embed.addFields([{
                name: '🔨 Recent Bans (Last 7 Days)',
                value: banList,
                inline: false
            }]);
        }

        // Most active today
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const todayActive = allStats
            .filter(stats => {
                const lastUpdate = new Date(stats.lastUpdated);
                return lastUpdate >= today;
            })
            .sort((a, b) => new Date(b.lastUpdated) - new Date(a.lastUpdated))
            .slice(0, 5);

        if (todayActive.length > 0) {
            const activeList = todayActive.map(stats =>
                `• <@${stats.staffDiscordId}> - Last active: ${new Date(stats.lastUpdated).toLocaleTimeString()}`
            ).join('\n');

            embed.addFields([{
                name: '🟢 Active Today',
                value: activeList,
                inline: false
            }]);
        }

        // System status
        const systemStatus = [
            `🔄 Ban Tracker: ${this.client.staffBanTracker?.isTracking ? '✅ Running' : '❌ Offline'}`,
            `🎫 Ticket Tracker: ${this.client.ticketClaimTracker?.isTracking ? '✅ Running' : '❌ Offline'}`,
            `📊 Stats Updater: ${this.isUpdating ? '✅ Running' : '❌ Offline'}`
        ].join('\n');

        embed.addFields([{
            name: '⚙️ System Status',
            value: systemStatus,
            inline: false
        }]);

        return embed;
    }

    /**
     * Create error embed
     */
    createErrorEmbed() {
        return new EmbedBuilder()
            .setTitle('❌ Stats Error')
            .setColor('#FF0000')
            .setDescription('Unable to load staff statistics at this time')
            .setTimestamp()
            .setFooter({ text: 'Staff Manager' });
    }

    /**
     * Force update stats embed
     */
    async forceUpdate() {
        console.log('Forcing stats embed update...');
        await this.updateStatsEmbed();
    }

    /**
     * Set stats channel
     */
    setStatsChannel(channelId) {
        this.statsChannelId = channelId;
        this.statsMessageId = null; // Reset message ID when channel changes
        console.log(`Stats channel set to: ${channelId}`);
    }
}

module.exports = StatsEmbedUpdater;
