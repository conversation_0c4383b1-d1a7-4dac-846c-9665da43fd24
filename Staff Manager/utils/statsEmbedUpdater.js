const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const StaffMember = require('../models/staffMember');
const StaffActivity = require('../models/staffActivity');
const moment = require('moment');

/**
 * Stats Embed Updater - Creates and updates comprehensive staff statistics embeds
 */
class StatsEmbedUpdater {
    constructor(client) {
        this.client = client;
        this.isUpdating = false;
        this.updateInterval = null;
        this.updateFrequency = 300000; // 5 minutes
        this.statsChannelId = null;
        this.statsMessageId = null;
    }

    /**
     * Initialize the stats embed updater
     */
    async initialize() {
        try {
            // Get stats channel from config
            this.statsChannelId = this.client.config.StaffManagement?.StatsEmbed?.ChannelID;

            if (!this.statsChannelId) {
                console.warn('No stats channel ID configured in StaffManagement.StatsEmbed.ChannelID');
                return false;
            }

            console.log(`Initializing stats embed updater for channel: ${this.statsChannelId}`);

            // Initial update
            await this.updateStatsEmbed();

            // Start interval updates
            this.startUpdating();

            return true;
        } catch (error) {
            console.error('Error initializing stats embed updater:', error);
            return false;
        }
    }

    /**
     * Start automatic updates
     */
    startUpdating() {
        if (this.isUpdating) {
            console.log('Stats embed updater is already running');
            return;
        }

        this.isUpdating = true;
        this.updateInterval = setInterval(async () => {
            try {
                await this.updateStatsEmbed();
            } catch (error) {
                console.error('Error in stats embed update interval:', error);
            }
        }, this.updateFrequency);

        console.log(`Stats embed updater started with ${this.updateFrequency / 1000}s interval`);
    }

    /**
     * Stop automatic updates
     */
    stopUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.isUpdating = false;
        console.log('Stats embed updater stopped');
    }

    /**
     * Update the stats embed with individual staff member embeds
     */
    async updateStatsEmbed() {
        try {
            const guild = this.client.guilds.cache.get(this.client.config.BotSettings.GuildID);
            if (!guild) {
                console.error('Guild not found for stats embed');
                return;
            }

            const channel = guild.channels.cache.get(this.statsChannelId);
            if (!channel) {
                console.error(`Stats channel not found: ${this.statsChannelId}`);
                return;
            }

            // Generate individual staff embeds
            const embedsData = await this.generateStatsEmbeds();

            // Clear existing messages first
            await this.clearExistingMessages(channel);

            // Send individual staff stats messages
            const sentMessages = [];
            for (const embedData of embedsData) {
                try {
                    // Handle both old format (single embed) and new format (embed + components)
                    if (embedData.embed) {
                        // New format with embed and components
                        const message = await channel.send({
                            embeds: [embedData.embed],
                            components: embedData.components || []
                        });
                        sentMessages.push(message.id);
                    } else {
                        // Old format (single embed) - fallback
                        const message = await channel.send({
                            embeds: [embedData]
                        });
                        sentMessages.push(message.id);
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 200));
                } catch (error) {
                    console.error('Error sending individual staff embed:', error);
                }
            }

            // Store the message IDs for future cleanup
            this.statsMessageIds = sentMessages;
            console.log(`Sent ${sentMessages.length} individual staff stats embeds`);

        } catch (error) {
            console.error('Error updating stats embed:', error);
        }
    }

    /**
     * Clear existing stats messages
     */
    async clearExistingMessages(channel) {
        try {
            if (this.statsMessageIds && this.statsMessageIds.length > 0) {
                for (const messageId of this.statsMessageIds) {
                    try {
                        const message = await channel.messages.fetch(messageId);
                        await message.delete();
                    } catch (error) {
                        // Message might already be deleted, ignore error
                        console.log(`Could not delete message ${messageId}:`, error.message);
                    }
                }
            } else if (this.statsMessageId) {
                // Handle old single message format
                try {
                    const message = await channel.messages.fetch(this.statsMessageId);
                    await message.delete();
                } catch (error) {
                    console.log(`Could not delete old message ${this.statsMessageId}:`, error.message);
                }
            }
        } catch (error) {
            console.error('Error clearing existing messages:', error);
        }
    }

    /**
     * Generate individual staff stats embeds (same format as /staffstats command)
     */
    async generateStatsEmbeds() {
        try {
            // Get all active staff members
            const staffMembers = await StaffMember.find({ active: true }).sort({ discord_name: 1 });

            if (!staffMembers || staffMembers.length === 0) {
                return [{ embed: this.createNoStaffEmbed(), components: [] }];
            }

            // Create all pages for staff stats
            const pages = await this.createAllStaffStatsPages(staffMembers);

            // Return all pages with navigation buttons if needed
            return pages.map((embed, index) => ({
                embed,
                components: pages.length > 1 ? this.createNavigationButtons(index, pages.length) : []
            }));

        } catch (error) {
            console.error('Error generating stats embeds:', error);
            return [{ embed: this.createErrorEmbed(), components: [] }];
        }
    }

    /**
     * Create all staff stats pages
     */
    async createAllStaffStatsPages(staffMembers) {
        try {
            const currentTime = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            // Build all staff info first
            const allStaffData = [];

            for (const staffMember of staffMembers) {
                try {
                    // Get staff activity data
                    const staffActivity = await StaffActivity.findById(staffMember._id);

                    if (!staffActivity) {
                        console.log(`No activity data found for staff member ${staffMember.discord_name}`);
                        continue;
                    }

                    // Get user from Discord client
                    const user = await this.client.users.fetch(staffMember._id).catch(() => null);
                    const userTag = user ? user.id : staffMember._id;

                    // Build individual staff info
                    let staffInfo = `<@${userTag}>\n`;

                    // Add Steam ID if available
                    if (staffMember.steam_id) {
                        staffInfo += `> Steam ID: ${staffMember.steam_id}\n`;
                    }

                    // Add BM ID if available
                    if (staffMember.bm_id) {
                        staffInfo += `> BM ID: ${staffMember.bm_id || 'Not set'}\n`;
                    }

                    // Add account status
                    staffInfo += `> Account: ${staffMember.account_status || 'Active'}\n`;

                    // Add activity stats
                    const voiceHours = (staffActivity.voice.total || 0).toFixed(1);
                    const ingameHours = (staffActivity.ingame.total || 0).toFixed(1);
                    staffInfo += `> Hours: ${voiceHours}h (In-game: ${voiceHours}h | Voice: ${ingameHours}h )\n`;

                    // Add activity summary
                    const totalMessages = staffActivity.messages.total || 0;
                    const totalTickets = staffActivity.tickets.total || 0;
                    const totalBans = staffActivity.bans.total || 0;
                    staffInfo += `> Activity: ${totalMessages} messages | ${totalTickets} tickets | ${totalBans} bans\n`;

                    // Add last active
                    if (staffActivity.lastUpdated) {
                        staffInfo += `> Last Active: ${moment(staffActivity.lastUpdated).format('YYYY-MM-DD')}`;
                    }

                    allStaffData.push(staffInfo);

                } catch (error) {
                    console.error(`Error processing staff member ${staffMember.discord_name}:`, error);
                    continue;
                }
            }

            if (allStaffData.length === 0) {
                const embed = new EmbedBuilder()
                    .setTitle('Staff Statistics')
                    .setDescription('No staff activity data found')
                    .setColor(this.client.config.BotSettings.EmbedColors || '#CCCCCC')
                    .setTimestamp()
                    .setFooter({
                        text: `Sorted by: Name | Detailed View`
                    });
                return [embed];
            }

            // Create pages if content is too long
            return this.createPages(allStaffData, staffMembers.length, currentTime);

        } catch (error) {
            console.error('Error creating all staff stats pages:', error);
            return [this.createErrorEmbed()];
        }
    }

    /**
     * Create navigation buttons for pagination
     */
    createNavigationButtons(currentPage, totalPages) {
        const row = new ActionRowBuilder();

        // Previous button
        row.addComponents(
            new ButtonBuilder()
                .setCustomId(`stats_prev_${currentPage}`)
                .setLabel('Previous')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage === 0)
        );

        // Page indicator
        row.addComponents(
            new ButtonBuilder()
                .setCustomId(`stats_page_${currentPage}`)
                .setLabel(`${currentPage + 1}/${totalPages}`)
                .setStyle(ButtonStyle.Primary)
                .setDisabled(true)
        );

        // Next button
        row.addComponents(
            new ButtonBuilder()
                .setCustomId(`stats_next_${currentPage}`)
                .setLabel('Next')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage === totalPages - 1)
        );

        return [row];
    }





    /**
     * Create pages for pagination
     */
    createPages(allStaffData, totalStaff, currentTime) {
        const pages = [];
        const maxDescriptionLength = 4000; // Discord limit is 4096, leave some buffer

        let currentPage = '';
        let currentPageStaffCount = 0;
        let pageNumber = 1;

        for (let i = 0; i < allStaffData.length; i++) {
            const staffInfo = allStaffData[i];
            const separator = i < allStaffData.length - 1 ? '\n\n─────────────────────────────\n\n' : '';
            const testContent = currentPage + staffInfo + separator;

            // Check if adding this staff member would exceed the limit
            if (testContent.length > maxDescriptionLength && currentPage.length > 0) {
                // Create page with current content
                const embed = new EmbedBuilder()
                    .setTitle('Staff Statistics')
                    .setDescription(currentPage.trim())
                    .setColor('#CCCCCC')
                    .setTimestamp()
                    .setFooter({
                        text: `Page ${pageNumber} • Showing ${currentPageStaffCount} of ${totalStaff} staff • Today at ${currentTime}`
                    });

                pages.push(embed);

                // Start new page
                currentPage = staffInfo + (i < allStaffData.length - 1 ? separator : '');
                currentPageStaffCount = 1;
                pageNumber++;
            } else {
                // Add to current page
                currentPage = testContent;
                currentPageStaffCount++;
            }
        }

        // Add the last page if there's content
        if (currentPage.trim().length > 0) {
            const embed = new EmbedBuilder()
                .setTitle('Staff Statistics')
                .setDescription(currentPage.trim())
                .setColor('#CCCCCC')
                .setTimestamp()
                .setFooter({
                    text: pages.length > 0
                        ? `Page ${pageNumber} • Showing ${currentPageStaffCount} of ${totalStaff} staff • Today at ${currentTime}`
                        : `Showing ${totalStaff} staff members • Today at ${currentTime}`
                });

            pages.push(embed);
        }

        return pages;
    }

    /**
     * Create error embed
     */
    createErrorEmbed() {
        return new EmbedBuilder()
            .setTitle('Stats Error')
            .setColor('#FF0000')
            .setDescription('Unable to load staff statistics at this time')
            .setTimestamp()
            .setFooter({ text: 'Staff Manager' });
    }

    /**
     * Create no staff embed
     */
    createNoStaffEmbed() {
        return new EmbedBuilder()
            .setTitle('Staff Statistics')
            .setColor('#FFA500')
            .setDescription('No active staff members found')
            .setTimestamp()
            .setFooter({ text: 'Staff Manager' });
    }



    /**
     * Force update stats embed
     */
    async forceUpdate() {
        console.log('Forcing stats embed update...');
        await this.updateStatsEmbed();
    }

    /**
     * Set stats channel
     */
    setStatsChannel(channelId) {
        this.statsChannelId = channelId;
        this.statsMessageId = null; // Reset message ID when channel changes
        console.log(`Stats channel set to: ${channelId}`);
    }
}

module.exports = StatsEmbedUpdater;
