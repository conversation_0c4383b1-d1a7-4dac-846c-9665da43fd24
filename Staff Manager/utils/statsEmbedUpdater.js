const { EmbedBuilder } = require('discord.js');
const StaffMember = require('../models/staffMember');
const StaffActivity = require('../models/staffActivity');
const moment = require('moment');

/**
 * Stats Embed Updater - Creates and updates comprehensive staff statistics embeds
 */
class StatsEmbedUpdater {
    constructor(client) {
        this.client = client;
        this.isUpdating = false;
        this.updateInterval = null;
        this.updateFrequency = 300000; // 5 minutes
        this.statsChannelId = null;
        this.statsMessageId = null;
    }

    /**
     * Initialize the stats embed updater
     */
    async initialize() {
        try {
            // Get stats channel from config
            this.statsChannelId = this.client.config.StaffManagement?.StatsEmbed?.ChannelID;

            if (!this.statsChannelId) {
                console.warn('No stats channel ID configured in StaffManagement.StatsEmbed.ChannelID');
                return false;
            }

            console.log(`Initializing stats embed updater for channel: ${this.statsChannelId}`);

            // Initial update
            await this.updateStatsEmbed();

            // Start interval updates
            this.startUpdating();

            return true;
        } catch (error) {
            console.error('Error initializing stats embed updater:', error);
            return false;
        }
    }

    /**
     * Start automatic updates
     */
    startUpdating() {
        if (this.isUpdating) {
            console.log('Stats embed updater is already running');
            return;
        }

        this.isUpdating = true;
        this.updateInterval = setInterval(async () => {
            try {
                await this.updateStatsEmbed();
            } catch (error) {
                console.error('Error in stats embed update interval:', error);
            }
        }, this.updateFrequency);

        console.log(`Stats embed updater started with ${this.updateFrequency / 1000}s interval`);
    }

    /**
     * Stop automatic updates
     */
    stopUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.isUpdating = false;
        console.log('Stats embed updater stopped');
    }

    /**
     * Update the stats embed with individual staff member embeds
     */
    async updateStatsEmbed() {
        try {
            const guild = this.client.guilds.cache.get(this.client.config.BotSettings.GuildID);
            if (!guild) {
                console.error('Guild not found for stats embed');
                return;
            }

            const channel = guild.channels.cache.get(this.statsChannelId);
            if (!channel) {
                console.error(`Stats channel not found: ${this.statsChannelId}`);
                return;
            }

            // Generate individual staff embeds
            const embedsData = await this.generateStatsEmbeds();

            // Clear existing messages first
            await this.clearExistingMessages(channel);

            // Send individual staff stats messages
            const sentMessages = [];
            for (const embedData of embedsData) {
                try {
                    // Handle both old format (single embed) and new format (embed + components)
                    if (embedData.embed) {
                        // New format with embed and components
                        const message = await channel.send({
                            embeds: [embedData.embed],
                            components: embedData.components || []
                        });
                        sentMessages.push(message.id);
                    } else {
                        // Old format (single embed) - fallback
                        const message = await channel.send({
                            embeds: [embedData]
                        });
                        sentMessages.push(message.id);
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 200));
                } catch (error) {
                    console.error('Error sending individual staff embed:', error);
                }
            }

            // Store the message IDs for future cleanup
            this.statsMessageIds = sentMessages;
            console.log(`Sent ${sentMessages.length} individual staff stats embeds`);

        } catch (error) {
            console.error('Error updating stats embed:', error);
        }
    }

    /**
     * Clear existing stats messages
     */
    async clearExistingMessages(channel) {
        try {
            if (this.statsMessageIds && this.statsMessageIds.length > 0) {
                for (const messageId of this.statsMessageIds) {
                    try {
                        const message = await channel.messages.fetch(messageId);
                        await message.delete();
                    } catch (error) {
                        // Message might already be deleted, ignore error
                        console.log(`Could not delete message ${messageId}:`, error.message);
                    }
                }
            } else if (this.statsMessageId) {
                // Handle old single message format
                try {
                    const message = await channel.messages.fetch(this.statsMessageId);
                    await message.delete();
                } catch (error) {
                    console.log(`Could not delete old message ${this.statsMessageId}:`, error.message);
                }
            }
        } catch (error) {
            console.error('Error clearing existing messages:', error);
        }
    }

    /**
     * Generate individual staff stats embeds (same format as /staffstats command)
     */
    async generateStatsEmbeds() {
        try {
            // Get all active staff members
            const staffMembers = await StaffMember.find({ active: true }).sort({ discord_name: 1 });

            if (!staffMembers || staffMembers.length === 0) {
                return [this.createNoStaffEmbed()];
            }

            const embeds = [];

            // Create individual staff stats embed for each staff member
            for (const staffMember of staffMembers) {
                try {
                    // Get staff activity data
                    const staffActivity = await StaffActivity.findById(staffMember._id);

                    if (!staffActivity) {
                        console.log(`No activity data found for staff member ${staffMember.discord_name}`);
                        continue;
                    }

                    // Create individual staff stats embed (clean format without emojis)
                    const embedData = await this.createIndividualStaffStatsEmbed(staffMember, staffActivity);
                    if (embedData && embedData.embed) {
                        embeds.push(embedData);
                    }

                } catch (error) {
                    console.error(`Error creating embed for staff member ${staffMember.discord_name}:`, error);
                    continue;
                }
            }

            // If no embeds were created, return error embed
            if (embeds.length === 0) {
                return [this.createErrorEmbed()];
            }

            return embeds;

        } catch (error) {
            console.error('Error generating stats embeds:', error);
            return [this.createErrorEmbed()];
        }
    }



    /**
     * Create individual staff stats embed (clean format without emojis)
     */
    async createIndividualStaffStatsEmbed(staffMember, staffActivity) {
        try {
            // Get user from Discord client
            const user = await this.client.users.fetch(staffMember._id).catch(() => null);
            const userTag = user ? user.tag : staffMember.discord_name;

            // Create embed with clean format
            const embed = new EmbedBuilder()
                .setTitle(`Staff Statistics`)
                .setDescription(`Showing 1 staff members`)
                .setColor(this.client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setTimestamp();

            // Build staff info string
            let staffInfo = `**${staffMember.discord_name}**\n`;
            staffInfo += `Discord: @${userTag}\n`;

            // Add Steam ID if available
            if (staffMember.steam_id) {
                staffInfo += `Steam ID: ${staffMember.steam_id}\n`;
            }

            // Add BM ID if available
            if (staffMember.bm_id) {
                staffInfo += `BM ID: ${staffMember.bm_id || 'Not set'}\n`;
            }

            // Add account status
            staffInfo += `Account: ${staffMember.account_status || 'Active'}\n`;

            // Add activity stats
            const voiceHours = (staffActivity.voice.total || 0).toFixed(1);
            const ingameHours = (staffActivity.ingame.total || 0).toFixed(1);
            staffInfo += `Hours: ${voiceHours}h (In-game: ${voiceHours}h | Voice: ${ingameHours}h )\n`;

            // Add activity summary
            const totalMessages = staffActivity.messages.total || 0;
            const totalTickets = staffActivity.tickets.total || 0;
            const totalBans = staffActivity.bans.total || 0;
            staffInfo += `Activity: ${totalMessages} messages | ${totalTickets} tickets | ${totalBans} bans\n`;

            // Add last active
            if (staffActivity.lastUpdated) {
                staffInfo += `Last Active: ${moment(staffActivity.lastUpdated).format('YYYY-MM-DD')}`;
            }

            embed.addFields({
                name: '\u200b',
                value: staffInfo,
                inline: false
            });

            // Add footer
            const currentTime = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
            embed.setFooter({
                text: `Sorted by: Name | Detailed View • Today at ${currentTime}`
            });

            return { embed, components: [] };

        } catch (error) {
            console.error(`Error creating individual staff stats embed for ${staffMember.discord_name}:`, error);
            return this.createErrorEmbed();
        }
    }

    /**
     * Create error embed
     */
    createErrorEmbed() {
        return new EmbedBuilder()
            .setTitle('Stats Error')
            .setColor('#FF0000')
            .setDescription('Unable to load staff statistics at this time')
            .setTimestamp()
            .setFooter({ text: 'Staff Manager' });
    }

    /**
     * Create no staff embed
     */
    createNoStaffEmbed() {
        return new EmbedBuilder()
            .setTitle('Staff Statistics')
            .setColor('#FFA500')
            .setDescription('No active staff members found')
            .setTimestamp()
            .setFooter({ text: 'Staff Manager' });
    }



    /**
     * Force update stats embed
     */
    async forceUpdate() {
        console.log('Forcing stats embed update...');
        await this.updateStatsEmbed();
    }

    /**
     * Set stats channel
     */
    setStatsChannel(channelId) {
        this.statsChannelId = channelId;
        this.statsMessageId = null; // Reset message ID when channel changes
        console.log(`Stats channel set to: ${channelId}`);
    }
}

module.exports = StatsEmbedUpdater;
