const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const StaffMember = require('../models/staffMember');
const StaffActivity = require('../models/staffActivity');
const moment = require('moment');

/**
 * Stats Embed Updater - Creates and updates comprehensive staff statistics embeds
 */
class StatsEmbedUpdater {
    constructor(client) {
        this.client = client;
        this.isUpdating = false;
        this.updateInterval = null;
        this.updateFrequency = 300000; // 5 minutes
        this.statsChannelId = null;
        this.statsMessageId = null;
    }

    /**
     * Initialize the stats embed updater
     */
    async initialize() {
        try {
            // Get stats channel from config
            this.statsChannelId = this.client.config.StaffManagement?.StatsEmbed?.ChannelID;

            if (!this.statsChannelId) {
                console.warn('No stats channel ID configured in StaffManagement.StatsEmbed.ChannelID');
                return false;
            }

            console.log(`Initializing stats embed updater for channel: ${this.statsChannelId}`);

            // Initial update
            await this.updateStatsEmbed();

            // Start interval updates
            this.startUpdating();

            return true;
        } catch (error) {
            console.error('Error initializing stats embed updater:', error);
            return false;
        }
    }

    /**
     * Start automatic updates
     */
    startUpdating() {
        if (this.isUpdating) {
            console.log('Stats embed updater is already running');
            return;
        }

        this.isUpdating = true;
        this.updateInterval = setInterval(async () => {
            try {
                await this.updateStatsEmbed();
            } catch (error) {
                console.error('Error in stats embed update interval:', error);
            }
        }, this.updateFrequency);

        console.log(`Stats embed updater started with ${this.updateFrequency / 1000}s interval`);
    }

    /**
     * Stop automatic updates
     */
    stopUpdating() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        this.isUpdating = false;
        console.log('Stats embed updater stopped');
    }

    /**
     * Update the stats embed with individual staff member embeds
     */
    async updateStatsEmbed() {
        try {
            const guild = this.client.guilds.cache.get(this.client.config.BotSettings.GuildID);
            if (!guild) {
                console.error('Guild not found for stats embed');
                return;
            }

            const channel = guild.channels.cache.get(this.statsChannelId);
            if (!channel) {
                console.error(`Stats channel not found: ${this.statsChannelId}`);
                return;
            }

            // Generate individual staff embeds
            const embedsData = await this.generateStatsEmbeds();

            // Clear existing messages first
            await this.clearExistingMessages(channel);

            // Send individual staff stats messages
            const sentMessages = [];
            for (const embedData of embedsData) {
                try {
                    // Handle both old format (single embed) and new format (embed + components)
                    if (embedData.embed) {
                        // New format with embed and components
                        const message = await channel.send({
                            embeds: [embedData.embed],
                            components: embedData.components || []
                        });
                        sentMessages.push(message.id);
                    } else {
                        // Old format (single embed) - fallback
                        const message = await channel.send({
                            embeds: [embedData]
                        });
                        sentMessages.push(message.id);
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 200));
                } catch (error) {
                    console.error('Error sending individual staff embed:', error);
                }
            }

            // Store the message IDs for future cleanup
            this.statsMessageIds = sentMessages;
            console.log(`Sent ${sentMessages.length} individual staff stats embeds`);

        } catch (error) {
            console.error('Error updating stats embed:', error);
        }
    }

    /**
     * Clear existing stats messages
     */
    async clearExistingMessages(channel) {
        try {
            if (this.statsMessageIds && this.statsMessageIds.length > 0) {
                for (const messageId of this.statsMessageIds) {
                    try {
                        const message = await channel.messages.fetch(messageId);
                        await message.delete();
                    } catch (error) {
                        // Message might already be deleted, ignore error
                        console.log(`Could not delete message ${messageId}:`, error.message);
                    }
                }
            } else if (this.statsMessageId) {
                // Handle old single message format
                try {
                    const message = await channel.messages.fetch(this.statsMessageId);
                    await message.delete();
                } catch (error) {
                    console.log(`Could not delete old message ${this.statsMessageId}:`, error.message);
                }
            }
        } catch (error) {
            console.error('Error clearing existing messages:', error);
        }
    }

    /**
     * Generate individual staff stats embeds (same format as /staffstats command)
     */
    async generateStatsEmbeds() {
        try {
            // Get all active staff members
            const staffMembers = await StaffMember.find({ active: true }).sort({ discord_name: 1 });

            if (!staffMembers || staffMembers.length === 0) {
                return [this.createNoStaffEmbed()];
            }

            // Calculate team statistics
            const teamStats = await this.calculateTeamStatistics(staffMembers);

            // Create the three main embeds
            const embeds = [
                await this.createTeamOverviewEmbed(teamStats),
                await this.createTopPerformersEmbed(teamStats),
                await this.createRecentActivityEmbed()
            ];

            return embeds.filter(embed => embed !== null);

        } catch (error) {
            console.error('Error generating stats embeds:', error);
            return [this.createErrorEmbed()];
        }
    }

    /**
     * Calculate comprehensive team statistics
     */
    async calculateTeamStatistics(staffMembers) {
        let totalMessages = 0;
        let totalTickets = 0;
        let totalBans = 0;
        let totalVoiceHours = 0;
        let totalIngameHours = 0;

        let weeklyMessages = 0;
        let weeklyTickets = 0;
        let weeklyBans = 0;
        let weeklyVoiceHours = 0;
        let weeklyIngameHours = 0;

        const staffPerformance = [];

        for (const staffMember of staffMembers) {
            try {
                const staffActivity = await StaffActivity.findById(staffMember._id);

                if (staffActivity) {
                    // All time stats
                    totalMessages += staffActivity.messages.total || 0;
                    totalTickets += staffActivity.tickets.total || 0;
                    totalBans += staffActivity.bans.total || 0;
                    totalVoiceHours += staffActivity.voice.total || 0;
                    totalIngameHours += staffActivity.ingame.total || 0;

                    // Weekly stats
                    weeklyMessages += staffActivity.messages.weekly || 0;
                    weeklyTickets += staffActivity.tickets.weekly || 0;
                    weeklyBans += staffActivity.bans.weekly || 0;
                    weeklyVoiceHours += staffActivity.voice.weekly || 0;
                    weeklyIngameHours += staffActivity.ingame.weekly || 0;

                    // Calculate weekly performance score
                    const weeklyScore = (staffActivity.messages.weekly || 0) +
                                      (staffActivity.tickets.weekly || 0) * 5 +
                                      (staffActivity.bans.weekly || 0) * 3 +
                                      (staffActivity.voice.weekly || 0) * 2 +
                                      (staffActivity.ingame.weekly || 0) * 1.5;

                    staffPerformance.push({
                        name: staffMember.discord_name,
                        score: weeklyScore,
                        tickets: staffActivity.tickets.weekly || 0,
                        messages: staffActivity.messages.weekly || 0,
                        bans: staffActivity.bans.weekly || 0,
                        voice: staffActivity.voice.weekly || 0,
                        ingame: staffActivity.ingame.weekly || 0
                    });
                }
            } catch (error) {
                console.error(`Error processing stats for ${staffMember.discord_name}:`, error);
            }
        }

        return {
            totalStaff: staffMembers.length,
            activeStaff: staffMembers.filter(s => s.active === true).length,
            totals: {
                messages: totalMessages,
                tickets: totalTickets,
                bans: totalBans,
                voiceHours: totalVoiceHours,
                ingameHours: totalIngameHours
            },
            weekly: {
                messages: weeklyMessages,
                tickets: weeklyTickets,
                bans: weeklyBans,
                voiceHours: weeklyVoiceHours,
                ingameHours: weeklyIngameHours
            },
            topPerformers: staffPerformance.sort((a, b) => b.score - a.score).slice(0, 3)
        };
    }

    /**
     * Create team overview embed
     */
    async createTeamOverviewEmbed(teamStats) {
        try {
            const currentTime = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle('📊 Staff Team Statistics')
                .setDescription('Comprehensive overview of staff team performance')
                .setThumbnail('https://cdn.discordapp.com/attachments/1234567890/oasis-logo.png') // Replace with your server logo
                .addFields(
                    {
                        name: '👥 Team Overview',
                        value: `Active Staff: ${teamStats.activeStaff}\nTracked Members: ${teamStats.totalStaff}`,
                        inline: true
                    },
                    {
                        name: '🎫 Tickets (All Time)',
                        value: `Total: ${teamStats.totals.tickets}\nThis Week: ${teamStats.weekly.tickets}`,
                        inline: true
                    },
                    {
                        name: '💬 Messages (All Time)',
                        value: `Total: ${teamStats.totals.messages}\nThis Week: ${teamStats.weekly.messages}`,
                        inline: true
                    },
                    {
                        name: '🔨 Bans (All Time)',
                        value: `Total: ${teamStats.totals.bans}\nThis Week: ${teamStats.weekly.bans}`,
                        inline: true
                    },
                    {
                        name: '🎤 Voice Activity',
                        value: `Total: ${teamStats.totals.voiceHours.toFixed(1)}h\nThis Week: ${teamStats.weekly.voiceHours.toFixed(1)}h`,
                        inline: true
                    },
                    {
                        name: '🎮 In-Game Activity',
                        value: `Total: ${teamStats.totals.ingameHours.toFixed(1)}h\nThis Week: ${teamStats.weekly.ingameHours.toFixed(1)}h`,
                        inline: true
                    }
                )
                .setFooter({
                    text: `Updates every 5 minutes • Staff Manager • Today at ${currentTime}`
                });

            return embed;
        } catch (error) {
            console.error('Error creating team overview embed:', error);
            return null;
        }
    }

    /**
     * Create top performers embed
     */
    async createTopPerformersEmbed(teamStats) {
        try {
            const currentTime = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            const embed = new EmbedBuilder()
                .setColor('#ffd700')
                .setTitle('🏆 Top Weekly Performers')
                .setDescription('Staff members with highest weekly activity scores');

            if (teamStats.topPerformers.length > 0 && teamStats.topPerformers[0].score > 0) {
                let performersText = '';
                teamStats.topPerformers.forEach((performer, index) => {
                    const medals = ['🥇', '🥈', '🥉'];
                    performersText += `${medals[index] || '🏅'} ${performer.name} (${performer.score.toFixed(1)} pts)\n`;
                });
                embed.addFields({
                    name: 'Top Performers',
                    value: performersText,
                    inline: false
                });
            } else {
                embed.addFields({
                    name: 'No Activity',
                    value: 'No staff activity recorded this week',
                    inline: false
                });
            }

            embed.setFooter({
                text: `Today at ${currentTime}`
            });

            return embed;
        } catch (error) {
            console.error('Error creating top performers embed:', error);
            return null;
        }
    }

    /**
     * Create recent activity embed
     */
    async createRecentActivityEmbed() {
        try {
            const currentTime = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('📈 Recent Activity')
                .setDescription('Latest staff activity and achievements')
                .addFields(
                    {
                        name: '⭐ System Status',
                        value: '🚫 Ban Tracker: ✅ Running\n🎫 Ticket Tracker: ✅ Running\n📊 Stats Updater: ✅ Running',
                        inline: false
                    }
                )
                .setFooter({
                    text: `Today at ${currentTime}`
                });

            return embed;
        } catch (error) {
            console.error('Error creating recent activity embed:', error);
            return null;
        }
    }

    /**
     * Create individual staff stats embed (exact copy of /staffstats command format)
     */
    async createIndividualStaffStatsEmbed(staffMember, staffActivity) {
        try {
            // Get user from Discord client
            const user = await this.client.users.fetch(staffMember._id).catch(() => null);
            const userTag = user ? user.tag : staffMember.discord_name;
            const userAvatar = user ? user.displayAvatarURL({ dynamic: true }) : null;

            // Create embed with same format as /staffstats command
            const embed = new EmbedBuilder()
                .setTitle(`📊 Staff Statistics - ${userTag}`)
                .setColor(this.client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setTimestamp();

            // Add user avatar if available
            if (userAvatar) {
                embed.setThumbnail(userAvatar);
            }

            // Add region info
            if (staffMember.region_info && staffMember.region_info.region) {
                const regionFlag = this.getRegionFlag(staffMember.region_info.region);
                const regionName = this.getRegionName(staffMember.region_info.region);

                embed.addFields({
                    name: 'Region',
                    value: `${regionFlag} ${regionName}`,
                    inline: true
                });
            }

            // Add timezone info
            if (staffMember.region_info && staffMember.region_info.timezone) {
                embed.addFields({
                    name: 'Timezone',
                    value: staffMember.region_info.timezone,
                    inline: true
                });
            }

            // Add last active info
            if (staffActivity.lastUpdated) {
                embed.addFields({
                    name: 'Last Active',
                    value: moment(staffActivity.lastUpdated).fromNow(),
                    inline: true
                });
            }

            // Add weekly stats
            embed.addFields({
                name: 'Weekly Stats',
                value: `> • **${staffActivity.messages.weekly}** Messages\n` +
                       `> • **${staffActivity.voice.weekly.toFixed(1)}** Voice Hours\n` +
                       `> • **${staffActivity.tickets.weekly}** Tickets\n` +
                       `> • **${staffActivity.bans.weekly}** Bans`,
                inline: false
            });

            // Add all-time stats
            embed.addFields({
                name: 'All Time Stats',
                value: `> • **${staffActivity.messages.total}** Messages\n` +
                       `> • **${staffActivity.voice.total.toFixed(1)}** Voice Hours\n` +
                       `> • **${staffActivity.ingame.total.toFixed(1)}** Ingame Hours\n` +
                       `> • **${staffActivity.tickets.total}** Tickets\n` +
                       `> • **${staffActivity.bans.total}** Bans`,
                inline: false
            });

            // Add buttons for onload/offload
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`staff_onload_${staffMember._id}`)
                        .setLabel('Onload Staff')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId(`staff_offload_${staffMember._id}`)
                        .setLabel('Offload Staff')
                        .setStyle(ButtonStyle.Danger)
                );

            return { embed, components: [row] };

        } catch (error) {
            console.error(`Error creating individual staff stats embed for ${staffMember.discord_name}:`, error);
            return this.createErrorEmbed();
        }
    }

    /**
     * Create error embed
     */
    createErrorEmbed() {
        return new EmbedBuilder()
            .setTitle('❌ Stats Error')
            .setColor('#FF0000')
            .setDescription('Unable to load staff statistics at this time')
            .setTimestamp()
            .setFooter({ text: 'Staff Manager' });
    }

    /**
     * Create no staff embed
     */
    createNoStaffEmbed() {
        return new EmbedBuilder()
            .setTitle('📊 Staff Statistics')
            .setColor('#FFA500')
            .setDescription('No active staff members found')
            .setTimestamp()
            .setFooter({ text: 'Staff Manager' });
    }

    /**
     * Get region flag emoji
     */
    getRegionFlag(region) {
        const flags = {
            'US': '🇺🇸',
            'EU': '🇪🇺',
            'UK': '🇬🇧',
            'CA': '🇨🇦',
            'AU': '🇦🇺',
            'AS': '🌏',
            'SA': '🌎',
            'AF': '🌍'
        };
        return flags[region] || '🌐';
    }

    /**
     * Get region name
     */
    getRegionName(region) {
        const names = {
            'US': 'United States',
            'EU': 'Europe',
            'UK': 'United Kingdom',
            'CA': 'Canada',
            'AU': 'Australia',
            'AS': 'Asia',
            'SA': 'South America',
            'AF': 'Africa'
        };
        return names[region] || 'Unknown';
    }

    /**
     * Force update stats embed
     */
    async forceUpdate() {
        console.log('Forcing stats embed update...');
        await this.updateStatsEmbed();
    }

    /**
     * Set stats channel
     */
    setStatsChannel(channelId) {
        this.statsChannelId = channelId;
        this.statsMessageId = null; // Reset message ID when channel changes
        console.log(`Stats channel set to: ${channelId}`);
    }
}

module.exports = StatsEmbedUpdater;
