const axios = require('axios');
const client = require('../index');

/**
 * Check if staff members are online on BattleMetrics
 * This function has been enhanced to improve automatic detection of staff members
 * @param {Array} staffMembers - Array of staff member objects
 * @param {Array} servers - Array of server objects
 * @returns {Promise<Object>} - Object with online staff members by server
 */
async function checkOnlineStaffOnBattleMetrics(staffMembers, servers) {
    const onlineStaffByServer = {};
    const staffOnline = [];

    // Create a map of Steam IDs to staff members for faster lookup
    const steamIdToStaffMap = {};
    staffMembers.forEach(staff => {
        if (staff.steam_id) {
            steamIdToStaffMap[staff.steam_id] = staff;
        }
    });

    try {
        // Filter staff members with BattleMetrics IDs
        const staffWithBmIds = staffMembers.filter(staff => staff.bm_id);

        console.log(`Total staff members: ${staffMembers.length}`);
        console.log(`Staff members with BM IDs: ${staffWithBmIds.length}`);

        // Log staff members without BM IDs
        const staffWithoutBmIds = staffMembers.filter(staff => !staff.bm_id);
        if (staffWithoutBmIds.length > 0) {
            console.log('Staff members without BattleMetrics IDs:');
            staffWithoutBmIds.forEach(staff => {
                console.log(`- ${staff.discord_name} (${staff._id}): Steam ID: ${staff.steam_id || 'Not set'}`);
            });
        }

        if (staffWithBmIds.length === 0) {
            console.log('No staff members with BattleMetrics IDs found');
            return { onlineStaffByServer, staffOnline };
        }

        // Get API token
        let apiToken = client.config.APIKeys.bm_api_token;

        // Check if token exists and log a masked version for debugging
        if (!apiToken) {
            console.error('BattleMetrics API token not configured');
            return { onlineStaffByServer, staffOnline };
        } else {
            // Make sure the token is properly formatted (remove any "Bearer " prefix if present)
            if (apiToken.startsWith('Bearer ')) {
                apiToken = apiToken.substring(7);
            }

            // Log a masked version of the token for debugging
            const maskedToken = apiToken.substring(0, 5) + '...' + apiToken.substring(apiToken.length - 5);
            console.log(`Using BattleMetrics API token: ${maskedToken}`);
        }

        // Log server information
        console.log('Checking the following servers:');
        servers.forEach(server => {
            console.log(`- ${server.name}: BM ID: ${server.battlemetrics_id || 'Not set'}`);
        });

        // Check each server for online staff
        for (const server of servers) {
            if (!server.battlemetrics_id) {
                console.log(`Server ${server.name} has no BattleMetrics ID configured`);
                continue;
            }

            console.log(`Checking BattleMetrics for online staff on ${server.name} (BM ID: ${server.battlemetrics_id})`);

            try {
                // Get player list from BattleMetrics
                console.log(`Making BattleMetrics API request for ${server.name} (BM ID: ${server.battlemetrics_id})`);

                // Initialize server in result object
                onlineStaffByServer[server.name] = [];

                // Store the response for use
                let response;
                try {
                    // Use the correct BattleMetrics API endpoint and method
                    const apiUrl = `https://api.battlemetrics.com/servers/${server.battlemetrics_id}?include=player`;
                    console.log(`Making BattleMetrics API request to: ${apiUrl}`);

                    response = await axios.get(apiUrl, {
                        headers: {
                            'Authorization': `Bearer ${apiToken}`,
                            'Accept': 'application/json'
                        },
                        timeout: 15000 // 15 second timeout
                    });

                    console.log(`BattleMetrics API response status: ${response.status}`);
                } catch (apiError) {
                    console.error(`API error for ${server.name}:`, apiError.message);
                    if (apiError.response) {
                        console.error(`Status: ${apiError.response.status}, Data:`, apiError.response.data);
                    }
                    continue;
                }

                if (response.status !== 200 || !response.data) {
                    console.error(`Failed to get player list for ${server.name}: Status ${response.status}`);
                    continue;
                }

                // Extract players from the included data
                const included = response.data.included || [];

                // Log the entire included data for debugging
                console.log(`Response included ${included.length} items`);

                // First, check if we have any players in the included data
                const players = included.filter(item => item.type === 'player');

                // If no players found in the included data, try to extract from the relationships
                if (players.length === 0 && response.data.data && response.data.data.relationships) {
                    console.log('No players found in included data, checking relationships...');

                    // Try to get players from the relationships
                    const serverData = response.data.data;
                    if (serverData.relationships && serverData.relationships.players && serverData.relationships.players.data) {
                        const playerRelationships = serverData.relationships.players.data;
                        console.log(`Found ${playerRelationships.length} players in relationships`);

                        // For each player relationship, find the corresponding player in included data
                        for (const playerRef of playerRelationships) {
                            const playerData = included.find(item => item.type === 'player' && item.id === playerRef.id);
                            if (playerData) {
                                players.push(playerData);
                            }
                        }
                    }
                }

                console.log(`Found ${players.length} players on ${server.name}`);

                // Log player IDs for debugging
                if (players.length > 0) {
                    console.log(`Player IDs on ${server.name}:`);
                    players.forEach(player => {
                        const playerId = player.id;
                        const playerName = player.attributes?.name || 'Unknown';
                        const identifiers = player.attributes?.identifiers || [];
                        const steamId = identifiers.find(id => id.type === 'steamID')?.identifier || 'Unknown';
                        console.log(`- Player ID: ${playerId}, Name: ${playerName}, Steam ID: ${steamId}`);
                    });
                } else {
                    // If still no players found, try to extract directly from the server data
                    console.log('Attempting to extract players directly from server data...');
                    try {
                        // Make a direct request to get players
                        const playersUrl = `https://api.battlemetrics.com/servers/${server.battlemetrics_id}/players`;
                        console.log(`Making direct request to ${playersUrl}`);

                        const playersResponse = await axios.get(playersUrl, {
                            headers: {
                                'Authorization': `Bearer ${apiToken}`,
                                'Accept': 'application/json'
                            },
                            timeout: 15000
                        });

                        if (playersResponse.status === 200 && playersResponse.data && playersResponse.data.data) {
                            const directPlayers = playersResponse.data.data;
                            console.log(`Found ${directPlayers.length} players from direct request`);

                            // Add these players to our players array
                            players.push(...directPlayers);

                            // Log the players
                            directPlayers.forEach(player => {
                                const playerId = player.id;
                                const playerName = player.attributes?.name || 'Unknown';
                                console.log(`- (Direct) Player ID: ${playerId}, Name: ${playerName}`);
                            });
                        }
                    } catch (directError) {
                        console.error('Error making direct players request:', directError.message);
                    }
                }

                for (const player of players) {
                    const playerId = player.id;
                    if (!playerId) continue;

                    // Get player attributes
                    const playerAttributes = player.attributes || {};
                    const playerName = playerAttributes.name || '';

                    console.log(`Processing player: ${playerName} (BM ID: ${playerId})`);

                    // Try to get Steam ID from identifiers
                    let steamId = null;
                    if (playerAttributes.identifiers) {
                        const steamIdentifier = playerAttributes.identifiers.find(id =>
                            id.type === 'steamID' || id.type === 'steam');
                        if (steamIdentifier) {
                            steamId = steamIdentifier.identifier;
                            console.log(`Found Steam ID ${steamId} for player ${playerName} with BM ID ${playerId}`);
                        }
                    }

                    // First check if this player is a staff member by BattleMetrics ID
                    let staffMember = staffWithBmIds.find(staff => staff.bm_id === playerId);

                    // If not found by BM ID, try to find by Steam ID
                    if (!staffMember && steamId) {
                        staffMember = steamIdToStaffMap[steamId];
                        if (staffMember) {
                            console.log(`Found staff member by Steam ID: ${steamId}`);

                            // Update the staff member's BM ID if it's not set
                            if (!staffMember.bm_id) {
                                console.log(`Updating BM ID for ${staffMember.discord_name} to ${playerId}`);
                                staffMember.bm_id = playerId;
                                try {
                                    await staffMember.save();
                                    console.log(`Successfully updated BM ID for ${staffMember.discord_name}`);
                                } catch (error) {
                                    console.error(`Failed to update BM ID for ${staffMember.discord_name}:`, error);
                                }
                            }
                        }
                    }

                    // If still not found, try to match by name
                    if (!staffMember && playerName) {
                        // Check for Bob the Builder specifically
                        if (playerName.includes('Bob') || playerName.includes('Builder')) {
                            console.log(`Found player with name containing 'Bob' or 'Builder': ${playerName}`);

                            // Try to find a staff member with a similar name
                            const bobStaff = staffMembers.find(staff =>
                                staff.discord_name.includes('Bob') ||
                                staff.discord_name.toLowerCase().includes('builder'));

                            if (bobStaff) {
                                console.log(`Matched ${playerName} to staff member ${bobStaff.discord_name}`);
                                staffMember = bobStaff;

                                // Update the BM ID
                                if (!bobStaff.bm_id) {
                                    console.log(`Updating BM ID for ${bobStaff.discord_name} to ${playerId}`);
                                    bobStaff.bm_id = playerId;
                                    try {
                                        await bobStaff.save();
                                        console.log(`Successfully updated BM ID for ${bobStaff.discord_name}`);
                                    } catch (error) {
                                        console.error(`Failed to update BM ID for ${bobStaff.discord_name}:`, error);
                                    }
                                }
                            }
                        }

                        // Try to match other staff members by name
                        if (!staffMember) {
                            for (const staff of staffMembers) {
                                // Skip if already matched
                                if (staffMember) break;

                                // Try to match by name
                                const staffName = staff.discord_name.toLowerCase();
                                const playerNameLower = playerName.toLowerCase();

                                if (playerNameLower.includes(staffName) || staffName.includes(playerNameLower)) {
                                    console.log(`Matched ${playerName} to staff member ${staff.discord_name} by name similarity`);
                                    staffMember = staff;

                                    // Update the BM ID
                                    if (!staff.bm_id) {
                                        console.log(`Updating BM ID for ${staff.discord_name} to ${playerId}`);
                                        staff.bm_id = playerId;
                                        try {
                                            await staff.save();
                                            console.log(`Successfully updated BM ID for ${staff.discord_name}`);
                                        } catch (error) {
                                            console.error(`Failed to update BM ID for ${staff.discord_name}:`, error);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Log the comparison for debugging
                    console.log(`Checking player (BM ID: ${playerId}, Steam ID: ${steamId || 'Unknown'}, Name: ${playerAttributes.name || 'Unknown'}) against staff members`);

                    if (staffMember) {
                        console.log(`✅ MATCH: Found staff member ${staffMember.discord_name} online on ${server.name}`);
                    } else {
                        console.log(`❌ NO MATCH: Player is not a staff member`);
                    }

                    if (staffMember) {

                        // Get player details
                        const playerAttributes = player.attributes || {};

                        // Add to online staff list for this server
                        onlineStaffByServer[server.name].push({
                            id: staffMember._id,
                            name: staffMember.discord_name,
                            playTime: playerAttributes.duration || 0,
                            region: staffMember.region_info?.region || 'Unknown',
                            flag: staffMember.region_info?.flag || null
                        });

                        // Add to overall online staff list if not already there
                        if (!staffOnline.some(s => s.id === staffMember._id)) {
                            staffOnline.push({
                                id: staffMember._id,
                                name: staffMember.discord_name,
                                server: server.name,
                                playTime: playerAttributes.duration || 0,
                                region: staffMember.region_info?.region || 'Unknown',
                                flag: staffMember.region_info?.flag || null
                            });
                        }
                    }
                }

                console.log(`Found ${onlineStaffByServer[server.name].length} staff members online on ${server.name}`);
            } catch (error) {
                console.error(`Error checking BattleMetrics for ${server.name}:`, error.message);
            }
        }

        return { onlineStaffByServer, staffOnline };
    } catch (error) {
        console.error('Error checking online staff on BattleMetrics:', error);
        return { onlineStaffByServer, staffOnline };
    }
}

module.exports = { checkOnlineStaffOnBattleMetrics };
