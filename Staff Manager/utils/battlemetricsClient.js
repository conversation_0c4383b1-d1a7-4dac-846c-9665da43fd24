const axios = require('axios');
const client = require('../index');

/**
 * Client for interacting with the BattleMetrics API
 */
class BattleMetricsClient {
    /**
     * Search for a player by Steam ID
     * @param {string} steamId - Steam ID to search for
     * @returns {Promise<Object>} - Search results
     */
    static async searchPlayerBySteamId(steamId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;

            if (!apiToken) {
                console.error('BattleMetrics API token not configured');
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            const url = `https://api.battlemetrics.com/players?filter[search]=${steamId}&include=identifier`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Success',
                dict: response.data
            };
        } catch (error) {
            console.error(`Error searching player by Steam ID ${steamId}:`, error.message);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
            }
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Get player details by BattleMetrics ID
     * @param {string} bmId - BattleMetrics player ID
     * @returns {Promise<Object>} - Player details
     */
    static async getPlayerDetails(bmId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            const url = `https://api.battlemetrics.com/players/${bmId}?include=identifier`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Success',
                dict: response.data
            };
        } catch (error) {
            console.error('Error getting player details:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Get player server history
     * @param {string} bmId - BattleMetrics player ID
     * @returns {Promise<Object>} - Player server history
     */
    static async getPlayerServerHistory(bmId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            const url = `https://api.battlemetrics.com/players/${bmId}/relationships/servers?include=server`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Success',
                dict: response.data
            };
        } catch (error) {
            console.error('Error getting player server history:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Get player session history
     * @param {string} bmId - BattleMetrics player ID
     * @param {string} serverId - BattleMetrics server ID
     * @returns {Promise<Object>} - Player session history
     */
    static async getPlayerSessionHistory(bmId, serverId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            const url = `https://api.battlemetrics.com/players/${bmId}/relationships/sessions?filter[servers]=${serverId}&include=server`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Success',
                dict: response.data
            };
        } catch (error) {
            console.error('Error getting player session history:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Get server info
     * @param {string} serverId - BattleMetrics server ID
     * @returns {Promise<Object>} - Server info
     */
    static async getServerInfo(serverId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            const url = `https://api.battlemetrics.com/servers/${serverId}`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log(`Fetched server info for ID ${serverId}:`, response.data);
            return {
                status: response.status,
                message: 'Success',
                dict: response.data
            };
        } catch (error) {
            console.error(`Error getting server info for ID ${serverId}:`, error.message);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
            }
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Get all servers associated with the organization
     * @returns {Promise<Array>} - List of servers with id and name
     */
    static async getOrganizationServers() {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;
            if (!apiToken) {
                console.error('BattleMetrics API token not configured');
                return [];
            }

            console.log('Using BattleMetrics API token:', apiToken.substring(0, 10) + '...');

            const orgId = client.config.APIKeys.bm_org_id || client.config.StaffManagement.BattlemetricsORGID;
            if (!orgId) {
                console.error('BattleMetrics organization ID not configured');
                return [];
            }

            let servers = [];
            let nextUrl = `https://api.battlemetrics.com/servers?filter[organizations]=${orgId}&filter[status]=online&page[size]=100`;

            // Handle pagination
            while (nextUrl) {
                const response = await axios.get(nextUrl, {
                    headers: {
                        'Authorization': `Bearer ${apiToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.status !== 200 || !response.data.data) {
                    console.error('Failed to fetch servers. Status:', response.status);
                    break;
                }

                const fetchedServers = response.data.data.map(server => ({
                    id: server.id,
                    name: server.attributes.name
                }));
                servers = servers.concat(fetchedServers);

                nextUrl = response.data.links?.next || null;
            }

            if (servers.length === 0) {
                console.warn('No servers found with organization filter. Using fallback servers...');
                servers = [
                    { id: '1666', name: 'AU Mrk' },
                    { id: '5795', name: 'EU Mrk' },
                    { id: '5760', name: 'US Mrk' }
                ];
            }

            return servers;
        } catch (error) {
            console.error('Error fetching organization servers:', error.message);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
            }
            return [];
        }
    }

    /**
     * Get server player list
     * @param {string} serverId - BattleMetrics server ID
     * @returns {Promise<Object>} - Server player list
     */
    static async getServerPlayerList(serverId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            const url = `https://api.battlemetrics.com/servers/${serverId}/relationships/players`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Success',
                dict: response.data
            };
        } catch (error) {
            console.error('Error getting server player list:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Get organization info
     * @returns {Promise<Object>} - Organization info
     */
    static async getOrganizationInfo() {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;
            const orgId = client.config.APIKeys.bm_org_id || client.config.StaffManagement.BattlemetricsORGID;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            if (!orgId) {
                return {
                    status: 400,
                    message: 'BattleMetrics organization ID not configured',
                    dict: null
                };
            }

            const url = `https://api.battlemetrics.com/organizations/${orgId}?include=organizationUser,role`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Success',
                dict: response.data
            };
        } catch (error) {
            console.error('Error getting organization info:', error.message);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
            }
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Create a ban
     * @param {string} bmId - BattleMetrics player ID
     * @param {string} reason - Ban reason
     * @param {Array} evidence - Evidence URLs
     * @returns {Promise<Object>} - Ban result
     */
    static async createBan(bmId, reason, evidence = []) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;
            const orgId = client.config.APIKeys.bm_org_id || client.config.StaffManagement.BattlemetricsORGID;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            if (!orgId) {
                return {
                    status: 400,
                    message: 'BattleMetrics organization ID not configured',
                    dict: null
                };
            }

            // Format evidence as text
            let evidenceText = '';
            if (evidence && evidence.length > 0) {
                evidenceText = '\n\nEvidence:\n' + evidence.join('\n');
            }

            // Create ban payload
            const banPayload = {
                data: {
                    type: 'ban',
                    attributes: {
                        note: `${reason}${evidenceText}`,
                        reason: reason.substring(0, 255), // BM has a limit on reason length
                        expires: null, // Permanent ban
                        identifiers: [
                            {
                                type: 'battlemetricsId',
                                identifier: bmId,
                                manual: true
                            }
                        ]
                    },
                    relationships: {
                        organization: {
                            data: {
                                type: 'organization',
                                id: orgId
                            }
                        },
                        server: {
                            data: null // Global ban across all servers
                        }
                    }
                }
            };

            // Send ban request
            const url = 'https://api.battlemetrics.com/bans';

            const response = await axios.post(url, banPayload, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Ban created successfully',
                dict: response.data
            };
        } catch (error) {
            console.error('Error creating ban:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Unban a player on BattleMetrics
     * @param {string} bmId - BattleMetrics player ID
     * @returns {Promise<Object>} - Unban result
     */
    static async unbanPlayer(bmId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;
            const orgId = client.config.APIKeys.bm_org_id || client.config.StaffManagement.BattlemetricsORGID;

            if (!apiToken) {
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    dict: null
                };
            }

            if (!orgId) {
                return {
                    status: 400,
                    message: 'BattleMetrics organization ID not configured',
                    dict: null
                };
            }

            // First, find the active ban for the player
            const bansUrl = `https://api.battlemetrics.com/bans?filter[identifiers]=${bmId}&filter[organizations]=${orgId}`;
            const bansResponse = await axios.get(bansUrl, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (bansResponse.status !== 200 || !bansResponse.data.data || bansResponse.data.data.length === 0) {
                return {
                    status: 404,
                    message: 'No active bans found for this player',
                    dict: null
                };
            }

            // Get the most recent ban ID
            const banId = bansResponse.data.data[0].id;

            // Unban the player by setting the ban to expired
            const unbanUrl = `https://api.battlemetrics.com/bans/${banId}`;
            const unbanPayload = {
                data: {
                    type: 'ban',
                    id: banId,
                    attributes: {
                        expires: new Date().toISOString() // Set expiration to now to effectively unban
                    }
                }
            };

            const response = await axios.patch(unbanUrl, unbanPayload, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                status: response.status,
                message: 'Player unbanned successfully',
                dict: response.data
            };
        } catch (error) {
            console.error('Error unbanning player:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                dict: null
            };
        }
    }

    /**
     * Get player's region information from BattleMetrics ID
     * @param {string} bmId - BattleMetrics player ID
     * @returns {Promise<Object>} - Region information
     */
    static async getPlayerRegionInfo(bmId) {
        try {
            const apiToken = client.config.APIKeys.bm_api_token;

            if (!apiToken) {
                console.error('BattleMetrics API token not configured');
                return {
                    status: 401,
                    message: 'BattleMetrics API token not configured',
                    regionInfo: null
                };
            }

            // Get player details including identifiers
            const playerDetails = await this.getPlayerDetails(bmId);

            if (playerDetails.status !== 200 || !playerDetails.dict) {
                console.error(`Failed to get player details for BM ID ${bmId}: ${playerDetails.message}`);
                return {
                    status: playerDetails.status,
                    message: playerDetails.message,
                    regionInfo: null
                };
            }

            console.log(`Getting region info for BM ID: ${bmId}`);

            // Log the full player details response for debugging
            console.debug('Player details response:', JSON.stringify(playerDetails.dict, null, 2));

            // Try to find country code from player data
            let countryCode = playerDetails.dict.data?.attributes?.country ||
                            playerDetails.dict.data?.meta?.country;
            console.log(`Country code from player data: ${countryCode || 'Not found'}`);

            // Look through included identifiers for IP addresses or additional country data
            let ipAddress = null;
            if (playerDetails.dict.included && Array.isArray(playerDetails.dict.included)) {
                for (const item of playerDetails.dict.included) {
                    if (item.type === 'identifier' && item.attributes?.type === 'ip' && item.attributes?.identifier) {
                        ipAddress = item.attributes.identifier;
                        if (item.attributes.metadata?.country) {
                            countryCode = item.attributes.metadata.country;
                        }
                        console.log(`Found IP: ${ipAddress} with country: ${countryCode || 'unknown'}, Proxy: ${item.attributes.metadata?.connectionInfo?.proxy || 'unknown'}`);
                        break; // Use the first IP found
                    }
                }
            } else {
                console.log('No identifiers included in the response');
            }

            // Initialize region info with fallback values
            let regionInfo = {
                flag: countryCode ? `:flag_${countryCode.toLowerCase()}:` : null,
                country: null,
                continent: null,
                region: this.mapCountryToRegion(countryCode)
            };

            // If an IP address is found, try to get detailed geolocation data from ProxyCheck.io
            if (ipAddress) {
                try {
                    console.log(`Fetching geolocation data for IP: ${ipAddress}`);
                    const proxyCheckUrl = `https://proxycheck.io/v2/${ipAddress}?vpn=1&asn=1`;

                    const proxyCheckResponse = await axios.get(proxyCheckUrl, {
                        timeout: 5000 // 5 second timeout
                    });

                    if (proxyCheckResponse.status === 200 && proxyCheckResponse.data[ipAddress]) {
                        const proxyCheckData = proxyCheckResponse.data[ipAddress];
                        countryCode = proxyCheckData.isocode || countryCode; // Update countryCode if available
                        regionInfo = {
                            flag: countryCode ? `:flag_${countryCode.toLowerCase()}:` : null,
                            country: proxyCheckData.country || null,
                            continent: proxyCheckData.continent || null,
                            region: this.mapCountryToRegion(countryCode)
                        };
                        console.log(`ProxyCheck.io data: Country=${proxyCheckData.country}, Continent=${proxyCheckData.continent}, Region=${regionInfo.region}`);
                    } else {
                        console.warn(`ProxyCheck.io returned invalid data for IP ${ipAddress}: Status ${proxyCheckResponse.status}`);
                    }
                } catch (error) {
                    console.error(`Error fetching geolocation data from ProxyCheck.io for IP ${ipAddress}:`, error.message);
                    // Fallback to using any available country code
                    regionInfo.region = this.mapCountryToRegion(countryCode);
                    regionInfo.flag = countryCode ? `:flag_${countryCode.toLowerCase()}:` : null;
                }
            } else {
                console.log('No IP address found; falling back to country code mapping');
            }

            // If no region data is available, try to infer from server history
            if (regionInfo.region === 'unknown') {
                console.log('Attempting to infer region from server history');
                const serverHistory = await this.getPlayerServerHistory(bmId);
                if (serverHistory.status === 200 && serverHistory.dict?.data?.length > 0) {
                    const recentServer = serverHistory.dict.data[0];
                    const serverId = recentServer.relationships?.server?.data?.id;
                    if (serverId) {
                        const serverInfo = await this.getServerInfo(serverId);
                        if (serverInfo.status === 200 && serverInfo.dict?.data?.attributes?.country) {
                            countryCode = serverInfo.dict.data.attributes.country;
                            console.log(`Inferred country code from server history: ${countryCode}`);
                            regionInfo = {
                                flag: countryCode ? `:flag_${countryCode.toLowerCase()}:` : null,
                                country: null,
                                continent: null,
                                region: this.mapCountryToRegion(countryCode)
                            };
                        }
                    }
                } else {
                    console.log('No server history available to infer region');
                }
            }

            console.log(`Final region info for BM ID ${bmId}:`, JSON.stringify(regionInfo));
            return {
                status: 200,
                message: 'Success',
                regionInfo
            };
        } catch (error) {
            console.error('Error getting player region info:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                regionInfo: null
            };
        }
    }

    /**
     * Map country code to region
     * @param {string} countryCode - Two-letter country code
     * @returns {string} - Region code
     */
    static mapCountryToRegion(countryCode) {
        if (!countryCode) return 'unknown';

        const regionMap = {
            'US': 'na', // United States
            'CA': 'na', // Canada
            'MX': 'na', // Mexico

            'GB': 'eu', // United Kingdom
            'UK': 'eu', // United Kingdom (alternative)
            'DE': 'eu', // Germany
            'FR': 'eu', // France
            'ES': 'eu', // Spain
            'IT': 'eu', // Italy
            'NL': 'eu', // Netherlands
            'BE': 'eu', // Belgium
            'SE': 'eu', // Sweden
            'NO': 'eu', // Norway
            'DK': 'eu', // Denmark
            'FI': 'eu', // Finland
            'PL': 'eu', // Poland
            'PT': 'eu', // Portugal
            'IE': 'eu', // Ireland
            'CH': 'eu', // Switzerland
            'AT': 'eu', // Austria
            'GR': 'eu', // Greece
            'RO': 'eu', // Romania
            'CZ': 'eu', // Czech Republic
            'HU': 'eu', // Hungary
            'BG': 'eu', // Bulgaria
            'HR': 'eu', // Croatia
            'RS': 'eu', // Serbia
            'UA': 'eu', // Ukraine
            'RU': 'eu', // Russia

            'AU': 'au', // Australia
            'NZ': 'au', // New Zealand

            'BR': 'sa', // Brazil
            'AR': 'sa', // Argentina
            'CL': 'sa', // Chile
            'CO': 'sa', // Colombia
            'PE': 'sa', // Peru
            'VE': 'sa', // Venezuela

            'JP': 'as', // Japan
            'CN': 'as', // China
            'KR': 'as', // South Korea
            'IN': 'as', // India
            'SG': 'as', // Singapore
            'MY': 'as', // Malaysia
            'PH': 'as', // Philippines
            'ID': 'as', // Indonesia
            'TH': 'as', // Thailand
            'VN': 'as', // Vietnam
            'HK': 'as', // Hong Kong
            'TW': 'as', // Taiwan

            'ZA': 'af', // South Africa
            'EG': 'af', // Egypt
            'NG': 'af', // Nigeria
            'KE': 'af', // Kenya
            'MA': 'af', // Morocco
            'GH': 'af', // Ghana
            'TN': 'af', // Tunisia
            'DZ': 'af', // Algeria
        };

        return regionMap[countryCode.toUpperCase()] || 'unknown';
    }

    /**
     * Get staff BattleMetrics ID from organization users
     * @param {string} discordName - Discord username to match
     * @returns {Promise<Object>} - Staff BattleMetrics ID
     */
    static async getStaffBattleMetricsId(discordName) {
        try {
            const orgInfo = await this.getOrganizationInfo();

            if (orgInfo.status !== 200 || !orgInfo.dict) {
                return {
                    status: orgInfo.status,
                    message: orgInfo.message,
                    staffBmId: null
                };
            }

            // Find the staff member by nickname
            let staffBmId = null;

            if (orgInfo.dict.included && Array.isArray(orgInfo.dict.included)) {
                for (const item of orgInfo.dict.included) {
                    if (item.type === 'organizationUser' &&
                        item.attributes?.nickname === discordName) {
                        staffBmId = item.id.split(':')[0];
                        break;
                    }
                }
            }

            if (!staffBmId) {
                return {
                    status: 404,
                    message: 'Staff member not found in organization',
                    staffBmId: null
                };
            }

            return {
                status: 200,
                message: 'Success',
                staffBmId
            };
        } catch (error) {
            console.error('Error getting staff BattleMetrics ID:', error);
            return {
                status: error.response?.status || 500,
                message: error.message,
                staffBmId: null
            };
        }
    }
}

module.exports = BattleMetricsClient;