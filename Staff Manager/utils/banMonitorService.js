const axios = require('axios');
const StaffBanActivity = require('../models/staffBanActivity');
const StaffMember = require('../models/staffMember');
const SharedStaffStats = require('../models/sharedStaffStats');
const StaffActivity = require('../models/staffActivity');
const { EmbedBuilder, WebhookClient } = require('discord.js');

class BanMonitorService {
    constructor(client) {
        this.client = client;
        this.isMonitoring = false;
        this.monitorInterval = null;
        this.lastChecked = new Date();
        this.checkInterval = 60000; // 1 minute
    }

    /**
     * Start monitoring BattleMetrics for new bans
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            console.log('Ban monitoring is already running');
            return;
        }

        console.log('Starting BattleMetrics ban monitoring...');
        this.isMonitoring = true;
        
        // Initial check
        await this.checkForNewBans();
        
        // Set up interval
        this.monitorInterval = setInterval(async () => {
            try {
                await this.checkForNewBans();
            } catch (error) {
                console.error('Error in ban monitoring interval:', error);
            }
        }, this.checkInterval);
        
        console.log(`Ban monitoring started with ${this.checkInterval / 1000}s interval`);
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }
        this.isMonitoring = false;
        console.log('Ban monitoring stopped');
    }

    /**
     * Check for new bans from BattleMetrics
     */
    async checkForNewBans() {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;
            const banListId = config.APIKeys?.bm_ban_list_id;

            if (!apiToken || !banListId) {
                console.error('BattleMetrics API token or ban list ID not configured');
                return;
            }

            // Fetch recent bans from BattleMetrics
            const url = `https://api.battlemetrics.com/bans?filter[banList]=${banListId}&include=user,server&filter[expired]=false&page[size]=20&sort=-timestamp`;
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (response.status !== 200) {
                console.error(`Error fetching bans: ${response.status}`);
                return;
            }

            const bansData = response.data;
            
            // Process each ban
            for (const ban of bansData.data) {
                await this.processBan(ban, bansData.included);
            }

            this.lastChecked = new Date();
            
        } catch (error) {
            console.error('Error checking for new bans:', error);
        }
    }

    /**
     * Process a single ban and determine if it was issued by staff
     */
    async processBan(ban, includedData) {
        try {
            // Check if we've already processed this ban
            const existingBan = await StaffBanActivity.findOne({ banId: ban.id });
            if (existingBan) {
                return;
            }

            // Extract ban information
            const banAttributes = ban.attributes;
            const banTimestamp = new Date(banAttributes.timestamp);
            
            // Only process bans from the last 24 hours to avoid processing old bans
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            if (banTimestamp < oneDayAgo) {
                return;
            }

            // Get player information
            const playerData = this.getPlayerFromIncluded(ban.relationships?.user?.data?.id, includedData);
            const serverData = this.getServerFromIncluded(ban.relationships?.server?.data?.id, includedData);

            // Extract Steam ID
            let steamId = 'Unknown';
            if (banAttributes.identifiers) {
                for (const identifier of banAttributes.identifiers) {
                    if (identifier.type === 'steamID') {
                        steamId = identifier.identifier;
                        break;
                    }
                }
            }

            // Try to determine which staff member issued the ban
            const staffMember = await this.identifyStaffMember(ban, steamId, banTimestamp);
            
            if (staffMember) {
                // This ban was issued by a staff member
                await this.recordStaffBan(staffMember, ban, playerData, serverData, steamId);
                console.log(`Recorded ban by staff member ${staffMember.discord_name} (${ban.id})`);
            } else {
                // Record as unattributed ban for tracking purposes
                console.log(`Could not attribute ban ${ban.id} to any staff member`);
            }

        } catch (error) {
            console.error('Error processing ban:', error);
        }
    }

    /**
     * Try to identify which staff member issued the ban
     */
    async identifyStaffMember(ban, steamId, banTimestamp) {
        try {
            // Method 1: Check ban note for staff mentions or signatures
            const banNote = ban.attributes.note || '';
            const staffMembers = await StaffMember.find({ active: true });
            
            // Look for staff member names or Discord IDs in the ban note
            for (const staff of staffMembers) {
                if (banNote.includes(staff.discord_name) || 
                    banNote.includes(staff._id) ||
                    banNote.toLowerCase().includes(staff.discord_name.toLowerCase())) {
                    return staff;
                }
            }

            // Method 2: Check recent RCON activity (if available)
            // This would require integration with RCON logs
            
            // Method 3: Check BattleMetrics organization activity
            // This requires additional API calls to get organization activity
            
            return null;
        } catch (error) {
            console.error('Error identifying staff member:', error);
            return null;
        }
    }

    /**
     * Record a ban issued by a staff member
     */
    async recordStaffBan(staffMember, ban, playerData, serverData, steamId) {
        try {
            const banAttributes = ban.attributes;
            
            // Create staff ban activity record
            const staffBanActivity = new StaffBanActivity({
                staffDiscordId: staffMember._id,
                banId: ban.id,
                playerBMId: ban.relationships?.user?.data?.id || 'Unknown',
                playerSteamId: steamId,
                playerName: playerData?.attributes?.name || 'Unknown',
                reason: banAttributes.reason || 'No reason provided',
                serverName: serverData?.attributes?.name || 'Unknown Server',
                serverId: ban.relationships?.server?.data?.id || 'Unknown',
                banNote: banAttributes.note || '',
                expiresAt: banAttributes.expires ? new Date(banAttributes.expires) : null,
                issuedAt: new Date(banAttributes.timestamp),
                processed: true
            });

            await staffBanActivity.save();

            // Update staff activity statistics
            await this.updateStaffStats(staffMember._id);

            // Send notification if configured
            await this.sendBanNotification(staffMember, staffBanActivity, playerData);

        } catch (error) {
            console.error('Error recording staff ban:', error);
        }
    }

    /**
     * Update staff statistics for ban activity
     */
    async updateStaffStats(staffDiscordId) {
        try {
            // Update shared stats
            await SharedStaffStats.updateBanStats(staffDiscordId);
            
            // Update legacy staff activity model
            await StaffActivity.findOneAndUpdate(
                { _id: staffDiscordId },
                {
                    $inc: {
                        'bans.total': 1,
                        'bans.weekly': 1
                    },
                    $set: {
                        lastUpdated: new Date()
                    }
                },
                { upsert: true }
            );

        } catch (error) {
            console.error('Error updating staff stats:', error);
        }
    }

    /**
     * Send notification about staff ban activity
     */
    async sendBanNotification(staffMember, banActivity, playerData) {
        try {
            const config = this.client.config;
            const webhookUrl = config.Webhooks?.BanStaffWebhook;
            
            if (!webhookUrl) {
                return; // No webhook configured
            }

            const embed = new EmbedBuilder()
                .setTitle('🔨 Staff Ban Issued')
                .setColor('#FF6B6B')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffMember._id}>`, inline: true },
                    { name: 'Player', value: banActivity.playerName, inline: true },
                    { name: 'Server', value: banActivity.serverName, inline: true },
                    { name: 'Reason', value: banActivity.reason, inline: false },
                    { name: 'Steam ID', value: banActivity.playerSteamId, inline: true },
                    { name: 'Ban ID', value: banActivity.banId, inline: true }
                ])
                .setTimestamp(banActivity.issuedAt);

            if (banActivity.banNote) {
                embed.addFields([{ name: 'Note', value: banActivity.banNote, inline: false }]);
            }

            const webhook = new WebhookClient({ url: webhookUrl });
            await webhook.send({ embeds: [embed] });

        } catch (error) {
            console.error('Error sending ban notification:', error);
        }
    }

    /**
     * Helper method to get player data from included array
     */
    getPlayerFromIncluded(playerId, includedData) {
        if (!playerId || !includedData) return null;
        return includedData.find(item => item.type === 'player' && item.id === playerId);
    }

    /**
     * Helper method to get server data from included array
     */
    getServerFromIncluded(serverId, includedData) {
        if (!serverId || !includedData) return null;
        return includedData.find(item => item.type === 'server' && item.id === serverId);
    }

    /**
     * Get ban statistics for a staff member
     */
    async getStaffBanStats(staffDiscordId, timeframe = 'all') {
        try {
            const query = { staffDiscordId };
            
            if (timeframe === 'week') {
                const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                query.issuedAt = { $gte: weekAgo };
            } else if (timeframe === 'month') {
                const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                query.issuedAt = { $gte: monthAgo };
            }

            const bans = await StaffBanActivity.find(query).sort({ issuedAt: -1 });
            
            return {
                total: bans.length,
                bans: bans
            };

        } catch (error) {
            console.error('Error getting staff ban stats:', error);
            return { total: 0, bans: [] };
        }
    }
}

module.exports = BanMonitorService;
