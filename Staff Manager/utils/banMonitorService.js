const axios = require('axios');
// Note: This service only handles public ban notifications
// Staff attribution is handled by the separate StaffBanTracker service

class BanMonitorService {
    constructor(client) {
        this.client = client;
        this.isMonitoring = false;
        this.monitorInterval = null;
        this.lastChecked = new Date();
        this.checkInterval = 60000; // 1 minute
    }

    /**
     * Start monitoring BattleMetrics for new bans
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            console.log('Ban monitoring is already running');
            return;
        }

        console.log('Starting BattleMetrics ban monitoring...');
        this.isMonitoring = true;

        // Initial check
        await this.checkForNewBans();

        // Set up interval
        this.monitorInterval = setInterval(async () => {
            try {
                await this.checkForNewBans();
            } catch (error) {
                console.error('Error in ban monitoring interval:', error);
            }
        }, this.checkInterval);

        console.log(`Ban monitoring started with ${this.checkInterval / 1000}s interval`);
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }
        this.isMonitoring = false;
        console.log('Ban monitoring stopped');
    }

    /**
     * Check for new bans from BattleMetrics
     */
    async checkForNewBans() {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;
            const banListId = config.APIKeys?.bm_ban_list_id;

            if (!apiToken || !banListId) {
                console.error('BattleMetrics API token or ban list ID not configured');
                return;
            }

            // Fetch recent bans from BattleMetrics
            const url = `https://api.battlemetrics.com/bans?filter[banList]=${banListId}&include=user,server&filter[expired]=false&page[size]=20&sort=-timestamp`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (response.status !== 200) {
                console.error(`Error fetching bans: ${response.status}`);
                return;
            }

            const bansData = response.data;

            // Process each ban
            for (const ban of bansData.data) {
                await this.processBan(ban, bansData.included);
            }

            this.lastChecked = new Date();

        } catch (error) {
            console.error('Error checking for new bans:', error);
        }
    }

    /**
     * Process a single ban and determine if it was issued by staff
     */
    async processBan(ban, includedData) {
        try {
            // Check if we've already processed this ban
            const existingBan = await StaffBanActivity.findOne({ banId: ban.id });
            if (existingBan) {
                return;
            }

            // Extract ban information
            const banAttributes = ban.attributes;
            const banTimestamp = new Date(banAttributes.timestamp);

            // Only process bans from the last 24 hours to avoid processing old bans
            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            if (banTimestamp < oneDayAgo) {
                return;
            }

            // Get player information
            const playerData = this.getPlayerFromIncluded(ban.relationships?.user?.data?.id, includedData);
            const serverData = this.getServerFromIncluded(ban.relationships?.server?.data?.id, includedData);

            // Extract Steam ID
            let steamId = 'Unknown';
            if (banAttributes.identifiers) {
                for (const identifier of banAttributes.identifiers) {
                    if (identifier.type === 'steamID') {
                        steamId = identifier.identifier;
                        break;
                    }
                }
            }

            // This service only handles public ban notifications
            // Staff attribution is handled by the separate StaffBanTracker service
            console.log(`Processing public ban notification for ban ${ban.id}`);

        } catch (error) {
            console.error('Error processing ban:', error);
        }
    }

    // Staff attribution methods removed - handled by StaffBanTracker service

    /**
     * Helper method to get player data from included array
     */
    getPlayerFromIncluded(playerId, includedData) {
        if (!playerId || !includedData) return null;
        return includedData.find(item => item.type === 'player' && item.id === playerId);
    }

    /**
     * Helper method to get server data from included array
     */
    getServerFromIncluded(serverId, includedData) {
        if (!serverId || !includedData) return null;
        return includedData.find(item => item.type === 'server' && item.id === serverId);
    }

    /**
     * Get ban statistics for a staff member
     */
    async getStaffBanStats(staffDiscordId, timeframe = 'all') {
        try {
            const query = { staffDiscordId };

            if (timeframe === 'week') {
                const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                query.issuedAt = { $gte: weekAgo };
            } else if (timeframe === 'month') {
                const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                query.issuedAt = { $gte: monthAgo };
            }

            const bans = await StaffBanActivity.find(query).sort({ issuedAt: -1 });

            return {
                total: bans.length,
                bans: bans
            };

        } catch (error) {
            console.error('Error getting staff ban stats:', error);
            return { total: 0, bans: [] };
        }
    }
}

module.exports = BanMonitorService;
