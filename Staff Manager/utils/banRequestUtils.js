const { EmbedBuilder } = require('discord.js');
const axios = require('axios');

class BanRequestUtils {
    constructor(client) {
        this.client = client;
    }

    /**
     * Create a ban on BattleMetrics using the correct API format
     */
    async createBattleMetricsBan(battlemetricsId, reason, evidenceUrls = [], steamId = null) {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;
            const banListId = config.APIKeys?.bm_ban_list_id;

            if (!apiToken || !banListId) {
                throw new Error('BattleMetrics API token or ban list ID not configured');
            }

            console.log(`Creating BattleMetrics ban for BM ID: ${battlemetricsId}, Steam ID: ${steamId}`);
            console.log(`Reason: ${reason}`);
            console.log(`Evidence: ${evidenceUrls}`);

            // Prepare identifiers array - use Steam ID if available, otherwise use BM player ID
            const identifiers = [];
            if (steamId) {
                identifiers.push({
                    type: 'steamID',
                    identifier: steamId,
                    manual: true
                });
            }

            // Prepare ban data according to BattleMetrics API documentation
            const banData = {
                data: {
                    type: 'ban',
                    attributes: {
                        autoAddEnabled: true,
                        expires: null, // Permanent ban
                        identifiers: identifiers,
                        nativeEnabled: null, // Use ban list default
                        reason: reason,
                        note: evidenceUrls.length > 0 ? `Evidence: ${evidenceUrls.join(', ')}` : '',
                        orgWide: true // Apply to all servers in organization
                    },
                    relationships: {
                        banList: {
                            data: {
                                type: 'banList',
                                id: banListId
                            }
                        }
                    }
                }
            };

            // Add player relationship if BattleMetrics ID is available
            if (battlemetricsId && battlemetricsId !== 'none') {
                banData.data.relationships.player = {
                    data: {
                        type: 'player',
                        id: battlemetricsId
                    }
                };
            }

            console.log('Ban data payload:', JSON.stringify(banData, null, 2));

            const response = await axios.post('https://api.battlemetrics.com/bans', banData, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            console.log('BattleMetrics ban response:', response.status, response.data);

            return {
                success: true,
                status: response.status,
                message: 'Ban created successfully',
                banId: response.data.data.id,
                banUid: response.data.data.attributes?.uid
            };

        } catch (error) {
            console.error('Error creating BattleMetrics ban:', error);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', JSON.stringify(error.response.data, null, 2));
            }
            return {
                success: false,
                status: error.response?.status || 500,
                message: error.response?.data?.errors?.[0]?.detail || error.message
            };
        }
    }

    /**
     * Unban a player on BattleMetrics
     */
    async unbanPlayer(battlemetricsId) {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;
            const banListId = config.APIKeys?.bm_ban_list_id;

            if (!apiToken || !banListId) {
                throw new Error('BattleMetrics API token or ban list ID not configured');
            }

            // First, get the player's active bans
            const bansUrl = `https://api.battlemetrics.com/bans?filter[player]=${battlemetricsId}&filter[banList]=${banListId}&filter[expired]=false`;

            const bansResponse = await axios.get(bansUrl, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (!bansResponse.data.data || bansResponse.data.data.length === 0) {
                return {
                    success: false,
                    status: 404,
                    message: 'No active bans found for this player'
                };
            }

            // Get the most recent ban ID
            const banId = bansResponse.data.data[0].id;

            // Unban the player by setting the ban to expired
            const unbanUrl = `https://api.battlemetrics.com/bans/${banId}`;
            const unbanPayload = {
                data: {
                    type: 'ban',
                    id: banId,
                    attributes: {
                        expires: new Date().toISOString() // Set expiration to now to effectively unban
                    }
                }
            };

            const response = await axios.patch(unbanUrl, unbanPayload, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                success: true,
                status: response.status,
                message: 'Player unbanned successfully',
                banId: banId
            };

        } catch (error) {
            console.error('Error unbanning player:', error);
            return {
                success: false,
                status: error.response?.status || 500,
                message: error.response?.data?.errors?.[0]?.detail || error.message
            };
        }
    }

    /**
     * Get player information from BattleMetrics
     */
    async getPlayerInfo(steamId) {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;

            if (!apiToken) {
                throw new Error('BattleMetrics API token not configured');
            }

            const url = `https://api.battlemetrics.com/players?filter[search]=${steamId}&include=server,playerFlag,identifier,flagPlayer`;

            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (!response.data.data || response.data.data.length === 0) {
                return {
                    success: false,
                    message: 'Player not found on BattleMetrics'
                };
            }

            const playerData = response.data.data[0];
            return {
                success: true,
                battlemetricsId: playerData.id,
                playerName: playerData.attributes.name,
                data: playerData
            };

        } catch (error) {
            console.error('Error getting player info:', error);
            return {
                success: false,
                message: error.response?.data?.errors?.[0]?.detail || error.message
            };
        }
    }
}

module.exports = BanRequestUtils;
