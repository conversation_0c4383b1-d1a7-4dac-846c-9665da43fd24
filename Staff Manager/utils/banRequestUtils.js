const { EmbedBuilder } = require('discord.js');
const axios = require('axios');

class BanRequestUtils {
    constructor(client) {
        this.client = client;
    }

    /**
     * Create a ban on BattleMetrics
     */
    async createBattleMetricsBan(battlemetricsId, reason, evidenceUrls = []) {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;
            const banListId = config.APIKeys?.bm_ban_list_id;

            if (!apiToken || !banListId) {
                throw new Error('BattleMetrics API token or ban list ID not configured');
            }

            // Prepare ban data
            const banData = {
                data: {
                    type: 'ban',
                    attributes: {
                        autoAddEnabled: true,
                        expires: null, // Permanent ban
                        identifiers: [
                            {
                                type: 'steamID',
                                identifier: battlemetricsId,
                                manual: true
                            }
                        ],
                        nativeEnabled: null,
                        reason: reason,
                        note: evidenceUrls.length > 0 ? `Evidence: ${evidenceUrls.join(', ')}` : ''
                    },
                    relationships: {
                        banList: {
                            data: {
                                type: 'banList',
                                id: banListId
                            }
                        },
                        player: {
                            data: {
                                type: 'player',
                                id: battlemetricsId
                            }
                        }
                    }
                }
            };

            const response = await axios.post('https://api.battlemetrics.com/bans', banData, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            return {
                success: true,
                status: response.status,
                message: 'Ban created successfully',
                banId: response.data.data.id
            };

        } catch (error) {
            console.error('Error creating BattleMetrics ban:', error);
            return {
                success: false,
                status: error.response?.status || 500,
                message: error.response?.data?.errors?.[0]?.detail || error.message
            };
        }
    }

    /**
     * Unban a player on BattleMetrics
     */
    async unbanPlayer(battlemetricsId) {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;
            const banListId = config.APIKeys?.bm_ban_list_id;

            if (!apiToken || !banListId) {
                throw new Error('BattleMetrics API token or ban list ID not configured');
            }

            // First, get the player's active bans
            const bansUrl = `https://api.battlemetrics.com/bans?filter[player]=${battlemetricsId}&filter[banList]=${banListId}&filter[expired]=false`;
            
            const bansResponse = await axios.get(bansUrl, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (!bansResponse.data.data || bansResponse.data.data.length === 0) {
                return {
                    success: false,
                    status: 404,
                    message: 'No active bans found for this player'
                };
            }

            // Get the most recent ban ID
            const banId = bansResponse.data.data[0].id;

            // Unban the player by setting the ban to expired
            const unbanUrl = `https://api.battlemetrics.com/bans/${banId}`;
            const unbanPayload = {
                data: {
                    type: 'ban',
                    id: banId,
                    attributes: {
                        expires: new Date().toISOString() // Set expiration to now to effectively unban
                    }
                }
            };

            const response = await axios.patch(unbanUrl, unbanPayload, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                }
            });

            return {
                success: true,
                status: response.status,
                message: 'Player unbanned successfully',
                banId: banId
            };

        } catch (error) {
            console.error('Error unbanning player:', error);
            return {
                success: false,
                status: error.response?.status || 500,
                message: error.response?.data?.errors?.[0]?.detail || error.message
            };
        }
    }

    /**
     * Get player information from BattleMetrics
     */
    async getPlayerInfo(steamId) {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;

            if (!apiToken) {
                throw new Error('BattleMetrics API token not configured');
            }

            const url = `https://api.battlemetrics.com/players?filter[search]=${steamId}&include=server,playerFlag,identifier,flagPlayer`;
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (!response.data.data || response.data.data.length === 0) {
                return {
                    success: false,
                    message: 'Player not found on BattleMetrics'
                };
            }

            const playerData = response.data.data[0];
            return {
                success: true,
                battlemetricsId: playerData.id,
                playerName: playerData.attributes.name,
                data: playerData
            };

        } catch (error) {
            console.error('Error getting player info:', error);
            return {
                success: false,
                message: error.response?.data?.errors?.[0]?.detail || error.message
            };
        }
    }
}

module.exports = BanRequestUtils;
