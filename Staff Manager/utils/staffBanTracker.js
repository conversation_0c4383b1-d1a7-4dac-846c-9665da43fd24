const axios = require('axios');
const StaffBanActivity = require('../models/staffBanActivity');
const StaffMember = require('../models/staffMember');
const SharedStaffStats = require('../models/sharedStaffStats');
const StaffActivity = require('../models/staffActivity');
const { EmbedBuilder, WebhookClient } = require('discord.js');

/**
 * Staff Ban Tracker - Tracks bans back to staff members using BattleMetrics emails
 * This is separate from the existing ban monitor which sends bans to public
 */
class StaffBanTracker {
    constructor(client) {
        this.client = client;
        this.isTracking = false;
        this.trackingInterval = null;
        this.lastChecked = new Date();
        this.checkInterval = 120000; // 2 minutes (different from public ban monitor)
    }

    /**
     * Start tracking bans for staff attribution
     */
    async startTracking() {
        if (this.isTracking) {
            console.log('Staff ban tracking is already running');
            return;
        }

        console.log('Starting staff ban tracking...');
        this.isTracking = true;
        
        // Initial check
        await this.trackStaffBans();
        
        // Set up interval
        this.trackingInterval = setInterval(async () => {
            try {
                await this.trackStaffBans();
            } catch (error) {
                console.error('Error in staff ban tracking interval:', error);
            }
        }, this.checkInterval);
        
        console.log(`Staff ban tracking started with ${this.checkInterval / 1000}s interval`);
    }

    /**
     * Stop tracking
     */
    stopTracking() {
        if (this.trackingInterval) {
            clearInterval(this.trackingInterval);
            this.trackingInterval = null;
        }
        this.isTracking = false;
        console.log('Staff ban tracking stopped');
    }

    /**
     * Track bans and attribute them to staff members using BattleMetrics emails
     */
    async trackStaffBans() {
        try {
            const config = this.client.config;
            const apiToken = config.APIKeys?.bm_api_token;
            const banListId = config.APIKeys?.bm_ban_list_id;

            if (!apiToken || !banListId) {
                console.error('BattleMetrics API token or ban list ID not configured for staff ban tracking');
                return;
            }

            // Fetch recent bans from BattleMetrics with user details
            const url = `https://api.battlemetrics.com/bans?filter[banList]=${banListId}&include=user,server&filter[expired]=false&page[size]=50&sort=-timestamp`;
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            if (response.status !== 200) {
                console.error(`Error fetching bans for staff tracking: ${response.status}`);
                return;
            }

            const bansData = response.data;
            
            // Process each ban for staff attribution
            for (const ban of bansData.data) {
                await this.processBanForStaffAttribution(ban, bansData.included);
            }

            this.lastChecked = new Date();
            
        } catch (error) {
            console.error('Error tracking staff bans:', error);
        }
    }

    /**
     * Process a ban and try to attribute it to a staff member using BattleMetrics data
     */
    async processBanForStaffAttribution(ban, includedData) {
        try {
            // Check if we've already processed this ban
            const existingBan = await StaffBanActivity.findOne({ banId: ban.id });
            if (existingBan) {
                return;
            }

            // Only process recent bans (last 7 days)
            const banTimestamp = new Date(ban.attributes.timestamp);
            const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            if (banTimestamp < sevenDaysAgo) {
                return;
            }

            // Get detailed ban information from BattleMetrics
            const banDetails = await this.getBanDetails(ban.id);
            if (!banDetails) {
                console.log(`Could not get ban details for ban ${ban.id}`);
                return;
            }

            // Try to find staff member by BattleMetrics email
            const staffMember = await this.findStaffByBattleMetricsEmail(banDetails);
            
            if (staffMember) {
                console.log(`Ban ${ban.id} attributed to staff member ${staffMember.discord_name} via BattleMetrics email`);
                
                // Get additional ban data
                const playerData = this.getPlayerFromIncluded(ban.relationships?.user?.data?.id, includedData);
                const serverData = this.getServerFromIncluded(ban.relationships?.server?.data?.id, includedData);
                
                // Extract Steam ID
                let steamId = 'Unknown';
                if (ban.attributes.identifiers) {
                    for (const identifier of ban.attributes.identifiers) {
                        if (identifier.type === 'steamID') {
                            steamId = identifier.identifier;
                            break;
                        }
                    }
                }

                // Record the staff ban
                await this.recordStaffBan(staffMember, ban, playerData, serverData, steamId, banDetails);
            } else {
                console.log(`Could not attribute ban ${ban.id} to any staff member`);
            }

        } catch (error) {
            console.error('Error processing ban for staff attribution:', error);
        }
    }

    /**
     * Get detailed ban information from BattleMetrics API
     */
    async getBanDetails(banId) {
        try {
            const apiToken = this.client.config.APIKeys?.bm_api_token;
            if (!apiToken) return null;

            const url = `https://api.battlemetrics.com/bans/${banId}?include=user,server,banList`;
            
            const response = await axios.get(url, {
                headers: {
                    'Authorization': `Bearer ${apiToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.status === 200) {
                return response.data;
            }

            return null;
        } catch (error) {
            console.error(`Error getting ban details for ${banId}:`, error.message);
            return null;
        }
    }

    /**
     * Find staff member by BattleMetrics email from ban details
     */
    async findStaffByBattleMetricsEmail(banDetails) {
        try {
            // Look for admin/moderator information in the ban data
            // BattleMetrics includes admin email in ban metadata for organization bans
            let adminEmail = null;

            // Check ban attributes for admin information
            if (banDetails.data?.attributes?.metadata?.admin_email) {
                adminEmail = banDetails.data.attributes.metadata.admin_email;
            } else if (banDetails.data?.attributes?.admin_email) {
                adminEmail = banDetails.data.attributes.admin_email;
            } else if (banDetails.data?.relationships?.admin?.data?.attributes?.email) {
                adminEmail = banDetails.data.relationships.admin.data.attributes.email;
            }

            // Also check included data for admin information
            if (!adminEmail && banDetails.included) {
                for (const included of banDetails.included) {
                    if (included.type === 'user' && included.attributes?.email) {
                        // This might be the admin who issued the ban
                        adminEmail = included.attributes.email;
                        break;
                    }
                }
            }

            if (!adminEmail) {
                return null;
            }

            // Find staff member with matching BattleMetrics email
            const staffMember = await StaffMember.findOne({ 
                bm_email: adminEmail,
                active: true 
            });

            return staffMember;

        } catch (error) {
            console.error('Error finding staff by BattleMetrics email:', error);
            return null;
        }
    }

    /**
     * Record a ban issued by a staff member
     */
    async recordStaffBan(staffMember, ban, playerData, serverData, steamId, banDetails) {
        try {
            const banAttributes = ban.attributes;
            
            // Create staff ban activity record
            const staffBanActivity = new StaffBanActivity({
                staffDiscordId: staffMember._id,
                banId: ban.id,
                playerBMId: ban.relationships?.user?.data?.id || 'Unknown',
                playerSteamId: steamId,
                playerName: playerData?.attributes?.name || 'Unknown',
                reason: banAttributes.reason || 'No reason provided',
                serverName: serverData?.attributes?.name || 'Unknown Server',
                serverId: ban.relationships?.server?.data?.id || 'Unknown',
                banNote: banAttributes.note || '',
                expiresAt: banAttributes.expires ? new Date(banAttributes.expires) : null,
                issuedAt: new Date(banAttributes.timestamp),
                processed: true
            });

            await staffBanActivity.save();

            // Update staff activity statistics
            await this.updateStaffStats(staffMember._id);

            // Send notification
            await this.sendStaffBanNotification(staffMember, staffBanActivity, playerData);

            console.log(`Successfully recorded ban ${ban.id} for staff member ${staffMember.discord_name}`);

        } catch (error) {
            console.error('Error recording staff ban:', error);
        }
    }

    /**
     * Update staff statistics for ban activity
     */
    async updateStaffStats(staffDiscordId) {
        try {
            // Update shared stats
            await SharedStaffStats.updateBanStats(staffDiscordId);
            
            // Update legacy staff activity model
            await StaffActivity.findOneAndUpdate(
                { _id: staffDiscordId },
                {
                    $inc: {
                        'bans.total': 1,
                        'bans.weekly': 1
                    },
                    $set: {
                        lastUpdated: new Date()
                    }
                },
                { upsert: true }
            );

        } catch (error) {
            console.error('Error updating staff stats:', error);
        }
    }

    /**
     * Send notification about staff ban activity
     */
    async sendStaffBanNotification(staffMember, banActivity, playerData) {
        try {
            const config = this.client.config;
            const webhookUrl = config.Webhooks?.BanStaffWebhook;
            
            if (!webhookUrl) {
                return; // No webhook configured
            }

            const embed = new EmbedBuilder()
                .setTitle('🔨 Staff Ban Tracked')
                .setColor('#FF6B6B')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffMember._id}>`, inline: true },
                    { name: 'Player', value: banActivity.playerName, inline: true },
                    { name: 'Server', value: banActivity.serverName, inline: true },
                    { name: 'Reason', value: banActivity.reason, inline: false },
                    { name: 'Steam ID', value: banActivity.playerSteamId, inline: true },
                    { name: 'Ban ID', value: banActivity.banId, inline: true },
                    { name: 'Attribution Method', value: 'BattleMetrics Email', inline: true }
                ])
                .setTimestamp(banActivity.issuedAt)
                .setFooter({ text: 'Staff Ban Tracking System' });

            if (banActivity.banNote) {
                embed.addFields([{ name: 'Note', value: banActivity.banNote, inline: false }]);
            }

            const webhook = new WebhookClient({ url: webhookUrl });
            await webhook.send({ embeds: [embed] });

        } catch (error) {
            console.error('Error sending staff ban notification:', error);
        }
    }

    /**
     * Helper method to get player data from included array
     */
    getPlayerFromIncluded(playerId, includedData) {
        if (!playerId || !includedData) return null;
        return includedData.find(item => item.type === 'player' && item.id === playerId);
    }

    /**
     * Helper method to get server data from included array
     */
    getServerFromIncluded(serverId, includedData) {
        if (!serverId || !includedData) return null;
        return includedData.find(item => item.type === 'server' && item.id === serverId);
    }

    /**
     * Get ban statistics for a staff member
     */
    async getStaffBanStats(staffDiscordId, timeframe = 'all') {
        try {
            const query = { staffDiscordId };
            
            if (timeframe === 'week') {
                const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
                query.issuedAt = { $gte: weekAgo };
            } else if (timeframe === 'month') {
                const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
                query.issuedAt = { $gte: monthAgo };
            }

            const bans = await StaffBanActivity.find(query).sort({ issuedAt: -1 });
            
            return {
                total: bans.length,
                bans: bans
            };

        } catch (error) {
            console.error('Error getting staff ban stats:', error);
            return { total: 0, bans: [] };
        }
    }

    /**
     * Get all staff ban statistics
     */
    async getAllStaffBanStats() {
        try {
            const staffMembers = await StaffMember.find({ active: true });
            const statsPromises = staffMembers.map(async (staff) => {
                const stats = await this.getStaffBanStats(staff._id);
                return {
                    staffId: staff._id,
                    staffName: staff.discord_name,
                    totalBans: stats.total,
                    recentBans: stats.bans.slice(0, 5)
                };
            });

            const allStats = await Promise.all(statsPromises);
            return allStats.filter(stats => stats.totalBans > 0);

        } catch (error) {
            console.error('Error getting all staff ban stats:', error);
            return [];
        }
    }
}

module.exports = StaffBanTracker;
