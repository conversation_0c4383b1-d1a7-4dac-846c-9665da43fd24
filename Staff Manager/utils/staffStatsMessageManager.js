const StaffStatsMessage = require('../models/staffStatsMessage');

/**
 * Manager for staff statistics messages
 * Handles updating existing messages and cleanup
 */
class StaffStatsMessageManager {
    constructor(client) {
        this.client = client;
        this.cleanupInterval = null;
    }

    /**
     * Start automatic cleanup of old message records
     */
    startCleanupTask() {
        if (this.cleanupInterval) {
            return;
        }

        // Run cleanup every 6 hours
        this.cleanupInterval = setInterval(async () => {
            try {
                await this.cleanupOldRecords();
            } catch (error) {
                console.error('Error in staff stats message cleanup:', error);
            }
        }, 6 * 60 * 60 * 1000); // 6 hours

        console.log('Staff stats message cleanup task started');
    }

    /**
     * Stop automatic cleanup
     */
    stopCleanupTask() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
            console.log('Staff stats message cleanup task stopped');
        }
    }

    /**
     * Clean up old message records (older than 7 days)
     */
    async cleanupOldRecords() {
        try {
            const result = await StaffStatsMessage.cleanupOldMessages(7);
            if (result.deletedCount > 0) {
                console.log(`Cleaned up ${result.deletedCount} old staff stats message records`);
            }
            return result;
        } catch (error) {
            console.error('Error cleaning up old staff stats message records:', error);
            throw error;
        }
    }

    /**
     * Update or create a staff stats message
     */
    async updateStatsMessage(staffDiscordId, channelId, embed, components, requestedBy) {
        try {
            const guild = this.client.guilds.cache.get(this.client.config.BotSettings.GuildID);
            if (!guild) {
                throw new Error('Guild not found');
            }

            const channel = guild.channels.cache.get(channelId);
            if (!channel) {
                throw new Error('Channel not found');
            }

            // Check for existing message
            const existingRecord = await StaffStatsMessage.getStatsMessage(staffDiscordId, channelId);
            
            if (existingRecord) {
                try {
                    // Try to update existing message
                    const existingMessage = await channel.messages.fetch(existingRecord.messageId);
                    await existingMessage.edit({ embeds: [embed], components: components || [] });
                    
                    // Update database record
                    await StaffStatsMessage.saveStatsMessage(staffDiscordId, channelId, existingRecord.messageId, requestedBy);
                    
                    return {
                        success: true,
                        updated: true,
                        messageId: existingRecord.messageId
                    };
                } catch (error) {
                    console.log(`Could not update existing message, will create new one:`, error.message);
                    // Fall through to create new message
                }
            }

            // Create new message
            const newMessage = await channel.send({ embeds: [embed], components: components || [] });
            
            // Save to database
            await StaffStatsMessage.saveStatsMessage(staffDiscordId, channelId, newMessage.id, requestedBy);
            
            return {
                success: true,
                updated: false,
                messageId: newMessage.id
            };

        } catch (error) {
            console.error('Error updating staff stats message:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Remove all staff stats messages from a channel
     */
    async cleanupChannel(channelId) {
        try {
            const guild = this.client.guilds.cache.get(this.client.config.BotSettings.GuildID);
            if (!guild) {
                throw new Error('Guild not found');
            }

            const channel = guild.channels.cache.get(channelId);
            if (!channel) {
                throw new Error('Channel not found');
            }

            // Get all stats messages in this channel
            const statsMessages = await StaffStatsMessage.getChannelStatsMessages(channelId);
            
            let deletedCount = 0;
            let failedCount = 0;

            // Delete each message
            for (const statsMessage of statsMessages) {
                try {
                    const message = await channel.messages.fetch(statsMessage.messageId);
                    await message.delete();
                    deletedCount++;
                } catch (error) {
                    console.log(`Failed to delete message ${statsMessage.messageId}:`, error.message);
                    failedCount++;
                }
            }

            // Remove all records from database
            await StaffStatsMessage.deleteMany({ channelId });

            return {
                success: true,
                deletedCount,
                failedCount,
                totalFound: statsMessages.length
            };

        } catch (error) {
            console.error('Error cleaning up channel:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get statistics about staff stats messages
     */
    async getStats() {
        try {
            const totalMessages = await StaffStatsMessage.countDocuments();
            const recentMessages = await StaffStatsMessage.countDocuments({
                lastUpdated: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
            });

            return {
                totalMessages,
                recentMessages,
                oldMessages: totalMessages - recentMessages
            };
        } catch (error) {
            console.error('Error getting stats:', error);
            return {
                totalMessages: 0,
                recentMessages: 0,
                oldMessages: 0
            };
        }
    }
}

module.exports = StaffStatsMessageManager;
