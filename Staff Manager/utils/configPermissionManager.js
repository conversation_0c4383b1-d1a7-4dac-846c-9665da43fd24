/**
 * Config-based Permission Manager
 * Handles permission levels defined in the config file instead of database
 */
class ConfigPermissionManager {
    constructor(client) {
        this.client = client;
    }

    /**
     * Get all permission levels from config
     */
    getAllPermissionLevels() {
        const permissionLevels = this.client.config.StaffManagement?.PermissionLevels;
        if (!permissionLevels) {
            console.warn('No permission levels configured in StaffManagement.PermissionLevels');
            return [];
        }

        // Convert config object to array with names
        return Object.entries(permissionLevels).map(([name, config]) => ({
            name,
            ...config
        })).sort((a, b) => (b.priority || 0) - (a.priority || 0));
    }

    /**
     * Get permission level by name
     */
    getPermissionLevel(name) {
        const permissionLevels = this.client.config.StaffManagement?.PermissionLevels;
        if (!permissionLevels || !permissionLevels[name]) {
            return null;
        }

        return {
            name,
            ...permissionLevels[name]
        };
    }

    /**
     * Get permission levels for select menu options
     */
    getSelectMenuOptions() {
        const levels = this.getAllPermissionLevels();

        return levels.map(level => ({
            label: level.displayName || level.name,
            value: level.name,
            description: (level.description || `Priority: ${level.priority || 0}`).substring(0, 100),
            emoji: this.getPriorityEmoji(level.priority || 0)
        }));
    }

    /**
     * Get emoji based on priority level
     */
    getPriorityEmoji(priority) {
        if (priority >= 100) return '👑'; // Admin
        if (priority >= 50) return '🛡️';  // Moderator
        return '👤'; // Basic
    }

    /**
     * Get onload commands for a permission level
     */
    getOnloadCommands(levelName) {
        const level = this.getPermissionLevel(levelName);
        return level?.onloadCommands || [];
    }

    /**
     * Get offload commands for a permission level
     */
    getOffloadCommands(levelName) {
        const level = this.getPermissionLevel(levelName);
        return level?.offloadCommands || [];
    }

    // Discord roles removed - this system focuses on RCON server permissions

    /**
     * Check if a permission level has a specific permission
     */
    hasPermission(levelName, permission) {
        const level = this.getPermissionLevel(levelName);
        return level?.permissions?.[permission] || false;
    }

    /**
     * Get color for a permission level
     */
    getColor(levelName) {
        const level = this.getPermissionLevel(levelName);
        return level?.color || '#CCCCCC';
    }

    /**
     * Validate permission level configuration
     */
    validatePermissionLevel(levelName) {
        const level = this.getPermissionLevel(levelName);
        if (!level) {
            return { valid: false, errors: ['Permission level not found'] };
        }

        const errors = [];

        // Check required fields
        if (!level.displayName) errors.push('Missing displayName');
        if (typeof level.priority !== 'number') errors.push('Missing or invalid priority');

        // Validate commands
        if (level.onloadCommands) {
            level.onloadCommands.forEach((cmd, index) => {
                if (!cmd.title) errors.push(`Onload command ${index + 1}: Missing title`);
                if (!cmd.command) errors.push(`Onload command ${index + 1}: Missing command`);
            });
        }

        if (level.offloadCommands) {
            level.offloadCommands.forEach((cmd, index) => {
                if (!cmd.title) errors.push(`Offload command ${index + 1}: Missing title`);
                if (!cmd.command) errors.push(`Offload command ${index + 1}: Missing command`);
            });
        }

        // Discord roles validation removed - focusing on RCON commands

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Get permission level statistics
     */
    getPermissionLevelStats() {
        const levels = this.getAllPermissionLevels();

        return {
            totalLevels: levels.length,
            highestPriority: Math.max(...levels.map(l => l.priority || 0)),
            lowestPriority: Math.min(...levels.map(l => l.priority || 0)),
            // Discord roles removed - focusing on RCON commands
            levelsWithCommands: levels.filter(l => l.onloadCommands && l.onloadCommands.length > 0).length
        };
    }

    // Discord role-based permission detection removed
    // Staff permission levels are now managed through RCON server commands only

    /**
     * Process command with Steam ID placeholder
     */
    processCommand(command, steamId) {
        return command.replace(/{steam_id}/g, steamId);
    }

    /**
     * Get formatted command list for display
     */
    getFormattedCommandList(commands, steamId = '{steam_id}') {
        if (!commands || commands.length === 0) {
            return 'No commands configured';
        }

        return commands.map(cmd => {
            const processedCommand = this.processCommand(cmd.command, steamId);
            return `• **${cmd.title}**: \`${processedCommand}\``;
        }).join('\n');
    }

    /**
     * Validate all permission levels in config
     */
    validateAllPermissionLevels() {
        const levels = this.getAllPermissionLevels();
        const results = {};

        for (const level of levels) {
            results[level.name] = this.validatePermissionLevel(level.name);
        }

        return results;
    }

    /**
     * Get permission level summary for display
     */
    getPermissionLevelSummary(levelName) {
        const level = this.getPermissionLevel(levelName);
        if (!level) return null;

        return {
            name: level.name,
            displayName: level.displayName || level.name,
            priority: level.priority || 0,
            description: level.description || 'No description',
            color: level.color || '#CCCCCC',
            // Discord roles removed
            onloadCommandCount: (level.onloadCommands || []).length,
            offloadCommandCount: (level.offloadCommands || []).length,
            permissions: Object.entries(level.permissions || {})
                .filter(([key, value]) => value)
                .map(([key]) => key)
        };
    }
}

module.exports = ConfigPermissionManager;
