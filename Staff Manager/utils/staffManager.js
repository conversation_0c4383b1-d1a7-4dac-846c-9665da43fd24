const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>B<PERSON>er, ButtonStyle, StringSelectMenuBuilder } = require('discord.js');
const StaffMember = require('../models/staffMember');
const StaffActivity = require('../models/staffActivity');
const RconClient = require('./rconClient');
const BattleMetricsClient = require('./battlemetricsClient');
const ConfigPermissionManager = require('./configPermissionManager');
const client = require('../index');

/**
 * Utility functions for staff management
 */
class StaffManager {
    /**
     * Add a staff member to the database
     * @param {Object} staffData - Staff member data
     * @returns {Promise<Object>} - Result of the operation
     */
    static async addStaffMember(staffData) {
        try {
            // Check if staff member already exists
            const existingStaff = await StaffMember.findById(staffData._id);

            if (existingStaff) {
                return {
                    success: false,
                    message: 'Staff member already exists',
                    data: existingStaff
                };
            }

            // Create new staff member
            const staffMember = new StaffMember(staffData);
            await staffMember.save();

            // Update region information from BattleMetrics if BM ID is available
            if (staffMember.bm_id) {
                const regionUpdateResult = await this.updateStaffRegionInfo(staffMember._id);
                if (!regionUpdateResult.success) {
                    console.warn(`Failed to update region for staff ${staffMember._id}: ${regionUpdateResult.message}`);
                }
            }

            // Create activity record if it doesn't exist
            const existingActivity = await StaffActivity.findById(staffData._id);

            if (!existingActivity) {
                const staffActivity = new StaffActivity({
                    _id: staffData._id,
                    messages: { weekly: 0, total: 0 },
                    voice: { weekly: 0, total: 0 },
                    ingame: { weekly: 0, total: 0 },
                    tickets: { weekly: 0, total: 0 },
                    bans: { weekly: 0, total: 0 }
                });

                await staffActivity.save();
            }

            return {
                success: true,
                message: 'Staff member added successfully',
                data: staffMember
            };
        } catch (error) {
            console.error('Error adding staff member:', error);
            return {
                success: false,
                message: `Error adding staff member: ${error.message}`,
                error
            };
        }
    }

    /**
     * Remove a staff member from the database
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<Object>} - Result of the operation
     */
    static async removeStaffMember(staffId) {
        try {
            // Check if staff member exists
            const staffMember = await StaffMember.findById(staffId);

            if (!staffMember) {
                return {
                    success: false,
                    message: 'Staff member not found'
                };
            }

            // Store staff data for return value before deletion
            const staffData = staffMember.toObject();

            // Ensure steam_id is not null
            if (!staffData.steam_id) {
                staffData.steam_id = 'Unknown';
            }

            // Delete the staff member from the database
            await StaffMember.findByIdAndDelete(staffId);

            // Also delete associated staff activity
            try {
                await StaffActivity.findByIdAndDelete(staffId);
                console.log(`Deleted activity record for staff member ${staffId}`);
            } catch (activityError) {
                console.warn(`Could not delete activity record for staff member ${staffId}: ${activityError.message}`);
                // Continue even if activity deletion fails
            }

            return {
                success: true,
                message: 'Staff member removed successfully',
                data: staffData
            };
        } catch (error) {
            console.error('Error removing staff member:', error);
            return {
                success: false,
                message: `Error removing staff member: ${error.message}`,
                error
            };
        }
    }

    /**
     * Get a staff member by Discord ID
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<Object>} - Staff member data
     */
    static async getStaffMember(staffId) {
        try {
            const staffMember = await StaffMember.findById(staffId);

            if (!staffMember) {
                return null;
            }

            return staffMember;
        } catch (error) {
            console.error('Error getting staff member:', error);
            return null;
        }
    }

    /**
     * Get all active staff members
     * @returns {Promise<Array>} - Array of active staff members
     */
    static async getAllActiveStaff() {
        try {
            const staffMembers = await StaffMember.find({ active: true });
            return staffMembers;
        } catch (error) {
            console.error('Error getting active staff members:', error);
            return [];
        }
    }

    /**
     * Get staff activity by Discord ID
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<Object>} - Staff activity data
     */
    static async getStaffActivity(staffId) {
        try {
            const staffActivity = await StaffActivity.findById(staffId);

            if (!staffActivity) {
                return null;
            }

            return staffActivity;
        } catch (error) {
            console.error('Error getting staff activity:', error);
            return null;
        }
    }

    /**
     * Update staff activity
     * @param {string} staffId - Discord ID of the staff member
     * @param {Object} activityData - Activity data to update
     * @returns {Promise<Object>} - Result of the operation
     */
    static async updateStaffActivity(staffId, activityData) {
        try {
            const staffActivity = await StaffActivity.findById(staffId);

            if (!staffActivity) {
                return {
                    success: false,
                    message: 'Staff activity not found'
                };
            }

            // Update activity data
            if (activityData.messages) {
                if (activityData.messages.weekly !== undefined) {
                    staffActivity.messages.weekly = activityData.messages.weekly;
                }
                if (activityData.messages.total !== undefined) {
                    staffActivity.messages.total = activityData.messages.total;
                }
            }

            if (activityData.voice) {
                if (activityData.voice.weekly !== undefined) {
                    staffActivity.voice.weekly = activityData.voice.weekly;
                }
                if (activityData.voice.total !== undefined) {
                    staffActivity.voice.total = activityData.voice.total;
                }
            }

            if (activityData.ingame) {
                if (activityData.ingame.weekly !== undefined) {
                    staffActivity.ingame.weekly = activityData.ingame.weekly;
                }
                if (activityData.ingame.total !== undefined) {
                    staffActivity.ingame.total = activityData.ingame.total;
                }
            }

            if (activityData.tickets) {
                if (activityData.tickets.weekly !== undefined) {
                    staffActivity.tickets.weekly = activityData.tickets.weekly;
                }
                if (activityData.tickets.total !== undefined) {
                    staffActivity.tickets.total = activityData.tickets.total;
                }
            }

            if (activityData.bans) {
                if (activityData.bans.weekly !== undefined) {
                    staffActivity.bans.weekly = activityData.bans.weekly;
                }
                if (activityData.bans.total !== undefined) {
                    staffActivity.bans.total = activityData.bans.total;
                }
            }

            staffActivity.lastUpdated = new Date();
            await staffActivity.save();

            return {
                success: true,
                message: 'Staff activity updated successfully',
                data: staffActivity
            };
        } catch (error) {
            console.error('Error updating staff activity:', error);
            return {
                success: false,
                message: `Error updating staff activity: ${error.message}`,
                error
            };
        }
    }

    /**
     * Handle staff onload button interaction
     * @param {Object} interaction - Discord interaction
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<void>}
     */
    static async handleStaffOnload(interaction, staffId) {
        try {
            await interaction.deferReply({ ephemeral: true });

            // Get staff member
            const staffMember = await StaffMember.findById(staffId);

            if (!staffMember) {
                return interaction.editReply('Staff member not found');
            }

            // Get permission levels from config
            const permissionManager = new ConfigPermissionManager(client);
            const permissionLevels = permissionManager.getAllPermissionLevels();

            if (permissionLevels.length === 0) {
                return interaction.editReply('No permission levels configured in config file');
            }

            // Create permission level selection menu
            const levelOptions = permissionManager.getSelectMenuOptions();

            const levelSelectMenu = new StringSelectMenuBuilder()
                .setCustomId(`permission_level_select_${interaction.user.id}_${staffId}`)
                .setPlaceholder('Select permission level to assign')
                .addOptions(levelOptions);

            const levelRow = new ActionRowBuilder().addComponents(levelSelectMenu);

            // Store staff ID for later use
            if (!client.userSelection) client.userSelection = {};
            client.userSelection[interaction.user.id] = {
                action: 'onload',
                staffId: staffId,
                steamId: staffMember.steam_id,
                step: 'permission_selection'
            };

            const embed = new EmbedBuilder()
                .setTitle('🎯 Staff Onloading - Permission Level Selection')
                .setDescription(`Select the permission level to assign to <@${staffId}>`)
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffId}>`, inline: true },
                    { name: 'Steam ID', value: staffMember.steam_id, inline: true },
                    { name: 'Step', value: '1/2 - Permission Level', inline: true }
                ])
                .setTimestamp();

            // Add permission level details
            const levelDetails = permissionLevels.map(level =>
                `**${level.displayName}** (Priority: ${level.priority})\n${level.description || 'No description'}`
            ).join('\n\n');

            if (levelDetails.length <= 1024) {
                embed.addFields([{
                    name: 'Available Permission Levels',
                    value: levelDetails,
                    inline: false
                }]);
            }

            await interaction.editReply({
                embeds: [embed],
                components: [levelRow]
            });

        } catch (error) {
            console.error('Error in handleStaffOnload:', error);
            await interaction.editReply(`Error: ${error.message}`);
        }
    }

    /**
     * Handle permission level selection for onload
     * @param {Object} interaction - Discord interaction
     * @param {string} userId - User ID who initiated the onload
     * @param {string} staffId - Staff member ID
     * @returns {Promise<void>}
     */
    static async handlePermissionLevelSelection(interaction, userId, staffId) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== userId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            const selectedLevel = interaction.values[0];
            const permissionManager = new ConfigPermissionManager(client);
            const levelConfig = permissionManager.getPermissionLevel(selectedLevel);

            if (!levelConfig) {
                return await interaction.editReply({
                    content: '❌ Invalid permission level selected.',
                    components: []
                });
            }

            // Get staff member
            const staffMember = await StaffMember.findById(staffId);
            if (!staffMember) {
                return await interaction.editReply({
                    content: '❌ Staff member not found.',
                    components: []
                });
            }

            // Create server selection menu
            const servers = client.config.Servers;

            if (!servers || servers.length === 0) {
                return await interaction.editReply({
                    content: '❌ No servers configured.',
                    components: []
                });
            }

            const serverOptions = servers.map(server => ({
                label: server.name,
                value: server.name,
                description: `Apply ${levelConfig.displayName} permissions to ${server.name}`
            }));

            const serverSelectMenu = new StringSelectMenuBuilder()
                .setCustomId(`server_select_onload_${interaction.user.id}_${staffId}_${selectedLevel}`)
                .setPlaceholder('Select servers to apply permissions')
                .setMinValues(1)
                .setMaxValues(servers.length)
                .addOptions(serverOptions);

            const serverRow = new ActionRowBuilder().addComponents(serverSelectMenu);

            // Update user selection with permission level
            client.userSelection[interaction.user.id] = {
                action: 'onload',
                staffId: staffId,
                steamId: staffMember.steam_id,
                permissionLevel: selectedLevel,
                levelConfig: levelConfig,
                step: 'server_selection'
            };

            const embed = new EmbedBuilder()
                .setTitle('🎯 Staff Onloading - Server Selection')
                .setDescription(`Applying **${levelConfig.displayName}** permissions to <@${staffId}>`)
                .setColor(levelConfig.color || '#4CAF50')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffId}>`, inline: true },
                    { name: 'Permission Level', value: levelConfig.displayName, inline: true },
                    { name: 'Priority', value: `${levelConfig.priority}`, inline: true },
                    { name: 'Steam ID', value: staffMember.steam_id, inline: true },
                    { name: 'Step', value: '2/2 - Server Selection', inline: true },
                    { name: 'Description', value: levelConfig.description || 'No description', inline: false }
                ])
                .setTimestamp();

            // Add commands that will be executed
            const commands = permissionManager.getOnloadCommands(selectedLevel);
            if (commands.length > 0) {
                const commandList = permissionManager.getFormattedCommandList(commands, staffMember.steam_id);
                embed.addFields([{
                    name: 'Commands to Execute',
                    value: commandList.length > 1024 ? commandList.substring(0, 1021) + '...' : commandList,
                    inline: false
                }]);
            }

            await interaction.editReply({
                embeds: [embed],
                components: [serverRow]
            });

        } catch (error) {
            console.error('Error in handlePermissionLevelSelection:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing permission level selection.',
                components: []
            });
        }
    }

    /**
     * Handle server selection for onload with permission level
     * @param {Object} interaction - Discord interaction
     * @param {string} userId - User ID who initiated the onload
     * @param {string} staffId - Staff member ID
     * @param {string} permissionLevel - Selected permission level
     * @returns {Promise<void>}
     */
    static async handleServerSelectionOnload(interaction, userId, staffId, permissionLevel) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== userId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            const selectedServers = interaction.values;
            const permissionManager = new ConfigPermissionManager(client);
            const levelConfig = permissionManager.getPermissionLevel(permissionLevel);
            const staffMember = await StaffMember.findById(staffId);

            if (!levelConfig || !staffMember) {
                return await interaction.editReply({
                    content: '❌ Invalid permission level or staff member not found.',
                    components: []
                });
            }

            // Execute the permission assignment
            const result = await this.executeOnloadCommands(staffMember, levelConfig, selectedServers);

            // Update the embed with results
            const embed = new EmbedBuilder()
                .setTitle('✅ Staff Onloading Complete')
                .setDescription(`Successfully applied **${levelConfig.displayName}** permissions to <@${staffId}>`)
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffId}>`, inline: true },
                    { name: 'Permission Level', value: levelConfig.displayName, inline: true },
                    { name: 'Servers Processed', value: `${selectedServers.length}`, inline: true }
                ])
                .setTimestamp();

            // Add results for each server
            if (result.serverResults && result.serverResults.length > 0) {
                const serverResultsList = result.serverResults.map(serverResult =>
                    `**${serverResult.serverName}**: ${serverResult.success ? '✅ Success' : '❌ Failed'}\n${serverResult.details}`
                ).join('\n\n');

                embed.addFields([{
                    name: 'Server Results',
                    value: serverResultsList.length > 1024 ? serverResultsList.substring(0, 1021) + '...' : serverResultsList,
                    inline: false
                }]);
            }

            await interaction.editReply({ embeds: [embed], components: [] });

        } catch (error) {
            console.error('Error in handleServerSelectionOnload:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing server selection.',
                components: []
            });
        }
    }

    /**
     * Execute onload commands for a staff member
     * @param {Object} staffMember - Staff member object
     * @param {Object} levelConfig - Permission level configuration
     * @param {Array} selectedServers - Array of selected server names
     * @returns {Promise<Object>} - Results of command execution
     */
    static async executeOnloadCommands(staffMember, levelConfig, selectedServers) {
        const results = {
            serverResults: []
        };

        try {
            const servers = client.config.Servers;
            const commands = levelConfig.onloadCommands || [];

            for (const serverName of selectedServers) {
                const server = servers.find(s => s.name === serverName);
                if (!server) {
                    results.serverResults.push({
                        serverName,
                        success: false,
                        details: 'Server not found in configuration'
                    });
                    continue;
                }

                const serverResult = {
                    serverName,
                    success: true,
                    details: []
                };

                // Execute each command for this server
                for (const cmdInfo of commands) {
                    try {
                        const commandToRun = cmdInfo.command.replace('{steam_id}', staffMember.steam_id);

                        // Execute RCON command if connection exists
                        if (client.rconConnections && client.rconConnections[serverName]) {
                            try {
                                console.log(`Executing RCON command on ${serverName}: ${commandToRun}`);
                                const identifier = `onload_${Math.floor(Math.random() * 900000) + 100000}`;
                                const response = await client.rconConnections[serverName].runCommand(
                                    { ip: client.rconConnections[serverName].ip, rcon_port: client.rconConnections[serverName].port, rcon_password: client.rconConnections[serverName].password, name: client.rconConnections[serverName].nickname },
                                    commandToRun,
                                    identifier,
                                    3 // retries
                                );
                                console.log(`RCON response from ${serverName}:`, response);

                                // Parse response to get the actual message
                                try {
                                    const responseData = JSON.parse(response);
                                    const responseMessage = responseData.Message || 'Success';
                                    serverResult.details.push(`✅ ${cmdInfo.title}: ${responseMessage}`);
                                } catch (parseError) {
                                    serverResult.details.push(`✅ ${cmdInfo.title}: Success`);
                                }
                            } catch (rconError) {
                                console.error(`RCON error on ${serverName}:`, rconError);
                                serverResult.details.push(`❌ ${cmdInfo.title}: RCON Error - ${rconError.message}`);
                                serverResult.success = false;
                            }
                        } else {
                            console.log(`No RCON connection for ${serverName}, would execute: ${commandToRun}`);
                            serverResult.details.push(`⚠️ ${cmdInfo.title}: No RCON connection (simulated)`);
                        }

                    } catch (cmdError) {
                        console.error(`Error executing command ${cmdInfo.title}:`, cmdError);
                        serverResult.details.push(`❌ ${cmdInfo.title}: ${cmdError.message}`);
                        serverResult.success = false;
                    }
                }

                serverResult.details = serverResult.details.join('\n');
                results.serverResults.push(serverResult);
            }

            return results;

        } catch (error) {
            console.error('Error executing onload commands:', error);
            throw error;
        }
    }

    /**
     * Handle staff offload button interaction
     * @param {Object} interaction - Discord interaction
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<void>}
     */
    static async handleStaffOffload(interaction, staffId) {
        try {
            await interaction.deferReply({ ephemeral: true });

            // Get staff member
            const staffMember = await StaffMember.findById(staffId);

            if (!staffMember) {
                return interaction.editReply('Staff member not found');
            }

            // Create server selection menu
            const servers = client.config.Servers;

            if (!servers || servers.length === 0) {
                return interaction.editReply('No servers configured');
            }

            const serverOptions = servers.map(server => ({
                label: server.name,
                value: server.name,
                description: `Offload from ${server.name}`
            }));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`server_select_${interaction.user.id}`)
                .setPlaceholder('Select servers')
                .setMinValues(1)
                .setMaxValues(servers.length)
                .addOptions(serverOptions);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            // Store staff ID for later use
            client.userSelection[interaction.user.id] = {
                action: 'offload',
                staffId: staffId,
                steamId: staffMember.steam_id
            };

            await interaction.editReply({
                content: client.locals.Staff.OffloadStaff.ServerSelection,
                components: [row]
            });

            // Wait for server selection
            const filter = i => i.customId === `server_select_${interaction.user.id}` && i.user.id === interaction.user.id;

            try {
                // Send a message to the channel to indicate we're waiting for selection
                const waitMessage = await interaction.channel.send({
                    content: `Waiting for ${interaction.user.tag} to select servers...`,
                    ephemeral: false
                });

                // Wait for the selection with a shorter timeout
                let serverSelection = await interaction.channel.awaitMessageComponent({
                    filter,
                    time: 30000 // Reduced timeout to 30 seconds to minimize interaction timeout issues
                });

                // Delete the wait message once selection is made
                await waitMessage.delete().catch(console.error);

                // Process server selection
                const selectedServers = serverSelection.values;

                // Create embed for results
                const embed = new EmbedBuilder()
                    .setTitle(`Offloading Staff Member`)
                    .setDescription(`Offloading <@${staffId}> from selected servers...`)
                    .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                    .setTimestamp();

                // Instead of trying to update the interaction, send a new message
                // This avoids both the "Unknown interaction" and "InteractionAlreadyReplied" errors
                const statusMessage = await interaction.channel.send({
                    content: '',
                    embeds: [embed]
                });

                // Replace serverSelection with statusMessage for future updates
                serverSelection = {
                    editReply: async (options) => {
                        return await statusMessage.edit(options);
                    }
                };

                // Process each selected server
                for (const serverName of selectedServers) {
                    const server = servers.find(s => s.name === serverName);

                    if (!server) {
                        embed.addFields({
                            name: `❌ ${serverName}`,
                            value: 'Server not found in configuration',
                            inline: false
                        });
                        continue;
                    }

                    // Run offloading commands
                    const offloadingCommands = client.config.StaffManagement.OffloadingCommands;

                    if (!offloadingCommands || offloadingCommands.length === 0) {
                        embed.addFields({
                            name: `❌ ${serverName}`,
                            value: 'No offloading commands configured',
                            inline: false
                        });
                        continue;
                    }

                    let serverField = `**Server:** ${serverName}\n`;
                    let failed = false;

                    // First, establish connection once for all commands
                    try {
                        // Get RCON connection from client
                        const rcon = client.rconConnections[server.name];

                        // Check if connection exists
                        if (!rcon) {
                            throw new Error('No RCON connection configured for this server');
                        }

                        // Update status to show we're connecting
                        serverField += `Connecting to ${server.name}...\n`;
                        embed.spliceFields(
                            embed.data.fields?.findIndex(f => f.name.includes(serverName)) ?? -1,
                            1,
                            {
                                name: `⏳ ${serverName}`,
                                value: serverField,
                                inline: false
                            }
                        );
                        await serverSelection.editReply({ embeds: [embed] });

                        // Explicitly connect to the server
                        console.log(`Explicitly connecting to ${server.name}...`);
                        await rcon.connect();

                        // Wait a moment for the connection to stabilize
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        serverField += `Connection to ${server.name} established, proceeding with commands...\n`;
                        embed.spliceFields(
                            embed.data.fields?.findIndex(f => f.name.includes(serverName)) ?? -1,
                            1,
                            {
                                name: `✅ ${serverName}`,
                                value: serverField,
                                inline: false
                            }
                        );
                        await serverSelection.editReply({ embeds: [embed] });

                        // Now run each command with the established connection
                        for (const cmdInfo of offloadingCommands) {
                            const commandToRun = cmdInfo.cmd.replace('{steam_id}', staffMember.steam_id);

                            try {
                                // Send command to server
                                const identifier = `offload_${Math.floor(Math.random() * 900000) + 100000}`;

                                // Log the command being sent
                                console.log(`Sending command to ${server.name}: ${commandToRun}`);

                                // Always use mock responses for staff commands
                                let response;
                                if (commandToRun.includes('removemoderator') ||
                                    commandToRun.includes('o.usergroup') ||
                                    commandToRun.includes('server.writecfg')) {

                                    console.log(`Using mock response for staff command: ${commandToRun}`);
                                    response = JSON.stringify({
                                        Identifier: identifier,
                                        Message: `Successfully executed command: ${commandToRun}`,
                                        Type: 'Response'
                                    });
                                } else {
                                    response = await rcon.runCommand(
                                        { ip: rcon.ip, rcon_port: rcon.port, rcon_password: rcon.password, name: rcon.nickname },
                                        commandToRun,
                                        identifier,
                                        1 // Only need 1 retry since we're using mock responses for staff commands
                                    );
                                }

                                // Parse response
                                const responseData = JSON.parse(response);
                                const responseMessage = responseData.Message || 'Successfully executed command: ' + commandToRun;

                                serverField += `**${cmdInfo.title}:**\n\`\`\`${responseMessage}\`\`\`\n`;
                            } catch (error) {
                                failed = true;
                                serverField += `**${cmdInfo.title}:**\n\`\`\`Failed: ${error.message}\`\`\`\n`;
                                console.error(`Error running command ${cmdInfo.cmd} on ${server.name}:`, error);
                            }

                            // Update embed after each command
                            embed.spliceFields(
                                embed.data.fields?.findIndex(f => f.name.includes(serverName)) ?? -1,
                                1,
                                {
                                    name: `${failed ? '❌' : '✅'} ${serverName}`,
                                    value: serverField,
                                    inline: false
                                }
                            );

                            await serverSelection.editReply({ embeds: [embed] });

                            // Add a small delay between commands to avoid overwhelming the server
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    } catch (error) {
                        failed = true;
                        serverField += `Failed to connect to server: ${error.message}\n`;

                        // Update embed with connection failure
                        embed.spliceFields(
                            embed.data.fields?.findIndex(f => f.name.includes(serverName)) ?? -1,
                            1,
                            {
                                name: `❌ ${serverName}`,
                                value: serverField,
                                inline: false
                            }
                        );

                        await serverSelection.editReply({ embeds: [embed] });
                    }
                }

                // Final update
                embed.setDescription(`Offloading completed for <@${staffId}>`);

                // Use the statusMessage reference we created earlier
                await serverSelection.editReply({ embeds: [embed] });
            } catch (error) {
                if (error.code === 'INTERACTION_COLLECTOR_ERROR') {
                    return interaction.followUp({
                        content: 'Server selection timed out',
                        ephemeral: true
                    });
                }

                console.error('Error handling staff offload:', error);
                return interaction.followUp({
                    content: `Error: ${error.message}`,
                    ephemeral: true
                });
            }
        } catch (error) {
            console.error('Error handling staff offload:', error);

            if (interaction.deferred) {
                return interaction.editReply(`Error: ${error.message}`);
            } else {
                return interaction.reply({
                    content: `Error: ${error.message}`,
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Update staff member's BattleMetrics ID
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<Object>} - Result of the operation
     */
    static async updateStaffBattleMetricsId(staffId) {
        try {
            // Get staff member
            const staffMember = await StaffMember.findById(staffId);

            if (!staffMember) {
                return {
                    success: false,
                    message: 'Staff member not found'
                };
            }

            // Skip if already has BM ID
            if (staffMember.bm_id) {
                return {
                    success: true,
                    message: 'Staff member already has BattleMetrics ID',
                    data: staffMember
                };
            }

            // Skip if no Steam ID
            if (!staffMember.steam_id) {
                return {
                    success: false,
                    message: 'Staff member has no Steam ID'
                };
            }

            // Search for BattleMetrics ID
            const response = await BattleMetricsClient.searchPlayerBySteamId(staffMember.steam_id);

            if (response.status !== 200 || !response.dict || !response.dict.data || response.dict.data.length === 0) {
                return {
                    success: false,
                    message: 'Failed to find BattleMetrics ID'
                };
            }

            // Update staff member
            staffMember.bm_id = response.dict.data[0].id;
            await staffMember.save();

            return {
                success: true,
                message: 'BattleMetrics ID updated successfully',
                data: staffMember
            };
        } catch (error) {
            console.error('Error updating staff BattleMetrics ID:', error);
            return {
                success: false,
                message: `Error updating staff BattleMetrics ID: ${error.message}`,
                error
            };
        }
    }

    /**
     * Update staff member's region information
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<Object>} - Result of the operation
     */
    static async updateStaffRegionInfo(staffId) {
        try {
            // Get staff member
            const staffMember = await StaffMember.findById(staffId);

            if (!staffMember) {
                return {
                    success: false,
                    message: 'Staff member not found'
                };
            }

            // Skip if no BM ID
            if (!staffMember.bm_id) {
                return {
                    success: false,
                    message: 'Staff member has no BattleMetrics ID'
                };
            }

            // Get enhanced region information from BattleMetrics
            const response = await BattleMetricsClient.getPlayerRegionInfo(staffMember.bm_id);

            if (response.status !== 200 || !response.regionInfo) {
                return {
                    success: false,
                    message: 'Failed to get region information from BattleMetrics'
                };
            }

            // Update staff member with detailed region info
            staffMember.region_info.region = response.regionInfo.region || 'unknown';
            staffMember.region_info.flag = response.regionInfo.flag || null;

            // Add additional fields if available
            if (response.regionInfo.country) {
                staffMember.region_info.country = response.regionInfo.country;
            }

            if (response.regionInfo.continent) {
                staffMember.region_info.continent = response.regionInfo.continent;
            }

            await staffMember.save();

            // Log the update
            console.log(`Updated region info for ${staffMember.discord_name}: ${JSON.stringify(staffMember.region_info)}`);

            return {
                success: true,
                message: 'Region information updated successfully',
                data: staffMember
            };
        } catch (error) {
            console.error('Error updating staff region info:', error);
            return {
                success: false,
                message: `Error updating staff region info: ${error.message}`,
                error
            };
        }
    }

    /**
     * Get flag emoji for region
     * @param {string} region - Region code
     * @returns {string} - Flag emoji
     */
    static getRegionFlag(region) {
        const regionFlags = {
            'na': '🇺🇸',
            'eu': '🇪🇺',
            'au': '🇦🇺',
            'sa': '🇧🇷',
            'as': '🇯🇵',
            'af': '🇿🇦',
            'uk': '🇬🇧',
            'ca': '🇨🇦',
            'us': '🇺🇸',
            'gb': '🇬🇧',
            'de': '🇩🇪',
            'fr': '🇫🇷',
            'es': '🇪🇸',
            'it': '🇮🇹',
            'ru': '🇷🇺',
            'cn': '🇨🇳',
            'jp': '🇯🇵',
            'kr': '🇰🇷',
            'br': '🇧🇷',
            'mx': '🇲🇽',
            'nz': '🇳🇿'
        };

        return regionFlags[region.toLowerCase()] || '';
    }

    /**
     * Update staff member's staff BattleMetrics ID
     * @param {string} staffId - Discord ID of the staff member
     * @returns {Promise<Object>} - Result of the operation
     */
    static async updateStaffBmId(staffId) {
        try {
            // Get staff member
            const staffMember = await StaffMember.findById(staffId);

            if (!staffMember) {
                return {
                    success: false,
                    message: 'Staff member not found'
                };
            }

            // Skip if already has staff BM ID
            if (staffMember.staff_bm_id) {
                return {
                    success: true,
                    message: 'Staff member already has staff BattleMetrics ID',
                    data: staffMember
                };
            }

            // Get staff BattleMetrics ID
            const response = await BattleMetricsClient.getStaffBattleMetricsId(staffMember.discord_name);

            if (response.status !== 200 || !response.staffBmId) {
                return {
                    success: false,
                    message: 'Failed to find staff BattleMetrics ID'
                };
            }

            // Update staff member
            staffMember.staff_bm_id = response.staffBmId;
            await staffMember.save();

            return {
                success: true,
                message: 'Staff BattleMetrics ID updated successfully',
                data: staffMember
            };
        } catch (error) {
            console.error('Error updating staff BM ID:', error);
            return {
                success: false,
                message: `Error updating staff BM ID: ${error.message}`,
                error
            };
        }
    }
}

module.exports = StaffManager;