const { Embed<PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const StaffMember = require('../models/staffMember');
const ConfigPermissionManager = require('./configPermissionManager');

/**
 * Permission Handler - Handles permission-related interactions and operations
 */
class PermissionHandler {
    constructor(client) {
        this.client = client;
        this.permissionManager = new ConfigPermissionManager(client);
        this.pendingOnboards = new Map(); // Store Steam IDs temporarily during onboarding
    }

    /**
     * Store Steam ID for pending onboarding
     */
    storePendingOnboard(targetUserId, requesterId, steamId) {
        const key = `${targetUserId}_${requesterId}`;
        this.pendingOnboards.set(key, steamId);
        // Clean up after 10 minutes
        setTimeout(() => {
            this.pendingOnboards.delete(key);
        }, 10 * 60 * 1000);
    }

    /**
     * Get Steam ID for pending onboarding
     */
    getPendingOnboardSteamId(targetUserId, requesterId) {
        const key = `${targetUserId}_${requesterId}`;
        return this.pendingOnboards.get(key);
    }

    /**
     * Handle permission selection for onboarding
     */
    async handleOnboardPermissionSelect(interaction, targetUserId, requesterId) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            const selectedPermissionLevel = interaction.values[0];
            const permissionLevel = this.permissionManager.getPermissionLevel(selectedPermissionLevel);

            if (!permissionLevel) {
                return await interaction.reply({
                    content: '❌ Invalid permission level selected.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            // Get target user
            const targetUser = await this.client.users.fetch(targetUserId);
            if (!targetUser) {
                return await interaction.editReply({
                    content: '❌ Target user not found.',
                    components: []
                });
            }

            // Show server selection for onboarding
            await this.showServerSelection(interaction, targetUser, permissionLevel, 'onboard');

        } catch (error) {
            console.error('Error handling onboard permission select:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing the permission selection.',
                components: []
            });
        }
    }

    /**
     * Handle permission selection for modification
     */
    async handleModifyPermissionSelect(interaction, targetUserId, requesterId) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            const selectedPermissionLevel = interaction.values[0];
            const permissionLevel = this.permissionManager.getPermissionLevel(selectedPermissionLevel);

            if (!permissionLevel) {
                return await interaction.reply({
                    content: '❌ Invalid permission level selected.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            // Get target user
            const targetUser = await this.client.users.fetch(targetUserId);
            if (!targetUser) {
                return await interaction.editReply({
                    content: '❌ Target user not found.',
                    components: []
                });
            }

            // Show server selection for modification
            await this.showServerSelection(interaction, targetUser, permissionLevel, 'modify');

        } catch (error) {
            console.error('Error handling modify permission select:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing the permission selection.',
                components: []
            });
        }
    }

    /**
     * Show server selection menu
     */
    async showServerSelection(interaction, targetUser, permissionLevel, action) {
        try {
            const servers = this.client.config.Servers;

            if (!servers || servers.length === 0) {
                return await interaction.editReply({
                    content: '❌ No servers configured.',
                    components: []
                });
            }

            // Create server selection menu
            const serverOptions = servers.map(server => ({
                label: server.name,
                value: server.name,
                description: `Apply permissions to ${server.name}`
            }));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`server_select_${action}_${targetUser.id}_${interaction.user.id}_${permissionLevel.name}`)
                .setPlaceholder('Select servers to apply permissions')
                .setMinValues(1)
                .setMaxValues(servers.length)
                .addOptions(serverOptions);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            const embed = new EmbedBuilder()
                .setTitle(`🎯 ${action === 'onboard' ? 'Onboarding' : 'Modifying'} Staff Member`)
                .setDescription(`Applying **${permissionLevel.displayName}** permissions to <@${targetUser.id}>`)
                .setColor(permissionLevel.color || '#2196F3')
                .addFields([
                    { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Permission Level', value: permissionLevel.displayName, inline: true },
                    { name: 'Priority', value: `${permissionLevel.priority}`, inline: true },
                    { name: 'Description', value: permissionLevel.description || 'No description', inline: false }
                ])
                .setTimestamp();

            // Add commands that will be executed
            const commands = this.permissionManager.getOnloadCommands(permissionLevel.name);
            if (commands.length > 0) {
                const commandList = this.permissionManager.getFormattedCommandList(commands);
                embed.addFields([{
                    name: 'Commands to Execute',
                    value: commandList.length > 1024 ? commandList.substring(0, 1021) + '...' : commandList,
                    inline: false
                }]);
            }

            // Note: This system focuses on server permissions via RCON
            embed.addFields([{
                name: 'ℹ️ Note',
                value: 'This system will execute RCON commands on selected servers to grant permissions. Discord roles are managed separately.',
                inline: false
            }]);

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error('Error showing server selection:', error);
            await interaction.editReply({
                content: '❌ An error occurred while showing server selection.',
                components: []
            });
        }
    }

    /**
     * Handle server selection and execute permissions
     */
    async handleServerSelection(interaction, targetUserId, requesterId, permissionLevelName, action) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            const selectedServers = interaction.values;
            const permissionLevel = this.permissionManager.getPermissionLevel(permissionLevelName);
            const targetUser = await this.client.users.fetch(targetUserId);

            if (!permissionLevel || !targetUser) {
                return await interaction.editReply({
                    content: '❌ Invalid permission level or target user.',
                    components: []
                });
            }

            // Execute the permission assignment
            const result = await this.executePermissionAssignment(
                targetUser,
                permissionLevel,
                selectedServers,
                action,
                interaction
            );

            // Update the embed with results
            const embed = new EmbedBuilder()
                .setTitle(`✅ ${action === 'onboard' ? 'Onboarding' : 'Permission Modification'} Complete`)
                .setDescription(`Successfully applied **${permissionLevel.displayName}** permissions to <@${targetUser.id}>`)
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Permission Level', value: permissionLevel.displayName, inline: true },
                    { name: 'Servers Processed', value: `${selectedServers.length}`, inline: true }
                ])
                .setTimestamp();

            // Add results for each server
            if (result.serverResults && result.serverResults.length > 0) {
                const serverResultsList = result.serverResults.map(serverResult =>
                    `**${serverResult.serverName}**: ${serverResult.success ? '✅ Success' : '❌ Failed'}\n${serverResult.details}`
                ).join('\n\n');

                embed.addFields([{
                    name: 'Server Results',
                    value: serverResultsList.length > 1024 ? serverResultsList.substring(0, 1021) + '...' : serverResultsList,
                    inline: false
                }]);
            }

            // Add system note
            if (result.discordRoles) {
                embed.addFields([{
                    name: 'Discord Roles',
                    value: result.discordRoles.message || 'Discord roles managed separately',
                    inline: false
                }]);
            }

            await interaction.editReply({ embeds: [embed], components: [] });

        } catch (error) {
            console.error('Error handling server selection:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing server selection.',
                components: []
            });
        }
    }

    /**
     * Execute permission assignment
     */
    async executePermissionAssignment(targetUser, permissionLevel, selectedServers, action, interaction) {
        const results = {
            serverResults: [],
            discordRoles: null
        };

        try {
            // First, handle staff member creation/update if onboarding
            if (action === 'onboard') {
                // This would need Steam ID - for now we'll skip this part
                // In a real implementation, you'd need to collect Steam ID first
                console.log(`Would create staff member record for ${targetUser.id}`);
            }

            // Note: Discord role assignment removed - focusing on RCON server permissions
            results.discordRoles = { success: true, message: 'Discord roles managed separately from server permissions' };

            // Execute server commands
            const commands = this.permissionManager.getOnloadCommands(permissionLevel.name);
            const servers = this.client.config.Servers;

            for (const serverName of selectedServers) {
                const server = servers.find(s => s.name === serverName);
                if (!server) {
                    results.serverResults.push({
                        serverName,
                        success: false,
                        details: 'Server not found in configuration'
                    });
                    continue;
                }

                const serverResult = {
                    serverName,
                    success: true,
                    details: []
                };

                // Execute each command for this server
                for (const cmdInfo of commands) {
                    try {
                        // Get Steam ID from pending onboards or staff member record
                        const steamId = this.getPendingOnboardSteamId(targetUser.id, interaction.user.id) || 'STEAM_ID_PLACEHOLDER';
                        const commandToRun = this.permissionManager.processCommand(cmdInfo.command, steamId);

                        // Execute RCON command if connection exists
                        if (this.client.rconConnections && this.client.rconConnections[serverName]) {
                            try {
                                console.log(`Executing RCON command on ${serverName}: ${commandToRun}`);
                                const response = await this.client.rconConnections[serverName].send(commandToRun);
                                console.log(`RCON response from ${serverName}:`, response);
                                serverResult.details.push(`✅ ${cmdInfo.title}: Success`);
                            } catch (rconError) {
                                console.error(`RCON error on ${serverName}:`, rconError);
                                serverResult.details.push(`❌ ${cmdInfo.title}: RCON Error - ${rconError.message}`);
                                serverResult.success = false;
                            }
                        } else {
                            console.log(`No RCON connection for ${serverName}, would execute: ${commandToRun}`);
                            serverResult.details.push(`⚠️ ${cmdInfo.title}: No RCON connection (simulated)`);
                        }

                    } catch (cmdError) {
                        console.error(`Error executing command ${cmdInfo.title}:`, cmdError);
                        serverResult.details.push(`❌ ${cmdInfo.title}: ${cmdError.message}`);
                        serverResult.success = false;
                    }
                }

                serverResult.details = serverResult.details.join('\n');
                results.serverResults.push(serverResult);
            }

            return results;

        } catch (error) {
            console.error('Error executing permission assignment:', error);
            throw error;
        }
    }

    /**
     * Handle offboarding confirmation
     */
    async handleOffboardConfirm(interaction, targetUserId, requesterId) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            const targetUser = await this.client.users.fetch(targetUserId);
            const staffMember = await StaffMember.findById(targetUserId);

            if (!targetUser || !staffMember) {
                return await interaction.editReply({
                    content: '❌ Target user or staff member not found.',
                    components: []
                });
            }

            // Execute offboarding
            const result = await this.executeOffboarding(targetUser, staffMember, interaction);

            const embed = new EmbedBuilder()
                .setTitle('✅ Staff Offboarding Complete')
                .setDescription(`Successfully offboarded <@${targetUser.id}>`)
                .setColor('#FF9800')
                .addFields([
                    { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Previous Status', value: 'Active Staff Member', inline: true },
                    { name: 'New Status', value: 'Offboarded', inline: true }
                ])
                .setTimestamp();

            if (result.details) {
                embed.addFields([{
                    name: 'Offboarding Details',
                    value: result.details,
                    inline: false
                }]);
            }

            await interaction.editReply({ embeds: [embed], components: [] });

        } catch (error) {
            console.error('Error handling offboard confirm:', error);
            await interaction.editReply({
                content: '❌ An error occurred during offboarding.',
                components: []
            });
        }
    }

    /**
     * Execute offboarding process
     */
    async executeOffboarding(targetUser, staffMember, interaction) {
        const details = [];

        try {
            // Deactivate staff member
            staffMember.active = false;
            staffMember.updated_at = new Date();
            await staffMember.save();
            details.push('✅ Staff record deactivated');

            // Note: Discord roles managed separately
            details.push('ℹ️ Discord roles managed separately from server permissions');

            // Execute offload commands on all servers
            try {
                const servers = this.client.config.Servers || [];
                const permissionLevels = this.permissionManager.getAllPermissionLevels();

                // Get all possible offload commands from all permission levels
                const allOffloadCommands = [];
                for (const level of permissionLevels) {
                    const commands = this.permissionManager.getOffloadCommands(level.name);
                    allOffloadCommands.push(...commands);
                }

                // Remove duplicates based on command
                const uniqueCommands = allOffloadCommands.filter((cmd, index, self) =>
                    index === self.findIndex(c => c.command === cmd.command)
                );

                if (uniqueCommands.length > 0) {
                    for (const server of servers) {
                        const serverName = server.name;
                        let serverSuccess = true;
                        const serverDetails = [];

                        for (const cmdInfo of uniqueCommands) {
                            try {
                                // TODO: Get actual Steam ID from staff member record
                                const steamId = staffMember.steam_id || 'STEAM_ID_PLACEHOLDER';
                                const commandToRun = this.permissionManager.processCommand(cmdInfo.command, steamId);

                                // Execute RCON command if connection exists
                                if (this.client.rconConnections && this.client.rconConnections[serverName]) {
                                    try {
                                        console.log(`Executing offload RCON command on ${serverName}: ${commandToRun}`);
                                        const response = await this.client.rconConnections[serverName].send(commandToRun);
                                        console.log(`RCON response from ${serverName}:`, response);
                                        serverDetails.push(`✅ ${cmdInfo.title}`);
                                    } catch (rconError) {
                                        console.error(`RCON error on ${serverName}:`, rconError);
                                        serverDetails.push(`❌ ${cmdInfo.title}: ${rconError.message}`);
                                        serverSuccess = false;
                                    }
                                } else {
                                    console.log(`No RCON connection for ${serverName}, would execute: ${commandToRun}`);
                                    serverDetails.push(`⚠️ ${cmdInfo.title}: No RCON connection`);
                                }
                            } catch (cmdError) {
                                console.error(`Error executing offload command ${cmdInfo.title}:`, cmdError);
                                serverDetails.push(`❌ ${cmdInfo.title}: ${cmdError.message}`);
                                serverSuccess = false;
                            }
                        }

                        if (serverDetails.length > 0) {
                            details.push(`**${serverName}**: ${serverSuccess ? '✅' : '⚠️'} ${serverDetails.join(', ')}`);
                        }
                    }
                } else {
                    details.push('ℹ️ No offload commands configured');
                }
            } catch (error) {
                console.error('Error executing offload commands:', error);
                details.push(`❌ Error executing server offload commands: ${error.message}`);
            }

            return { success: true, details: details.join('\n') };

        } catch (error) {
            console.error('Error executing offboarding:', error);
            details.push(`❌ Error during offboarding: ${error.message}`);
            return { success: false, details: details.join('\n') };
        }
    }
}

module.exports = PermissionHandler;
