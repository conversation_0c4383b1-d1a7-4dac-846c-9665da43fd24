const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const StaffMember = require('../models/staffMember');
const PermissionLevel = require('../models/permissionLevel');
const StaffManager = require('./staffManager');

/**
 * Permission Handler - Handles permission-related interactions and operations
 */
class PermissionHandler {
    constructor(client) {
        this.client = client;
    }

    /**
     * Handle permission selection for onboarding
     */
    async handleOnboardPermissionSelect(interaction, targetUserId, requesterId) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            const selectedPermissionLevel = interaction.values[0];
            const permissionLevel = await PermissionLevel.getByName(selectedPermissionLevel);
            
            if (!permissionLevel) {
                return await interaction.reply({
                    content: '❌ Invalid permission level selected.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            // Get target user
            const targetUser = await this.client.users.fetch(targetUserId);
            if (!targetUser) {
                return await interaction.editReply({
                    content: '❌ Target user not found.',
                    components: []
                });
            }

            // Show server selection for onboarding
            await this.showServerSelection(interaction, targetUser, permissionLevel, 'onboard');

        } catch (error) {
            console.error('Error handling onboard permission select:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing the permission selection.',
                components: []
            });
        }
    }

    /**
     * Handle permission selection for modification
     */
    async handleModifyPermissionSelect(interaction, targetUserId, requesterId) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            const selectedPermissionLevel = interaction.values[0];
            const permissionLevel = await PermissionLevel.getByName(selectedPermissionLevel);
            
            if (!permissionLevel) {
                return await interaction.reply({
                    content: '❌ Invalid permission level selected.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            // Get target user
            const targetUser = await this.client.users.fetch(targetUserId);
            if (!targetUser) {
                return await interaction.editReply({
                    content: '❌ Target user not found.',
                    components: []
                });
            }

            // Show server selection for modification
            await this.showServerSelection(interaction, targetUser, permissionLevel, 'modify');

        } catch (error) {
            console.error('Error handling modify permission select:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing the permission selection.',
                components: []
            });
        }
    }

    /**
     * Show server selection menu
     */
    async showServerSelection(interaction, targetUser, permissionLevel, action) {
        try {
            const servers = this.client.config.Servers;
            
            if (!servers || servers.length === 0) {
                return await interaction.editReply({
                    content: '❌ No servers configured.',
                    components: []
                });
            }

            // Create server selection menu
            const serverOptions = servers.map(server => ({
                label: server.name,
                value: server.name,
                description: `Apply permissions to ${server.name}`
            }));

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`server_select_${action}_${targetUser.id}_${interaction.user.id}_${permissionLevel.name}`)
                .setPlaceholder('Select servers to apply permissions')
                .setMinValues(1)
                .setMaxValues(servers.length)
                .addOptions(serverOptions);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            const embed = new EmbedBuilder()
                .setTitle(`🎯 ${action === 'onboard' ? 'Onboarding' : 'Modifying'} Staff Member`)
                .setDescription(`Applying **${permissionLevel.displayName}** permissions to <@${targetUser.id}>`)
                .setColor(permissionLevel.color || '#2196F3')
                .addFields([
                    { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Permission Level', value: permissionLevel.displayName, inline: true },
                    { name: 'Priority', value: `${permissionLevel.priority}`, inline: true },
                    { name: 'Description', value: permissionLevel.description || 'No description', inline: false }
                ])
                .setTimestamp();

            // Add commands that will be executed
            const commands = permissionLevel.getOnloadCommands();
            if (commands.length > 0) {
                const commandList = commands.map(cmd => `• **${cmd.title}**: \`${cmd.command}\``).join('\n');
                embed.addFields([{
                    name: 'Commands to Execute',
                    value: commandList.length > 1024 ? commandList.substring(0, 1021) + '...' : commandList,
                    inline: false
                }]);
            }

            // Add Discord roles that will be assigned
            if (permissionLevel.discordRoles && permissionLevel.discordRoles.length > 0) {
                const roleList = permissionLevel.discordRoles.map(roleId => `<@&${roleId}>`).join(', ');
                embed.addFields([{
                    name: 'Discord Roles to Assign',
                    value: roleList,
                    inline: false
                }]);
            }

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error('Error showing server selection:', error);
            await interaction.editReply({
                content: '❌ An error occurred while showing server selection.',
                components: []
            });
        }
    }

    /**
     * Handle server selection and execute permissions
     */
    async handleServerSelection(interaction, targetUserId, requesterId, permissionLevelName, action) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            const selectedServers = interaction.values;
            const permissionLevel = await PermissionLevel.getByName(permissionLevelName);
            const targetUser = await this.client.users.fetch(targetUserId);

            if (!permissionLevel || !targetUser) {
                return await interaction.editReply({
                    content: '❌ Invalid permission level or target user.',
                    components: []
                });
            }

            // Execute the permission assignment
            const result = await this.executePermissionAssignment(
                targetUser, 
                permissionLevel, 
                selectedServers, 
                action,
                interaction
            );

            // Update the embed with results
            const embed = new EmbedBuilder()
                .setTitle(`✅ ${action === 'onboard' ? 'Onboarding' : 'Permission Modification'} Complete`)
                .setDescription(`Successfully applied **${permissionLevel.displayName}** permissions to <@${targetUser.id}>`)
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Permission Level', value: permissionLevel.displayName, inline: true },
                    { name: 'Servers Processed', value: `${selectedServers.length}`, inline: true }
                ])
                .setTimestamp();

            // Add results for each server
            if (result.serverResults && result.serverResults.length > 0) {
                const serverResultsList = result.serverResults.map(serverResult => 
                    `**${serverResult.serverName}**: ${serverResult.success ? '✅ Success' : '❌ Failed'}\n${serverResult.details}`
                ).join('\n\n');

                embed.addFields([{
                    name: 'Server Results',
                    value: serverResultsList.length > 1024 ? serverResultsList.substring(0, 1021) + '...' : serverResultsList,
                    inline: false
                }]);
            }

            // Add Discord role results
            if (result.discordRoles) {
                embed.addFields([{
                    name: 'Discord Roles',
                    value: result.discordRoles.success ? '✅ Roles assigned successfully' : `❌ ${result.discordRoles.error}`,
                    inline: false
                }]);
            }

            await interaction.editReply({ embeds: [embed], components: [] });

        } catch (error) {
            console.error('Error handling server selection:', error);
            await interaction.editReply({
                content: '❌ An error occurred while processing server selection.',
                components: []
            });
        }
    }

    /**
     * Execute permission assignment
     */
    async executePermissionAssignment(targetUser, permissionLevel, selectedServers, action, interaction) {
        const results = {
            serverResults: [],
            discordRoles: null
        };

        try {
            // First, handle staff member creation/update if onboarding
            if (action === 'onboard') {
                // This would need Steam ID - for now we'll skip this part
                // In a real implementation, you'd need to collect Steam ID first
                console.log(`Would create staff member record for ${targetUser.id}`);
            }

            // Assign Discord roles
            try {
                const guild = interaction.guild;
                const member = await guild.members.fetch(targetUser.id);
                
                if (permissionLevel.discordRoles && permissionLevel.discordRoles.length > 0) {
                    for (const roleId of permissionLevel.discordRoles) {
                        try {
                            const role = guild.roles.cache.get(roleId);
                            if (role) {
                                await member.roles.add(role);
                            }
                        } catch (roleError) {
                            console.error(`Error assigning role ${roleId}:`, roleError);
                        }
                    }
                    results.discordRoles = { success: true };
                } else {
                    results.discordRoles = { success: true, message: 'No Discord roles to assign' };
                }
            } catch (error) {
                console.error('Error assigning Discord roles:', error);
                results.discordRoles = { success: false, error: error.message };
            }

            // Execute server commands
            const commands = permissionLevel.getOnloadCommands();
            const servers = this.client.config.Servers;

            for (const serverName of selectedServers) {
                const server = servers.find(s => s.name === serverName);
                if (!server) {
                    results.serverResults.push({
                        serverName,
                        success: false,
                        details: 'Server not found in configuration'
                    });
                    continue;
                }

                const serverResult = {
                    serverName,
                    success: true,
                    details: []
                };

                // Execute each command for this server
                for (const cmdInfo of commands) {
                    try {
                        // For now, we'll simulate command execution
                        // In a real implementation, you'd use RCON here
                        const commandToRun = cmdInfo.command.replace('{steam_id}', 'STEAM_ID_PLACEHOLDER');
                        
                        console.log(`Would execute on ${serverName}: ${commandToRun}`);
                        serverResult.details.push(`✅ ${cmdInfo.title}: Success`);
                        
                    } catch (cmdError) {
                        console.error(`Error executing command ${cmdInfo.title}:`, cmdError);
                        serverResult.details.push(`❌ ${cmdInfo.title}: ${cmdError.message}`);
                        serverResult.success = false;
                    }
                }

                serverResult.details = serverResult.details.join('\n');
                results.serverResults.push(serverResult);
            }

            return results;

        } catch (error) {
            console.error('Error executing permission assignment:', error);
            throw error;
        }
    }

    /**
     * Handle offboarding confirmation
     */
    async handleOffboardConfirm(interaction, targetUserId, requesterId) {
        try {
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            await interaction.deferUpdate();

            const targetUser = await this.client.users.fetch(targetUserId);
            const staffMember = await StaffMember.findById(targetUserId);

            if (!targetUser || !staffMember) {
                return await interaction.editReply({
                    content: '❌ Target user or staff member not found.',
                    components: []
                });
            }

            // Execute offboarding
            const result = await this.executeOffboarding(targetUser, staffMember, interaction);

            const embed = new EmbedBuilder()
                .setTitle('✅ Staff Offboarding Complete')
                .setDescription(`Successfully offboarded <@${targetUser.id}>`)
                .setColor('#FF9800')
                .addFields([
                    { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
                    { name: 'Previous Status', value: 'Active Staff Member', inline: true },
                    { name: 'New Status', value: 'Offboarded', inline: true }
                ])
                .setTimestamp();

            if (result.details) {
                embed.addFields([{
                    name: 'Offboarding Details',
                    value: result.details,
                    inline: false
                }]);
            }

            await interaction.editReply({ embeds: [embed], components: [] });

        } catch (error) {
            console.error('Error handling offboard confirm:', error);
            await interaction.editReply({
                content: '❌ An error occurred during offboarding.',
                components: []
            });
        }
    }

    /**
     * Execute offboarding process
     */
    async executeOffboarding(targetUser, staffMember, interaction) {
        const details = [];

        try {
            // Deactivate staff member
            staffMember.active = false;
            staffMember.updated_at = new Date();
            await staffMember.save();
            details.push('✅ Staff record deactivated');

            // Remove Discord roles (remove all roles except @everyone)
            try {
                const guild = interaction.guild;
                const member = await guild.members.fetch(targetUser.id);
                const rolesToRemove = member.roles.cache.filter(role => role.id !== guild.id);
                
                if (rolesToRemove.size > 0) {
                    await member.roles.remove(rolesToRemove);
                    details.push(`✅ Removed ${rolesToRemove.size} Discord roles`);
                } else {
                    details.push('ℹ️ No Discord roles to remove');
                }
            } catch (error) {
                console.error('Error removing Discord roles:', error);
                details.push(`❌ Error removing Discord roles: ${error.message}`);
            }

            // Execute offload commands on servers (if configured)
            // This would require getting all permission levels and their offload commands
            details.push('ℹ️ Server permission removal would be executed here');

            return { success: true, details: details.join('\n') };

        } catch (error) {
            console.error('Error executing offboarding:', error);
            details.push(`❌ Error during offboarding: ${error.message}`);
            return { success: false, details: details.join('\n') };
        }
    }
}

module.exports = PermissionHandler;
