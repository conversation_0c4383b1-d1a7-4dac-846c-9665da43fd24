const { EmbedBuilder } = require('discord.js');
const StaffManager = require('../utils/staffManager');
const client = require('../index');

/**
 * Staff Info Updater
 *
 * Background task to periodically update staff information:
 * - BattleMetrics ID
 * - Region information
 * - Staff BattleMetrics ID
 */
class StaffInfoUpdater {
    /**
     * Initialize the staff info updater
     */
    static init() {
        // Set up interval for updating staff info
        this.interval = setInterval(() => this.updateStaffInfo(), 60000); // Run every minute
        console.log('[STAFF] Staff info updater initialized');

        // Run immediately
        this.updateStaffInfo();

        return this;
    }

    /**
     * Stop the staff info updater
     */
    static stop() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
            console.log('[STAFF] Staff info updater stopped');
        }
    }

    /**
     * Update staff information
     */
    static async updateStaffInfo() {
        try {
            // Get all active staff members
            const staffMembers = await StaffManager.getAllActiveStaff();

            if (!staffMembers || staffMembers.length === 0) {
                return;
            }

            // Process one staff member at a time to avoid rate limiting
            const staffMember = this.getNextStaffMember(staffMembers);

            if (!staffMember) {
                return;
            }

            // Update BattleMetrics ID if needed
            if (!staffMember.bm_id && staffMember.steam_id) {
                const result = await StaffManager.updateStaffBattleMetricsId(staffMember._id);

                if (result.success) {
                    console.log(`[STAFF] Updated BattleMetrics ID for ${staffMember.discord_name}: ${result.data.bm_id}`);

                    // Send DM to staff member
                    try {
                        const user = await client.users.fetch(staffMember._id);

                        if (user) {
                            const embed = new EmbedBuilder()
                                .setTitle('BattleMetrics Profile Found')
                                .setDescription(`I have successfully fetched your [BattleMetrics Profile](https://www.battlemetrics.com/rcon/players/${result.data.bm_id})`)
                                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                                .setTimestamp();

                            await user.send({ embeds: [embed] });
                        }
                    } catch (error) {
                        console.error(`[STAFF] Failed to send DM to ${staffMember.discord_name}:`, error);
                    }

                    // Update staff member reference for next steps
                    staffMember.bm_id = result.data.bm_id;
                }
            }

            // Update region info if needed or if it's time for a refresh
            const needsRegionUpdate = staffMember.bm_id && (
                !staffMember.region_info ||
                staffMember.region_info.region === 'unknown' ||
                !staffMember.region_info.country ||
                !staffMember.region_info.continent ||
                // Check if it's been more than 24 hours since the last update
                (new Date() - new Date(staffMember.updated_at)) > (24 * 60 * 60 * 1000)
            );

            if (needsRegionUpdate) {
                const result = await StaffManager.updateStaffRegionInfo(staffMember._id);

                if (result.success) {
                    console.log(`[STAFF] Updated region info for ${staffMember.discord_name}: ${result.data.region_info.region}`);

                    // Only send DM if this is a new region or significant change
                    const isNewInfo = !staffMember.region_info ||
                                     staffMember.region_info.region === 'unknown' ||
                                     !staffMember.region_info.country ||
                                     !staffMember.region_info.continent;

                    if (isNewInfo) {
                        try {
                            const user = await client.users.fetch(staffMember._id);

                            if (user) {
                                const embed = new EmbedBuilder()
                                    .setTitle('Country Information Updated')
                                    .setDescription('I have updated your country information:')
                                    .addFields(
                                        { name: 'Region', value: result.data.region_info.region || 'Unknown', inline: true },
                                        { name: 'Country', value: result.data.region_info.country || 'Unknown', inline: true },
                                        { name: 'Continent', value: result.data.region_info.continent || 'Unknown', inline: true },
                                        { name: 'Flag', value: result.data.region_info.flag || 'Unknown', inline: true }
                                    )
                                    .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                                    .setTimestamp();

                                await user.send({ embeds: [embed] });
                            }
                        } catch (error) {
                            console.error(`[STAFF] Failed to send DM to ${staffMember.discord_name}:`, error);
                        }
                    }
                }
            }

            // Update staff BattleMetrics ID if needed
            if (!staffMember.staff_bm_id) {
                const result = await StaffManager.updateStaffBmId(staffMember._id);

                if (result.success) {
                    console.log(`[STAFF] Updated staff BattleMetrics ID for ${staffMember.discord_name}: ${result.data.staff_bm_id}`);

                    // Send DM to staff member
                    try {
                        const user = await client.users.fetch(staffMember._id);

                        if (user) {
                            const embed = new EmbedBuilder()
                                .setTitle('Staff BattleMetrics Profile Found')
                                .setDescription(`I have found your [BattleMetrics Staff Profile](https://www.battlemetrics.com/rcon/bans?filter%5Busers%5D=${result.data.staff_bm_id})`)
                                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                                .setTimestamp();

                            await user.send({ embeds: [embed] });
                        }
                    } catch (error) {
                        console.error(`[STAFF] Failed to send DM to ${staffMember.discord_name}:`, error);
                    }
                }
            }
        } catch (error) {
            console.error('[STAFF] Error updating staff info:', error);
        }
    }

    /**
     * Get the next staff member to process
     * @param {Array} staffMembers - Array of staff members
     * @returns {Object} - Next staff member to process
     */
    static getNextStaffMember(staffMembers) {
        // First, try to find a staff member without BattleMetrics ID
        const withoutBmId = staffMembers.find(staff => !staff.bm_id && staff.steam_id);

        if (withoutBmId) {
            return withoutBmId;
        }

        // Then, try to find a staff member without region info
        const withoutRegion = staffMembers.find(staff =>
            staff.bm_id && (!staff.region_info || staff.region_info.region === 'unknown')
        );

        if (withoutRegion) {
            return withoutRegion;
        }

        // Then, try to find a staff member without detailed region info (country, continent)
        const withoutDetailedRegion = staffMembers.find(staff =>
            staff.bm_id &&
            staff.region_info &&
            staff.region_info.region !== 'unknown' &&
            (!staff.region_info.country || !staff.region_info.continent)
        );

        if (withoutDetailedRegion) {
            return withoutDetailedRegion;
        }

        // Finally, try to find a staff member without staff BattleMetrics ID
        const withoutStaffBmId = staffMembers.find(staff => !staff.staff_bm_id);

        if (withoutStaffBmId) {
            return withoutStaffBmId;
        }

        // If all staff have complete information, pick one to refresh based on time
        // This ensures all staff information is periodically refreshed
        const now = Date.now();
        // Find staff whose information hasn't been updated in the last 24 hours
        const needsRefresh = staffMembers.find(staff => {
            const lastUpdate = new Date(staff.updated_at).getTime();
            const hoursSinceUpdate = (now - lastUpdate) / (1000 * 60 * 60);
            return hoursSinceUpdate > 24; // Refresh every 24 hours
        });

        if (needsRefresh) {
            console.log(`[STAFF] Refreshing information for ${needsRefresh.discord_name} (last updated ${new Date(needsRefresh.updated_at).toISOString()})`);
            return needsRefresh;
        }

        return null;
    }
}

module.exports = StaffInfoUpdater;
