General:
  NoPermsMessage: "Sorry, you don't have permissions to use this command!"
  CommandSuccess: "Command executed successfully!"
  CommandError: "An error occurred while executing the command."
  InvalidSteamID: "Invalid Steam ID format. Please provide a valid Steam ID."

Staff:
  AddStaff:
    Success: "Successfully added {user} to the staff team!"
    AlreadyStaff: "{user} is already a staff member."
    Error: "Failed to add {user} to the staff team: {error}"
  
  RemoveStaff:
    Success: "Successfully removed {user} from the staff team."
    NotStaff: "{user} is not a staff member."
    Error: "Failed to remove {user} from the staff team: {error}"
  
  OnloadStaff:
    Success: "Successfully onloaded {user} to {server}."
    Error: "Failed to onload {user} to {server}: {error}"
    ServerSelection: "Please select the server(s) to onload the staff member to:"
    CommandSent: "Command sent to server: {command}"
    CommandResponse: "Server response: {response}"
  
  OffloadStaff:
    Success: "Successfully offloaded {user} from {server}."
    Error: "Failed to offload {user} from {server}: {error}"
    ServerSelection: "Please select the server(s) to offload the staff member from:"
    CommandSent: "Command sent to server: {command}"
    CommandResponse: "Server response: {response}"
  
  StaffStatus:
    Title: "Staff Status"
    OnlineStaff: "Online Staff"
    OfflineStaff: "Offline Staff"
    NoStaffOnline: "No staff members are currently online."
    ServerOffline: "Server is currently offline."
    LastUpdated: "Last updated"
    StaffOnServer: "{staff} is online on {server} for {time}"
    TotalOnline: "Total Online"
    TotalOffline: "Total Offline"

  StaffStats:
    Title: "{user}'s Staff Statistics"
    WeeklyStats: "Weekly Stats"
    AllTimeStats: "All Time Stats"
    Messages: "Messages"
    VoiceHours: "Voice Hours"
    IngameHours: "Ingame Hours"
    Tickets: "Tickets"
    Bans: "Bans"
    LastActive: "Last Active"
    Region: "Region"
    Timezone: "Timezone"

Watchlist:
  Add:
    Success: "Successfully added {player} to your watchlist."
    AlreadyWatching: "{player} is already on your watchlist."
    Error: "Failed to add {player} to your watchlist: {error}"
  
  Remove:
    Success: "Successfully removed {player} from your watchlist."
    NotWatching: "{player} is not on your watchlist."
    Error: "Failed to remove {player} from your watchlist: {error}"
  
  List:
    Title: "Your Watchlist"
    Empty: "Your watchlist is empty."
    Entry: "{player} - Last seen: {lastSeen}"
  
  Notification:
    Title: "Watchlist Alert"
    PlayerOnline: "{player} is now online on {server}!"
    LastSeen: "Last seen: {lastSeen}"
    AddedBy: "Added to watchlist by: {user}"

BanRequest:
  Create:
    Success: "Ban request for {player} has been submitted."
    AlreadyExists: "There is already a pending ban request for {player}."
    Error: "Failed to create ban request: {error}"
  
  Review:
    Title: "Ban Request Review"
    NoRequests: "There are no pending ban requests."
    Player: "Player"
    Reason: "Reason"
    Evidence: "Evidence"
    RequestedBy: "Requested by"
    RequestedAt: "Requested at"
    Approve: "Approve"
    Reject: "Reject"
  
  Approve:
    Success: "Ban request for {player} has been approved."
    Error: "Failed to approve ban request: {error}"
  
  Reject:
    Success: "Ban request for {player} has been rejected."
    Error: "Failed to reject ban request: {error}"

SpectateRequest:
  Create:
    Success: "Spectate request for {player} has been submitted."
    AlreadyExists: "There is already a pending spectate request for {player}."
    Error: "Failed to create spectate request: {error}"
  
  Review:
    Title: "Spectate Request Review"
    NoRequests: "There are no pending spectate requests."
    Player: "Player"
    Reason: "Reason"
    RequestedBy: "Requested by"
    RequestedAt: "Requested at"
    Approve: "Approve"
    Reject: "Reject"
  
  Approve:
    Success: "Spectate request for {player} has been approved."
    Error: "Failed to approve spectate request: {error}"
  
  Reject:
    Success: "Spectate request for {player} has been rejected."
    Error: "Failed to reject spectate request: {error}"
