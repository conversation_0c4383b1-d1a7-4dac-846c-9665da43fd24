BotSettings:
  Token: "MTM2OTUwMDY4ODAwMjA1NjMyMg.Gwpc-O.sdSyspI3y46xASrTSLLpAn_zXEtaLWq75ELyGE" # Your bot token here
  BotName: "Staff Manager"
  prefix: ","
  GuildID: "1234609643372806205"
  StaffGuild: "1234609643372806205"
  EmbedColors: "#CCCCCC"
  LogCommands: true
  Statistics: true
  DevMode: false
  StaffRole: "1239323638302249041" # Staff role ID
  SERVER_NAME: "Oasis"

Database:
  MongoURI: "***************************************************/" # Your MongoDB connection string

APIKeys:
  steam_api_key: "DB9594CD0560F91155C225F3C236CB39"
  bm_api_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6ImUyZmE3OWI4YzQ4NWYyOWQiLCJpYXQiOjE3NDgxMjI1OTUsIm5iZiI6MTc0ODEyMjU5NSwiaXNzIjoiaHR0cHM6Ly93d3cuYmF0dGxlbWV0cmljcy5jb20iLCJzdWIiOiJ1cm46dXNlcjoxMDEzMDE0In0.Q0Soeuvhv6MJ6uMU9wnE5aKPDx6_PtLMAjTj-Ix-4xI"
  bm_org_id: "110575"
  bm_ban_list_id: "c504b820-0f70-11f0-8b8f-494322d3635f"

Channels:
  statusChannelID: "1376597969415176344" # Channel ID for staff status updates
  WatchlistChannelID: "1376477529145217034" # Channel ID for watchlist notifications
  BanRequestsChannelID: "1376477529145217034" # Channel ID for ban requests
  SpectateRequestsChannelID: "1376477529145217034" # Channel ID for spectate requests
  WeeklyReviewChannelID: "1376597969415176344" # Channel ID for weekly staff activity reviews

Webhooks:
  LogsWebhook: "https://discord.com/api/webhooks/1376602666217504828/jDsPKCLHsZIAlzhGYyUkNIBvchtruTOrdMzIZGag_FrZKoxhsMUCheU76n9Bkh_yCxW4" # Webhook URL for logs
  ErrorLogsHook: "https://discord.com/api/webhooks/1376602666217504828/jDsPKCLHsZIAlzhGYyUkNIBvchtruTOrdMzIZGag_FrZKoxhsMUCheU76n9Bkh_yCxW4" # Webhook URL for error logs
  StaffLogHook: "https://discord.com/api/webhooks/1376602666217504828/jDsPKCLHsZIAlzhGYyUkNIBvchtruTOrdMzIZGag_FrZKoxhsMUCheU76n9Bkh_yCxW4" # Webhook URL for staff logs
  BanStaffWebhook: "https://discord.com/api/webhooks/1376602599624806449/ZFPG-HWgSZb3deBx6wM3zq4xvraK-gt4vMVvW0yBC1xdrKPQZRzrf8YMgRB4MMDZi945" # Webhook URL for ban notifications to staff
  BanPublicWebhook: "https://discord.com/api/webhooks/1356195177840115742/WSuSTdDGjSiRtNSBweclRdv6cGm2Y_b8o0_MWlKg_eq3x5mWKztjKvU0jo3UwzYxUoPR" # Webhook URL for public ban notifications
  TicketClaimWebhook: "https://discord.com/api/webhooks/1376602666217504828/jDsPKCLHsZIAlzhGYyUkNIBvchtruTOrdMzIZGag_FrZKoxhsMUCheU76n9Bkh_yCxW4" # Webhook URL for ticket claim notifications

Servers:
  - name: "Oasis Rust ~ US 2x Monthly | Vanilla+ | No BP Wipes"
    long_name: "Oasis Rust ~ US 2x Monthly | Vanilla+ | No BP Wipes"
    ip: "*************"
    rcon_port: "28016"
    rcon_password: "E0fgSmWqVN8zDq"
    battlemetrics_id: "33665102"
  - name: "Oasis Rust - NA 10x | No BPs | Shop | Kits"
    long_name: "Oasis Rust - NA 10x | No BPs | Shop | Kits"
    ip: "*************"
    rcon_port: "28016"
    rcon_password: "Y1qFpmNTW3JUVA"
    battlemetrics_id: "33681211"

# Staff Management System Configuration
StaffManagement:
  # BattleMetrics Organization ID for tracking staff in-game hours
  BattlemetricsORGID: "110575"

  # Channels for Battlemetrics requests
  Channels:
    # Channel for watchlist notifications
    WatchlistChannelID: "1376477529145217034"
    # Channel for ban requests
    BanRequestsChannelID: "1376477529145217034"
    # Channel for spectate requests
    SpectateRequestsChannelID: "1376477529145217034"

  # Admin roles that can review requests
  AdminRoles:
    - "1258960020368920657"
    - "1374923248067936307"
    - "1239078623743442954"

  Images:
    StaffStatusEmbed: "https://cdn.discordapp.com/attachments/1329591447468507247/1329979591426834595/npb3h0u.gif?ex=68180e84&is=6816bd04&hm=9c88ba0fffdde1304826fde54ec7ebd9496c33f42489d61d3ddea3e7a6d3299c&"

  # Staff Activity Tracking
  ActivityTracking:
    Enabled: true
    # Channel to display online staff
    OnlineStaffChannelID: "1376597880520839189"
    # Channel for weekly staff activity reviews
    WeeklyReviewChannelID: "1376597969415176344"
    # Whether to DM staff with their weekly activity
    MessageStaffWeeklyReview: true

  # Stats Embed Configuration
  StatsEmbed:
    # Channel ID for the updating stats embed (set via /statsembed setup)
    ChannelID: "1376595707854848211"
    # Update frequency in milliseconds (default: 300000 = 5 minutes)
    UpdateFrequency: 300000

  # Permission Levels Configuration (RCON-based server permissions)
  PermissionLevels:
    admin:
      displayName: "Administrator"
      priority: 100
      description: "Full administrative access with all server permissions"
      color: "#FF0000"
      onloadCommands:
        - title: "Grant Admin Access"
          command: "moderatorid {steam_id}"
          description: "Grants admin access on the server"
        - title: "Add to Admin Group"
          command: "o.usergroup add {steam_id} admin"
          description: "Adds to Oxide admin group"
        - title: "Grant Owner Permissions"
          command: "o.grant user {steam_id} *"
          description: "Grants all permissions to user"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"
      offloadCommands:
        - title: "Remove Admin Access"
          command: "removemoderator {steam_id}"
          description: "Removes admin access from server"
        - title: "Remove from Admin Group"
          command: "o.usergroup remove {steam_id} admin"
          description: "Removes from Oxide admin group"
        - title: "Revoke All Permissions"
          command: "o.revoke user {steam_id} *"
          description: "Revokes all permissions from user"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"

    moderator:
      displayName: "Moderator"
      priority: 70
      description: "Moderation permissions for bans, kicks, and player management"
      color: "#FF9800"
      onloadCommands:
        - title: "Grant Moderator Access"
          command: "moderatorid {steam_id}"
          description: "Grants moderator access on the server"
        - title: "Add to Moderator Group"
          command: "o.usergroup add {steam_id} moderator"
          description: "Adds to Oxide moderator group"
        - title: "Grant Ban Permission"
          command: "o.grant user {steam_id} ban"
          description: "Grants ban permission"
        - title: "Grant Kick Permission"
          command: "o.grant user {steam_id} kick"
          description: "Grants kick permission"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"
      offloadCommands:
        - title: "Remove Moderator Access"
          command: "removemoderator {steam_id}"
          description: "Removes moderator access from server"
        - title: "Remove from Moderator Group"
          command: "o.usergroup remove {steam_id} moderator"
          description: "Removes from Oxide moderator group"
        - title: "Revoke Ban Permission"
          command: "o.revoke user {steam_id} ban"
          description: "Revokes ban permission"
        - title: "Revoke Kick Permission"
          command: "o.revoke user {steam_id} kick"
          description: "Revokes kick permission"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"

    helper:
      displayName: "Helper"
      priority: 30
      description: "Basic helper permissions for player assistance"
      color: "#4CAF50"
      onloadCommands:
        - title: "Add to Helper Group"
          command: "o.usergroup add {steam_id} helper"
          description: "Adds to Oxide helper group"
        - title: "Grant Teleport Permission"
          command: "o.grant user {steam_id} teleport.use"
          description: "Grants teleport permission"
        - title: "Grant Info Permission"
          command: "o.grant user {steam_id} playerinfo.use"
          description: "Grants player info permission"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"
      offloadCommands:
        - title: "Remove from Helper Group"
          command: "o.usergroup remove {steam_id} helper"
          description: "Removes from Oxide helper group"
        - title: "Revoke Teleport Permission"
          command: "o.revoke user {steam_id} teleport.use"
          description: "Revokes teleport permission"
        - title: "Revoke Info Permission"
          command: "o.revoke user {steam_id} playerinfo.use"
          description: "Revokes player info permission"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"

    trial:
      displayName: "Trial Staff"
      priority: 10
      description: "Limited trial permissions for new staff members"
      color: "#2196F3"
      onloadCommands:
        - title: "Add to Trial Group"
          command: "o.usergroup add {steam_id} trial"
          description: "Adds to Oxide trial group"
        - title: "Grant Basic Info Permission"
          command: "o.grant user {steam_id} playerinfo.basic"
          description: "Grants basic player info permission"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"
      offloadCommands:
        - title: "Remove from Trial Group"
          command: "o.usergroup remove {steam_id} trial"
          description: "Removes from Oxide trial group"
        - title: "Revoke Basic Info Permission"
          command: "o.revoke user {steam_id} playerinfo.basic"
          description: "Revokes basic player info permission"
        - title: "Save Configuration"
          command: "server.writecfg"
          description: "Saves server configuration"
