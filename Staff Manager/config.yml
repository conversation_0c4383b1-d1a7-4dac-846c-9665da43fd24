BotSettings:
  Token: "MTM2ODQzNTc3OTY2MzI5ODYxMA.Gu_AOI.pDylnAGGhZTBhkhMwtKvzqTzTUCl-ZMk9UwYuY" # Your bot token here
  BotName: "Staff Manager"
  prefix: ","
  GuildID: "1250601096783593573"
  StaffGuild: "1250601096783593573"
  EmbedColors: "#CCCCCC"
  LogCommands: true
  Statistics: true
  DevMode: false
  StaffRole: "1329591326324560044" # Staff role ID
  SERVER_NAME: "Mrk"

Database:
  MongoURI: "mongodb://admin:xTW8mgv5bl8jbQ@89.39.210.59:27017" # Your MongoDB connection string

APIKeys:
  steam_api_key: "BE076C1F77ADE2C84412BC428312D4C4"
  bm_api_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6ImRjMjU5NWUwYTgxYmIyM2IiLCJpYXQiOjE3NDYxMjE5MTksIm5iZiI6MTc0NjEyMTkxOSwiaXNzIjoiaHR0cHM6Ly93d3cuYmF0dGxlbWV0cmljcy5jb20iLCJzdWIiOiJ1cm46dXNlcjo0OTUxNjMifQ.16I9eIAO_QwXQELIzJlbo9aBD9NuF5vH10Umw88TmSc
  bm_org_id: "106455"
  bm_ban_list_id: "b45a3070-f172-11ef-8919-b1029e5519b0"

Channels:
  statusChannelID: "1347546446693662801" # Channel ID for staff status updates
  WatchlistChannelID: "1367590415641088061" # Channel ID for watchlist notifications
  BanRequestsChannelID: "1367590415641088061" # Channel ID for ban requests
  SpectateRequestsChannelID: "1367590415641088061" # Channel ID for spectate requests
  WeeklyReviewChannelID: "1367609269360394261" # Channel ID for weekly staff activity reviews

Webhooks:
  LogsWebhook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ" # Webhook URL for logs
  ErrorLogsHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ" # Webhook URL for error logs
  StaffLogHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ" # Webhook URL for staff logs
  BanStaffWebhook: "https://discord.com/api/webhooks/1332971372108058667/QmzjoVld3YQR_5y7M_4ExVjijncFCqiuIH61bjOSvGayaI76YZ4YGjL5-I5IjqLv-989" # Webhook URL for ban notifications to staff
  BanPublicWebhook: "https://discord.com/api/webhooks/1361927692299014245/XathzmstYlgLFsmbMuFw-OnBvSlCkzDwQOhSJ4pG5mx-PdI-fImHY9MFG3K-P7_cvLa5" # Webhook URL for public ban notifications
  TicketClaimWebhook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ" # Webhook URL for ticket claim notifications

Servers:
  - name: "AU Mrk"
    long_name: "AU Mrk"
    ip: "*************"
    rcon_port: "29019"
    rcon_password: "24fnn3y8f4n308"
    battlemetrics_id: "31654621"
  - name: "EU Mrk"
    long_name: "EU Mrk"
    ip: "**************"
    rcon_port: "29019"
    rcon_password: "9uqwef927234qwef"
    battlemetrics_id: "32179603"
  - name: "NA Mrk"
    long_name: "NA Mrk"
    ip: "************"
    rcon_port: "28016"
    rcon_password: "1Lygttv58vbEEP"
    battlemetrics_id: "33281924"

# Staff Management System Configuration
StaffManagement:
  # BattleMetrics Organization ID for tracking staff in-game hours
  BattlemetricsORGID: "106455"

  # Channels for Battlemetrics requests
  Channels:
    # Channel for watchlist notifications
    WatchlistChannelID: "1368432428917461082"
    # Channel for ban requests
    BanRequestsChannelID: "1368432428917461082"
    # Channel for spectate requests
    SpectateRequestsChannelID: "1368432428917461082"

  # Admin roles that can review requests
  AdminRoles:
    - "1329591326324560044"
    - "1339537106737037375"

  Images:
    StaffStatusEmbed: "https://cdn.discordapp.com/attachments/1329591447468507247/1329979591426834595/npb3h0u.gif?ex=68180e84&is=6816bd04&hm=9c88ba0fffdde1304826fde54ec7ebd9496c33f42489d61d3ddea3e7a6d3299c&"

  # Staff Activity Tracking
  ActivityTracking:
    Enabled: true
    # Channel to display online staff
    OnlineStaffChannelID: "1347546446693662801"
    # Channel for weekly staff activity reviews
    WeeklyReviewChannelID: "1367609269360394261"
    # Whether to DM staff with their weekly activity
    MessageStaffWeeklyReview: true

  # Commands for onloading staff to servers
  OnloadingCommands:
    - title: "ModeratorID Response"
      cmd: "moderatorid {steam_id}"
    - title: "Oxide Group Response"
      cmd: "o.usergroup add {steam_id} admin"
    - title: "Server Save Response"
      cmd: "server.writecfg"

  # Commands for offloading staff from servers
  OffloadingCommands:
    - title: "ModeratorID Response"
      cmd: "removemoderator {steam_id}"
    - title: "Oxide Group Response"
      cmd: "o.usergroup remove {steam_id} admin"
    - title: "Server Save Response"
      cmd: "server.writecfg"
