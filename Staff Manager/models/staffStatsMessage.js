const mongoose = require('mongoose');

/**
 * Schema for tracking staff statistics message IDs
 * This allows the bot to update existing staff stats embeds instead of creating new ones
 */
const staffStatsMessageSchema = new mongoose.Schema({
    staffDiscordId: {
        type: String,
        required: true,
        index: true,
        comment: 'Discord ID of the staff member'
    },
    channelId: {
        type: String,
        required: true,
        comment: 'Channel ID where the stats message was sent'
    },
    messageId: {
        type: String,
        required: true,
        comment: 'Message ID of the stats embed'
    },
    lastUpdated: {
        type: Date,
        default: Date.now,
        comment: 'When the message was last updated'
    },
    requestedBy: {
        type: String,
        required: true,
        comment: 'Discord ID of the user who requested the stats'
    }
}, {
    timestamps: true
});

// Compound index for efficient queries
staffStatsMessageSchema.index({ staffDiscordId: 1, channelId: 1 });
staffStatsMessageSchema.index({ messageId: 1 });
staffStatsMessageSchema.index({ lastUpdated: -1 });

/**
 * Static method to save or update a staff stats message
 */
staffStatsMessageSchema.statics.saveStatsMessage = async function(staffDiscordId, channelId, messageId, requestedBy) {
    return await this.findOneAndUpdate(
        { staffDiscordId, channelId },
        {
            messageId,
            requestedBy,
            lastUpdated: new Date()
        },
        { upsert: true, new: true }
    );
};

/**
 * Static method to get a staff stats message
 */
staffStatsMessageSchema.statics.getStatsMessage = async function(staffDiscordId, channelId) {
    return await this.findOne({ staffDiscordId, channelId });
};

/**
 * Static method to remove old stats messages (cleanup)
 */
staffStatsMessageSchema.statics.cleanupOldMessages = async function(olderThanDays = 7) {
    const cutoffDate = new Date(Date.now() - (olderThanDays * 24 * 60 * 60 * 1000));
    return await this.deleteMany({ lastUpdated: { $lt: cutoffDate } });
};

/**
 * Static method to get all stats messages in a channel
 */
staffStatsMessageSchema.statics.getChannelStatsMessages = async function(channelId) {
    return await this.find({ channelId }).sort({ lastUpdated: -1 });
};

module.exports = mongoose.model('StaffStatsMessage', staffStatsMessageSchema);
