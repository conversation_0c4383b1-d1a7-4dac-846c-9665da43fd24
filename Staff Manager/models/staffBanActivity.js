const mongoose = require('mongoose');

/**
 * Schema for tracking staff ban activity
 */
const staffBanActivitySchema = new mongoose.Schema({
    staffDiscordId: {
        type: String,
        required: true,
        index: true,
        comment: 'Discord ID of the staff member who issued the ban'
    },
    banId: {
        type: String,
        required: true,
        unique: true,
        comment: 'BattleMetrics ban ID'
    },
    playerBMId: {
        type: String,
        required: true,
        comment: 'BattleMetrics player ID of the banned player'
    },
    playerSteamId: {
        type: String,
        default: 'Unknown',
        comment: 'Steam ID of the banned player'
    },
    playerName: {
        type: String,
        default: 'Unknown',
        comment: 'Name of the banned player'
    },
    reason: {
        type: String,
        required: true,
        comment: 'Reason for the ban'
    },
    serverName: {
        type: String,
        required: true,
        comment: 'Server where the ban was issued'
    },
    serverId: {
        type: String,
        required: true,
        comment: 'BattleMetrics server ID'
    },
    banNote: {
        type: String,
        default: '',
        comment: 'Additional notes about the ban'
    },
    expiresAt: {
        type: Date,
        default: null,
        comment: 'When the ban expires (null for permanent bans)'
    },
    issuedAt: {
        type: Date,
        default: Date.now,
        comment: 'When the ban was issued'
    },
    processed: {
        type: Boolean,
        default: false,
        comment: 'Whether this ban has been processed for statistics'
    }
}, {
    timestamps: true
});

// Index for efficient queries
staffBanActivitySchema.index({ staffDiscordId: 1, issuedAt: -1 });
staffBanActivitySchema.index({ banId: 1 });
staffBanActivitySchema.index({ processed: 1 });

module.exports = mongoose.model('StaffBanActivity', staffBanActivitySchema);
