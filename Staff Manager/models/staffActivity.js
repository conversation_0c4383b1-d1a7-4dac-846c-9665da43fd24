const mongoose = require('mongoose');

/**
 * Schema for tracking staff activity
 */
const staffActivitySchema = new mongoose.Schema({
    _id: { type: String, required: true }, // Discord ID as primary key
    
    // Messages
    messages: {
        weekly: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
    },
    
    // Voice activity
    voice: {
        weekly: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
    },
    
    // In-game activity
    ingame: {
        weekly: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
    },
    
    // Ticket activity
    tickets: {
        weekly: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
    },
    
    // Ban activity
    bans: {
        weekly: { type: Number, default: 0 },
        total: { type: Number, default: 0 }
    },
    
    lastUpdated: { type: Date, default: Date.now }
});

module.exports = mongoose.model('StaffActivity', staffActivitySchema);
