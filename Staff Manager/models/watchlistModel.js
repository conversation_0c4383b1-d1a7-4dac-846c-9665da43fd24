const mongoose = require('mongoose');

/**
 * Schema for watchlist entries
 */
const watchlistSchema = new mongoose.Schema({
    steamId: {
        type: String,
        required: true,
        index: true
    },
    battlemetricsId: {
        type: String,
        default: null
    },
    playerName: {
        type: String,
        default: 'Unknown'
    },
    requestedBy: {
        type: String,
        required: true,
        comment: 'Discord ID of the user who requested the watch'
    },
    requestedAt: {
        type: Date,
        default: Date.now
    },
    lastSeen: {
        type: Date,
        default: null
    },
    isActive: {
        type: Boolean,
        default: true
    },
    notificationSent: {
        type: Boolean,
        default: false
    },
    lastNotificationSent: {
        type: Date,
        default: null
    },
    playerDetails: {
        type: mongoose.Schema.Types.Mixed,
        default: null,
        comment: 'Detailed player information from BattleMetrics'
    }
});

module.exports = mongoose.model('Watchlist', watchlistSchema);
