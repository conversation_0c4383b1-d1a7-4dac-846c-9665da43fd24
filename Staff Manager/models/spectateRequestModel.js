const mongoose = require('mongoose');

/**
 * Schema for spectate requests
 */
const spectateRequestSchema = new mongoose.Schema({
    steamId: {
        type: String,
        required: true,
        index: true
    },
    battlemetricsId: {
        type: String,
        default: null
    },
    playerName: {
        type: String,
        default: 'Unknown'
    },
    reason: {
        type: String,
        required: true
    },
    requestedBy: {
        type: String,
        required: true,
        comment: 'Discord ID of the user who requested the spectate'
    },
    requestedAt: {
        type: Date,
        default: Date.now
    },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending'
    },
    reviewedBy: {
        type: String,
        default: null,
        comment: 'Discord ID of the staff member who reviewed the request'
    },
    reviewedAt: {
        type: Date,
        default: null
    },
    reviewNotes: {
        type: String,
        default: ''
    },
    playerDetails: {
        type: mongoose.Schema.Types.Mixed,
        default: null,
        comment: 'Detailed player information from BattleMetrics'
    }
});

module.exports = mongoose.model('SpectateRequest', spectateRequestSchema);
