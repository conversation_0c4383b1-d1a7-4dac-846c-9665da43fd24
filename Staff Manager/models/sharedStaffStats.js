const mongoose = require('mongoose');

/**
 * Schema for shared staff statistics between Manager <PERSON> and Staff Manager
 * This allows both bots to track and update staff activity
 */
const sharedStaffStatsSchema = new mongoose.Schema({
    staffDiscordId: {
        type: String,
        required: true,
        unique: true,
        index: true,
        comment: 'Discord ID of the staff member'
    },
    
    // Ticket Statistics
    tickets: {
        totalClaimed: { type: Number, default: 0 },
        weeklyClaimed: { type: Number, default: 0 },
        monthlyClaimed: { type: Number, default: 0 },
        lastTicketClaimed: { type: Date, default: null }
    },
    
    // Message Statistics
    messages: {
        total: { type: Number, default: 0 },
        weekly: { type: Number, default: 0 },
        monthly: { type: Number, default: 0 },
        lastMessage: { type: Date, default: null }
    },
    
    // Ban Statistics
    bans: {
        total: { type: Number, default: 0 },
        weekly: { type: Number, default: 0 },
        monthly: { type: Number, default: 0 },
        lastBan: { type: Date, default: null }
    },
    
    // Voice Activity (tracked by Staff Manager)
    voice: {
        totalMinutes: { type: Number, default: 0 },
        weeklyMinutes: { type: Number, default: 0 },
        monthlyMinutes: { type: Number, default: 0 },
        lastVoiceActivity: { type: Date, default: null }
    },
    
    // In-game Activity (tracked by Staff Manager)
    ingame: {
        totalMinutes: { type: Number, default: 0 },
        weeklyMinutes: { type: Number, default: 0 },
        monthlyMinutes: { type: Number, default: 0 },
        lastIngameActivity: { type: Date, default: null }
    },
    
    // Weekly reset tracking
    weeklyResetDate: {
        type: Date,
        default: () => {
            const now = new Date();
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay() + 1); // Monday
            startOfWeek.setHours(0, 0, 0, 0);
            return startOfWeek;
        }
    },
    
    // Monthly reset tracking
    monthlyResetDate: {
        type: Date,
        default: () => {
            const now = new Date();
            return new Date(now.getFullYear(), now.getMonth(), 1);
        }
    },
    
    lastUpdated: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

// Indexes for efficient queries
sharedStaffStatsSchema.index({ staffDiscordId: 1 });
sharedStaffStatsSchema.index({ lastUpdated: -1 });
sharedStaffStatsSchema.index({ weeklyResetDate: 1 });
sharedStaffStatsSchema.index({ monthlyResetDate: 1 });

/**
 * Static method to update ticket statistics
 */
sharedStaffStatsSchema.statics.updateTicketStats = async function(staffDiscordId) {
    const now = new Date();
    
    // Check if we need to reset weekly/monthly stats
    await this.checkAndResetStats(staffDiscordId, now);
    
    return await this.findOneAndUpdate(
        { staffDiscordId },
        {
            $inc: {
                'tickets.totalClaimed': 1,
                'tickets.weeklyClaimed': 1,
                'tickets.monthlyClaimed': 1
            },
            $set: {
                'tickets.lastTicketClaimed': now,
                lastUpdated: now
            }
        },
        { upsert: true, new: true }
    );
};

/**
 * Static method to update ban statistics
 */
sharedStaffStatsSchema.statics.updateBanStats = async function(staffDiscordId) {
    const now = new Date();
    
    // Check if we need to reset weekly/monthly stats
    await this.checkAndResetStats(staffDiscordId, now);
    
    return await this.findOneAndUpdate(
        { staffDiscordId },
        {
            $inc: {
                'bans.total': 1,
                'bans.weekly': 1,
                'bans.monthly': 1
            },
            $set: {
                'bans.lastBan': now,
                lastUpdated: now
            }
        },
        { upsert: true, new: true }
    );
};

/**
 * Static method to update message statistics
 */
sharedStaffStatsSchema.statics.updateMessageStats = async function(staffDiscordId, count = 1) {
    const now = new Date();
    
    // Check if we need to reset weekly/monthly stats
    await this.checkAndResetStats(staffDiscordId, now);
    
    return await this.findOneAndUpdate(
        { staffDiscordId },
        {
            $inc: {
                'messages.total': count,
                'messages.weekly': count,
                'messages.monthly': count
            },
            $set: {
                'messages.lastMessage': now,
                lastUpdated: now
            }
        },
        { upsert: true, new: true }
    );
};

/**
 * Static method to check and reset weekly/monthly stats
 */
sharedStaffStatsSchema.statics.checkAndResetStats = async function(staffDiscordId, currentDate = new Date()) {
    const stats = await this.findOne({ staffDiscordId });
    if (!stats) return;
    
    const updates = {};
    let needsUpdate = false;
    
    // Check weekly reset (Monday)
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay() + 1);
    startOfWeek.setHours(0, 0, 0, 0);
    
    if (stats.weeklyResetDate < startOfWeek) {
        updates['tickets.weeklyClaimed'] = 0;
        updates['messages.weekly'] = 0;
        updates['bans.weekly'] = 0;
        updates['voice.weeklyMinutes'] = 0;
        updates['ingame.weeklyMinutes'] = 0;
        updates.weeklyResetDate = startOfWeek;
        needsUpdate = true;
    }
    
    // Check monthly reset
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    
    if (stats.monthlyResetDate < startOfMonth) {
        updates['tickets.monthlyClaimed'] = 0;
        updates['messages.monthly'] = 0;
        updates['bans.monthly'] = 0;
        updates['voice.monthlyMinutes'] = 0;
        updates['ingame.monthlyMinutes'] = 0;
        updates.monthlyResetDate = startOfMonth;
        needsUpdate = true;
    }
    
    if (needsUpdate) {
        await this.updateOne({ staffDiscordId }, { $set: updates });
    }
};

module.exports = mongoose.model('SharedStaffStats', sharedStaffStatsSchema);
