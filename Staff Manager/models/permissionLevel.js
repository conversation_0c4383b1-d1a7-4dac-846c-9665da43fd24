const mongoose = require('mongoose');

/**
 * Schema for permission levels and role configurations
 */
const permissionLevelSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        comment: 'Name of the permission level (e.g., Admin, Moderator, Helper)'
    },
    displayName: {
        type: String,
        required: true,
        comment: 'Display name for the permission level'
    },
    description: {
        type: String,
        default: '',
        comment: 'Description of what this permission level includes'
    },
    priority: {
        type: Number,
        required: true,
        default: 0,
        comment: 'Priority level (higher = more permissions)'
    },
    discordRoles: [{
        type: String,
        comment: 'Discord role IDs to assign'
    }],
    serverCommands: [{
        title: {
            type: String,
            required: true,
            comment: 'Display title for the command'
        },
        command: {
            type: String,
            required: true,
            comment: 'RCON command to execute (use {steam_id} placeholder)'
        },
        description: {
            type: String,
            default: '',
            comment: 'Description of what this command does'
        }
    }],
    offloadCommands: [{
        title: {
            type: String,
            required: true,
            comment: 'Display title for the offload command'
        },
        command: {
            type: String,
            required: true,
            comment: 'RCON command to execute for removal (use {steam_id} placeholder)'
        },
        description: {
            type: String,
            default: '',
            comment: 'Description of what this command does'
        }
    }],
    permissions: {
        canManageStaff: { type: Boolean, default: false },
        canViewAllStats: { type: Boolean, default: false },
        canManageServers: { type: Boolean, default: false },
        canIssueBans: { type: Boolean, default: false },
        canManageTickets: { type: Boolean, default: false },
        canAccessAdminCommands: { type: Boolean, default: false }
    },
    active: {
        type: Boolean,
        default: true,
        comment: 'Whether this permission level is active'
    },
    color: {
        type: String,
        default: '#CCCCCC',
        comment: 'Color for embeds and displays'
    }
}, {
    timestamps: true
});

// Index for efficient queries
permissionLevelSchema.index({ name: 1 });
permissionLevelSchema.index({ priority: -1 });
permissionLevelSchema.index({ active: 1 });

/**
 * Static method to get permission level by name
 */
permissionLevelSchema.statics.getByName = async function(name) {
    return await this.findOne({ name: name.toLowerCase(), active: true });
};

/**
 * Static method to get all active permission levels sorted by priority
 */
permissionLevelSchema.statics.getAllActive = async function() {
    return await this.find({ active: true }).sort({ priority: -1 });
};

/**
 * Static method to get permission level for a staff member
 */
permissionLevelSchema.statics.getStaffPermissionLevel = async function(discordId) {
    // This would need to be implemented based on how you track staff member permission levels
    // For now, return null - this can be enhanced later
    return null;
};

/**
 * Instance method to check if this level has a specific permission
 */
permissionLevelSchema.methods.hasPermission = function(permission) {
    return this.permissions[permission] || false;
};

/**
 * Instance method to get all commands for onloading
 */
permissionLevelSchema.methods.getOnloadCommands = function() {
    return this.serverCommands || [];
};

/**
 * Instance method to get all commands for offloading
 */
permissionLevelSchema.methods.getOffloadCommands = function() {
    return this.offloadCommands || [];
};

module.exports = mongoose.model('PermissionLevel', permissionLevelSchema);
