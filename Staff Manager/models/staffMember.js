const mongoose = require('mongoose');

/**
 * Schema for staff members
 */
const staffMemberSchema = new mongoose.Schema({
    _id: { type: String, required: true }, // Discord ID as primary key
    discord_name: { type: String, required: true },
    steam_id: { type: String, required: true },
    bm_id: { type: String, default: null },
    staff_bm_id: { type: String, default: null },
    region_info: {
        region: { type: String, default: 'unknown' },
        timezone: { type: String, default: 'UTC' },
        flag: { type: String, default: null },
        country: { type: String, default: null },
        continent: { type: String, default: null }
    },
    active: { type: Boolean, default: true },
    created_at: { type: Date, default: Date.now },
    updated_at: { type: Date, default: Date.now }
});

// Update the updated_at field on save
staffMemberSchema.pre('save', function(next) {
    this.updated_at = new Date();
    next();
});

module.exports = mongoose.model('StaffMember', staffMemberSchema);
