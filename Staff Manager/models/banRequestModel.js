const mongoose = require('mongoose');

/**
 * Schema for ban requests
 */
const banRequestSchema = new mongoose.Schema({
    steamId: {
        type: String,
        required: true,
        index: true
    },
    battlemetricsId: {
        type: String,
        default: null
    },
    playerName: {
        type: String,
        default: 'Unknown'
    },
    reason: {
        type: String,
        required: true
    },
    evidence: {
        type: [String],
        default: []
    },
    requestedBy: {
        type: String,
        required: true,
        comment: 'Discord ID of the user who requested the ban'
    },
    requestedAt: {
        type: Date,
        default: Date.now
    },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending'
    },
    reviewedBy: {
        type: String,
        default: null,
        comment: 'Discord ID of the staff member who reviewed the request'
    },
    reviewedAt: {
        type: Date,
        default: null
    },
    reviewNotes: {
        type: String,
        default: ''
    },
    banId: {
        type: String,
        default: null,
        comment: 'Battlemetrics ban ID if the ban was applied'
    },
    playerDetails: {
        type: mongoose.Schema.Types.Mixed,
        default: null,
        comment: 'Detailed player information from BattleMetrics'
    }
});

module.exports = mongoose.model('BanRequest', banRequestSchema);
