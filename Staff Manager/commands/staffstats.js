const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const StaffMember = require('../models/staffMember');
const StaffActivity = require('../models/staffActivity');
const BattleMetricsClient = require('../utils/battlemetricsClient');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('total-staffstats')
        .setDescription('Display statistics for all staff members')
        .addBooleanOption(option =>
            option.setName('online_only')
                .setDescription('Show only online staff members')
                .setRequired(false))
        .addBooleanOption(option =>
            option.setName('compact')
                .setDescription('Show compact view with multiple staff per field')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('sort')
                .setDescription('Sort staff by a specific attribute')
                .setRequired(false)
                .addChoices(
                    { name: 'Name', value: 'name' },
                    { name: 'Region', value: 'region' },
                    { name: 'Hours', value: 'hours' },
                    { name: 'Last Active', value: 'active' }
                )),

    async execute(interaction, client) {
        await interaction.deferReply();

        try {
            // Get command options
            const onlineOnly = interaction.options.getBoolean('online_only') || false;
            const compactView = interaction.options.getBoolean('compact') || false;
            const sortOption = interaction.options.getString('sort') || 'name';

            // Fetch all staff members from the database
            const staffMembers = await StaffMember.find({}).sort({ discord_name: 1 });

            if (!staffMembers || staffMembers.length === 0) {
                return interaction.editReply('No staff members found in the database.');
            }

            // Add a status message while processing
            const statusMessage = await interaction.editReply(`Processing information for ${staffMembers.length} staff members... This may take a moment.`);

            // Create an array to hold all the staff information
            const staffInfoArray = [];

            // Create a progress embed that we'll update as we process each staff member
            const progressEmbed = new EmbedBuilder()
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setDescription(`Processing staff information...`);

            // Process each staff member
            for (let i = 0; i < staffMembers.length; i++) {
                const staffMember = staffMembers[i];
                try {
                    // Update progress embed
                    progressEmbed.setDescription(`Processing staff member ${i+1}/${staffMembers.length}: ${staffMember.discord_name || staffMember.discord_id}`);
                    await interaction.editReply({ content: '', embeds: [progressEmbed] });

                    // Get Discord user information
                    let discordUser = null;
                    try {
                        discordUser = await client.users.fetch(staffMember.discord_id);
                    } catch (error) {
                        console.error(`Failed to fetch Discord user for ${staffMember.discord_id}:`, error);
                    }

                    // Get BattleMetrics information if available
                    let bmInfo = { region: 'Unknown', flag: null, online: false, server: null };
                    if (staffMember.bm_id) {
                        try {
                            const regionResponse = await BattleMetricsClient.getPlayerRegionInfo(staffMember.bm_id);
                            if (regionResponse.status === 200 && regionResponse.regionInfo) {
                                bmInfo.region = regionResponse.regionInfo.region;
                                bmInfo.flag = regionResponse.regionInfo.flag;
                            }

                            // Check if staff is online
                            const presenceResponse = await BattleMetricsClient.getPlayerPresence(staffMember.bm_id);
                            if (presenceResponse.status === 200) {
                                bmInfo.online = presenceResponse.online;
                                bmInfo.server = presenceResponse.server;
                            }
                        } catch (error) {
                            console.error(`Failed to fetch BattleMetrics info for ${staffMember.discord_id}:`, error);
                        }
                    }

                    // Get activity information
                    let activityInfo = {
                        totalHours: 0,
                        lastActive: 'Never',
                        messages: 0,
                        voice: 0,
                        ingame: 0,
                        tickets: 0,
                        bans: 0
                    };
                    try {
                        const activity = await StaffActivity.findOne({ _id: staffMember._id });
                        if (activity) {
                            // Sum up all activity types for total hours
                            activityInfo.ingame = activity.ingame?.total || 0;
                            activityInfo.voice = activity.voice?.total || 0;
                            activityInfo.totalHours = (activityInfo.ingame + activityInfo.voice) / 60; // Convert minutes to hours

                            // Get other activity stats
                            activityInfo.messages = activity.messages?.total || 0;
                            activityInfo.tickets = activity.tickets?.total || 0;
                            activityInfo.bans = activity.bans?.total || 0;

                            // Last active timestamp
                            activityInfo.lastActive = activity.lastUpdated ?
                                new Date(activity.lastUpdated).toISOString().split('T')[0] : 'Never';
                        }
                    } catch (error) {
                        console.error(`Failed to fetch activity info for ${staffMember._id}:`, error);
                    }

                    // Format the staff member information
                    const staffInfo = {
                        name: discordUser ? discordUser.tag : staffMember.discord_name || 'Unknown',
                        id: staffMember._id,
                        avatar: discordUser ? discordUser.displayAvatarURL() : null,
                        steamId: staffMember.steam_id || 'Not set',
                        bmId: staffMember.bm_id || 'Not set',
                        region: bmInfo.region,
                        flag: bmInfo.flag,
                        online: bmInfo.online,
                        server: bmInfo.server,
                        totalHours: activityInfo.totalHours.toFixed(1),
                        ingameHours: (activityInfo.ingame / 60).toFixed(1),
                        voiceHours: (activityInfo.voice / 60).toFixed(1),
                        messages: activityInfo.messages,
                        tickets: activityInfo.tickets,
                        bans: activityInfo.bans,
                        lastActive: activityInfo.lastActive,
                        active: staffMember.active ? 'Active' : 'Inactive',
                        regionInfo: staffMember.region_info || {}
                    };

                    staffInfoArray.push(staffInfo);
                } catch (error) {
                    console.error(`Error processing staff member ${staffMember.discord_id}:`, error);
                    // Continue with the next staff member
                }
            }

            // Filter staff members if online_only is selected
            let filteredStaffInfo = [...staffInfoArray];
            if (onlineOnly) {
                filteredStaffInfo = filteredStaffInfo.filter(staff => staff.online);
            }

            // Sort staff members based on the selected option
            filteredStaffInfo.sort((a, b) => {
                switch (sortOption) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'region':
                        return a.region.localeCompare(b.region);
                    case 'hours':
                        return parseFloat(b.totalHours) - parseFloat(a.totalHours); // Descending
                    case 'active':
                        // Sort by last active date (most recent first)
                        const dateA = a.lastActive === 'Never' ? new Date(0) : new Date(a.lastActive);
                        const dateB = b.lastActive === 'Never' ? new Date(0) : new Date(b.lastActive);
                        return dateB - dateA;
                    default:
                        return a.name.localeCompare(b.name);
                }
            });

            // Create embeds with the staff information
            const embeds = [];
            let currentEmbed = new EmbedBuilder()
                .setTitle('Staff Statistics')
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setDescription(`Showing ${filteredStaffInfo.length} staff members${onlineOnly ? ' (Online Only)' : ''}`)
                .setTimestamp();

            // Add sorting information to the embed
            currentEmbed.setFooter({
                text: `Sorted by: ${sortOption.charAt(0).toUpperCase() + sortOption.slice(1)} | ${compactView ? 'Compact View' : 'Detailed View'}`
            });

            let fieldCount = 0;
            const MAX_FIELDS_PER_EMBED = 25; // Discord allows up to 25 fields per embed

            // Handle compact view (multiple staff per field)
            if (compactView) {
                // Group staff by region for compact view
                const staffByRegion = {};

                for (const staffInfo of filteredStaffInfo) {
                    const region = staffInfo.region || 'Unknown';
                    if (!staffByRegion[region]) {
                        staffByRegion[region] = [];
                    }
                    staffByRegion[region].push(staffInfo);
                }

                // Create fields for each region
                for (const [region, staffList] of Object.entries(staffByRegion)) {
                    // Check if we need to create a new embed
                    if (fieldCount >= MAX_FIELDS_PER_EMBED) {
                        embeds.push(currentEmbed);
                        currentEmbed = new EmbedBuilder()
                            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC');
                        fieldCount = 0;
                    }

                    // Create compact field value with multiple staff
                    let fieldValue = '';
                    for (const staff of staffList) {
                        const onlineEmoji = staff.online ? '🟢' : '🔴';
                        fieldValue += `${onlineEmoji} **${staff.name}** • ${staff.totalHours}h`;
                        if (staff.online && staff.server) {
                            fieldValue += ` • ${staff.server}`;
                        }
                        // Add last active date
                        fieldValue += ` • Last: ${staff.lastActive}`;
                        fieldValue += '\n';
                    }

                    // Add the field to the current embed
                    currentEmbed.addFields({
                        name: `${region} Region (${staffList.length} staff)`,
                        value: fieldValue || 'No staff in this region',
                        inline: false
                    });

                    fieldCount++;
                }
            } else {
                // Detailed view (one staff per field)
                for (const staffInfo of filteredStaffInfo) {
                    // No need for additional processing, we'll format directly in the field value

                    // Format the field value to match the image style
                    const fieldValue = [
                        `**Discord:** <@${staffInfo.id}>`,
                        `**Steam ID:** \`\`${staffInfo.steamId}\`\``,
                        `**BM ID:** \`\`${staffInfo.bmId}\`\``,
                        `**Account:** \`\`${staffInfo.active}\`\``,
                        `**Hours:** \`\`${staffInfo.totalHours}h\`\` (In-game: \`\`${staffInfo.ingameHours}h\`\` | Voice: \`\`${staffInfo.voiceHours}h\`\`)`,
                        `**Activity:** \`\`${staffInfo.messages}\`\` messages | \`\`${staffInfo.tickets}\`\` tickets | \`\`${staffInfo.bans}\`\` bans`,
                        `**Last Active:** \`\`${staffInfo.lastActive}\`\``,
                        `-------------------------------`
                    ].join('\n');

                    // Check if we need to create a new embed
                    if (fieldCount >= MAX_FIELDS_PER_EMBED) {
                        embeds.push(currentEmbed);
                        currentEmbed = new EmbedBuilder()
                            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC');
                        fieldCount = 0;
                    }

                    // Add the field to the current embed
                    currentEmbed.addFields({
                        name: `${staffInfo.flag ? ` ${staffInfo.flag}` : ''} | ${staffInfo.name}`,
                        value: fieldValue,
                        inline: false
                    });

                    fieldCount++;
                }
            }

            // Add the last embed if it has fields
            if (fieldCount > 0) {
                embeds.push(currentEmbed);
            }

            // Send the embeds
            if (embeds.length === 0) {
                return interaction.editReply('No staff information to display.');
            } else if (embeds.length === 1) {
                // If there's only one embed, just send it without pagination
                return interaction.editReply({ content: ``, embeds: [embeds[0]] });
            } else {
                // Create pagination buttons
                const prevButton = new ButtonBuilder()
                    .setCustomId('prev_page')
                    .setLabel('Previous')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(true); // Disabled on first page

                const nextButton = new ButtonBuilder()
                    .setCustomId('next_page')
                    .setLabel('Next')
                    .setStyle(ButtonStyle.Primary);

                const pageIndicator = new ButtonBuilder()
                    .setCustomId('page_indicator')
                    .setLabel(`Page 1/${embeds.length}`)
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true);

                const row = new ActionRowBuilder().addComponents(prevButton, pageIndicator, nextButton);

                // Send the first embed with pagination buttons
                const message = await interaction.editReply({
                    embeds: [embeds[0]],
                    components: [row]
                });

                // Create a collector for button interactions
                const collector = message.createMessageComponentCollector({
                    filter: i => i.user.id === interaction.user.id,
                    time: 300000 // 5 minutes
                });

                let currentPage = 0;

                collector.on('collect', async i => {
                    // Update the current page based on the button clicked
                    if (i.customId === 'prev_page') {
                        currentPage--;
                    } else if (i.customId === 'next_page') {
                        currentPage++;
                    }

                    // Update button states
                    prevButton.setDisabled(currentPage === 0);
                    nextButton.setDisabled(currentPage === embeds.length - 1);
                    pageIndicator.setLabel(`Page ${currentPage + 1}/${embeds.length}`);

                    // Update the row with the new button states
                    const updatedRow = new ActionRowBuilder().addComponents(prevButton, pageIndicator, nextButton);

                    // Update the message with the new embed and buttons
                    await i.update({
                        embeds: [embeds[currentPage]],
                        components: [updatedRow]
                    });
                });

                collector.on('end', async () => {
                    // Disable all buttons when the collector ends
                    prevButton.setDisabled(true);
                    nextButton.setDisabled(true);
                    pageIndicator.setDisabled(true);

                    const disabledRow = new ActionRowBuilder().addComponents(prevButton, pageIndicator, nextButton);

                    // Try to update the message with disabled buttons
                    try {
                        await message.edit({
                            embeds: [embeds[currentPage]],
                            components: [disabledRow]
                        });
                    } catch (error) {
                        console.error('Failed to update message after collector ended:', error);
                    }
                });
            }
        } catch (error) {
            console.error('Error executing staffstats command:', error);
            return interaction.editReply(`Error: ${error.message}`);
        }
    }
};
