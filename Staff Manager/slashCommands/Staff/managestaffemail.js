const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const StaffMember = require('../../models/staffMember');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('managestaffemail')
        .setDescription('Manage staff member BattleMetrics emails for ban attribution')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('set')
                .setDescription('Set a staff member\'s BattleMetrics email')
                .addUserOption(option =>
                    option.setName('staff')
                        .setDescription('Staff member to set email for')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('email')
                        .setDescription('BattleMetrics email address')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('remove')
                .setDescription('Remove a staff member\'s BattleMetrics email')
                .addUserOption(option =>
                    option.setName('staff')
                        .setDescription('Staff member to remove email from')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all staff members and their BattleMetrics emails'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('view')
                .setDescription('View a specific staff member\'s BattleMetrics email')
                .addUserOption(option =>
                    option.setName('staff')
                        .setDescription('Staff member to view email for')
                        .setRequired(true))),

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'set':
                    await handleSetEmail(interaction);
                    break;
                case 'remove':
                    await handleRemoveEmail(interaction);
                    break;
                case 'list':
                    await handleListEmails(interaction);
                    break;
                case 'view':
                    await handleViewEmail(interaction);
                    break;
                default:
                    await interaction.editReply({
                        content: '❌ Unknown subcommand.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('Error in managestaffemail command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while managing staff emails.',
                ephemeral: true
            });
        }
    }
};

async function handleSetEmail(interaction) {
    const targetUser = interaction.options.getUser('staff');
    const email = interaction.options.getString('email');

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return await interaction.editReply({
            content: '❌ Please provide a valid email address.',
            ephemeral: true
        });
    }

    // Check if staff member exists
    let staffMember = await StaffMember.findById(targetUser.id);
    if (!staffMember) {
        return await interaction.editReply({
            content: '❌ The specified user is not a registered staff member. Please add them as a staff member first.',
            ephemeral: true
        });
    }

    // Check if email is already in use by another staff member
    const existingStaff = await StaffMember.findOne({ 
        bm_email: email,
        _id: { $ne: targetUser.id }
    });

    if (existingStaff) {
        return await interaction.editReply({
            content: `❌ This email is already assigned to <@${existingStaff._id}>.`,
            ephemeral: true
        });
    }

    // Update staff member with email
    staffMember.bm_email = email;
    staffMember.updated_at = new Date();
    await staffMember.save();

    const embed = new EmbedBuilder()
        .setTitle('✅ Staff Email Updated')
        .setColor('#4CAF50')
        .addFields([
            { name: 'Staff Member', value: `<@${targetUser.id}>`, inline: true },
            { name: 'BattleMetrics Email', value: email, inline: true }
        ])
        .setTimestamp()
        .setFooter({ text: 'This email will be used for ban attribution' });

    await interaction.editReply({ embeds: [embed] });
}

async function handleRemoveEmail(interaction) {
    const targetUser = interaction.options.getUser('staff');

    // Check if staff member exists
    const staffMember = await StaffMember.findById(targetUser.id);
    if (!staffMember) {
        return await interaction.editReply({
            content: '❌ The specified user is not a registered staff member.',
            ephemeral: true
        });
    }

    if (!staffMember.bm_email) {
        return await interaction.editReply({
            content: '❌ This staff member does not have a BattleMetrics email set.',
            ephemeral: true
        });
    }

    const oldEmail = staffMember.bm_email;
    staffMember.bm_email = null;
    staffMember.updated_at = new Date();
    await staffMember.save();

    const embed = new EmbedBuilder()
        .setTitle('✅ Staff Email Removed')
        .setColor('#FF9800')
        .addFields([
            { name: 'Staff Member', value: `<@${targetUser.id}>`, inline: true },
            { name: 'Removed Email', value: oldEmail, inline: true }
        ])
        .setTimestamp()
        .setFooter({ text: 'Bans will no longer be attributed to this staff member' });

    await interaction.editReply({ embeds: [embed] });
}

async function handleListEmails(interaction) {
    const staffMembers = await StaffMember.find({ active: true }).sort({ discord_name: 1 });

    if (staffMembers.length === 0) {
        return await interaction.editReply({
            content: '❌ No active staff members found.',
            ephemeral: true
        });
    }

    const embed = new EmbedBuilder()
        .setTitle('📧 Staff BattleMetrics Emails')
        .setColor('#2196F3')
        .setDescription('BattleMetrics emails used for ban attribution')
        .setTimestamp();

    const staffWithEmails = staffMembers.filter(staff => staff.bm_email);
    const staffWithoutEmails = staffMembers.filter(staff => !staff.bm_email);

    if (staffWithEmails.length > 0) {
        const emailList = staffWithEmails.map(staff => 
            `<@${staff._id}> - \`${staff.bm_email}\``
        ).join('\n');

        embed.addFields([{
            name: '✅ Staff with Emails',
            value: emailList.length > 1024 ? emailList.substring(0, 1021) + '...' : emailList,
            inline: false
        }]);
    }

    if (staffWithoutEmails.length > 0) {
        const noEmailList = staffWithoutEmails.map(staff => 
            `<@${staff._id}>`
        ).join(', ');

        embed.addFields([{
            name: '❌ Staff without Emails',
            value: noEmailList.length > 1024 ? noEmailList.substring(0, 1021) + '...' : noEmailList,
            inline: false
        }]);
    }

    embed.addFields([{
        name: '📊 Summary',
        value: `Total Staff: ${staffMembers.length}\nWith Emails: ${staffWithEmails.length}\nWithout Emails: ${staffWithoutEmails.length}`,
        inline: false
    }]);

    await interaction.editReply({ embeds: [embed] });
}

async function handleViewEmail(interaction) {
    const targetUser = interaction.options.getUser('staff');

    // Check if staff member exists
    const staffMember = await StaffMember.findById(targetUser.id);
    if (!staffMember) {
        return await interaction.editReply({
            content: '❌ The specified user is not a registered staff member.',
            ephemeral: true
        });
    }

    const embed = new EmbedBuilder()
        .setTitle('📧 Staff Email Information')
        .setColor('#2196F3')
        .addFields([
            { name: 'Staff Member', value: `<@${targetUser.id}>`, inline: true },
            { name: 'Discord Name', value: staffMember.discord_name, inline: true },
            { name: 'BattleMetrics Email', value: staffMember.bm_email || 'Not set', inline: false },
            { name: 'Status', value: staffMember.bm_email ? '✅ Email configured for ban attribution' : '❌ No email set - bans will not be attributed', inline: false }
        ])
        .setTimestamp()
        .setFooter({ text: `Last updated: ${staffMember.updated_at.toLocaleDateString()}` });

    if (staffMember.bm_email) {
        embed.setColor('#4CAF50');
    } else {
        embed.setColor('#FF9800');
    }

    await interaction.editReply({ embeds: [embed] });
}
