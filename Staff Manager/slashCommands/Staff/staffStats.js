const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const StaffManager = require('../../utils/staffManager');
const StaffStatsMessage = require('../../models/staffStatsMessage');
const moment = require('moment');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('staffstats')
        .setDescription('View staff statistics')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The staff member to view stats for')
                .setRequired(false))
        .addBooleanOption(option =>
            option.setName('cleanup')
                .setDescription('Remove all existing staff stats embeds in this channel')
                .setRequired(false)),

    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: false });

        // Get options
        const user = interaction.options.getUser('user') || interaction.user;
        const cleanup = interaction.options.getBoolean('cleanup') || false;

        // Handle cleanup option
        if (cleanup) {
            return await this.handleCleanup(interaction, client);
        }

        // Check if user is a staff member
        const staffMember = await StaffManager.getStaffMember(user.id);

        if (!staffMember) {
            return interaction.editReply(client.locals.Staff.RemoveStaff.NotStaff.replace('{user}', user.tag));
        }

        // Get staff activity
        const staffActivity = await StaffManager.getStaffActivity(user.id);

        if (!staffActivity) {
            return interaction.editReply('No activity data found for this staff member.');
        }

        // Create embed with clean format (no emojis)
        const embed = new EmbedBuilder()
            .setTitle('Staff Statistics')
            .setDescription('Showing 1 staff members')
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
            .setTimestamp();

        // Build staff info string
        let staffInfo = `**${staffMember.discord_name}**\n`;
        staffInfo += `Discord: @${user.tag}\n`;

        // Add Steam ID if available
        if (staffMember.steam_id) {
            staffInfo += `Steam ID: ${staffMember.steam_id}\n`;
        }

        // Add BM ID if available
        if (staffMember.bm_id) {
            staffInfo += `BM ID: ${staffMember.bm_id || 'Not set'}\n`;
        }

        // Add account status
        staffInfo += `Account: ${staffMember.account_status || 'Active'}\n`;

        // Add activity stats
        const voiceHours = (staffActivity.voice.total || 0).toFixed(1);
        const ingameHours = (staffActivity.ingame.total || 0).toFixed(1);
        staffInfo += `Hours: ${voiceHours}h (In-game: ${voiceHours}h | Voice: ${ingameHours}h )\n`;

        // Add activity summary
        const totalMessages = staffActivity.messages.total || 0;
        const totalTickets = staffActivity.tickets.total || 0;
        const totalBans = staffActivity.bans.total || 0;
        staffInfo += `Activity: ${totalMessages} messages | ${totalTickets} tickets | ${totalBans} bans\n`;

        // Add last active
        if (staffActivity.lastUpdated) {
            staffInfo += `Last Active: ${moment(staffActivity.lastUpdated).format('YYYY-MM-DD')}`;
        }

        embed.addFields({
            name: '\u200b',
            value: staffInfo,
            inline: false
        });

        // Add footer
        const currentTime = new Date().toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
        embed.setFooter({
            text: `Sorted by: Name | Detailed View • Today at ${currentTime}`
        });

        // Add buttons for onload/offload if the user has permission
        const hasPermission = interaction.member.permissions.has('MANAGE_ROLES');

        let components = [];
        if (hasPermission) {
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`staff_onload_${user.id}`)
                        .setLabel('Onload Staff')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId(`staff_offload_${user.id}`)
                        .setLabel('Offload Staff')
                        .setStyle(ButtonStyle.Danger)
                );
            components = [row];
        }

        // Use the staff stats message manager to handle updating/creating the message
        const result = await client.staffStatsMessageManager.updateStatsMessage(
            user.id,
            interaction.channel.id,
            embed,
            components,
            interaction.user.id
        );

        if (result.success) {
            if (result.updated) {
                // Message was updated, delete the interaction reply
                await interaction.deleteReply();
            } else {
                // New message was created, update the interaction reply to show success
                await interaction.editReply({
                    content: `✅ Staff statistics for ${user.tag} have been displayed.`,
                    embeds: [],
                    components: []
                });
            }
        } else {
            // Fallback to normal reply if manager fails
            await interaction.editReply({ embeds: [embed], components });
        }
    },

    /**
     * Handle cleanup of all staff stats embeds in the channel
     */
    async handleCleanup(interaction, client) {
        try {
            // Check if user has permission to cleanup
            if (!interaction.member.permissions.has('MANAGE_MESSAGES')) {
                return interaction.editReply('You need `Manage Messages` permission to cleanup staff stats embeds.');
            }

            // Use the manager to cleanup the channel
            const result = await client.staffStatsMessageManager.cleanupChannel(interaction.channel.id);

            if (!result.success) {
                return interaction.editReply(`Cleanup failed: ${result.error}`);
            }

            if (result.totalFound === 0) {
                return interaction.editReply('No staff stats embeds found in this channel.');
            }

            // Create summary embed
            const summaryEmbed = new EmbedBuilder()
                .setTitle('Staff Stats Cleanup Complete')
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Deleted', value: `${result.deletedCount} messages`, inline: true },
                    { name: 'Failed', value: `${result.failedCount} messages`, inline: true },
                    { name: 'Total Found', value: `${result.totalFound} messages`, inline: true }
                ])
                .setTimestamp()
                .setFooter({ text: 'All staff stats embeds have been removed from this channel' });

            return interaction.editReply({ embeds: [summaryEmbed] });

        } catch (error) {
            console.error('Error during cleanup:', error);
            return interaction.editReply('An error occurred during cleanup. Please try again.');
        }
    }
};


