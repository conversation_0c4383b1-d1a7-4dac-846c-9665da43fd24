const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const StaffManager = require('../../utils/staffManager');
const moment = require('moment');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('staffstats')
        .setDescription('View staff statistics')
        .addUserOption(option => 
            option.setName('user')
                .setDescription('The staff member to view stats for')
                .setRequired(false)),
    
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: false });
        
        // Get options
        const user = interaction.options.getUser('user') || interaction.user;
        
        // Check if user is a staff member
        const staffMember = await StaffManager.getStaffMember(user.id);
        
        if (!staffMember) {
            return interaction.editReply(client.locals.Staff.RemoveStaff.NotStaff.replace('{user}', user.tag));
        }
        
        // Get staff activity
        const staffActivity = await StaffManager.getStaffActivity(user.id);
        
        if (!staffActivity) {
            return interaction.editReply('No activity data found for this staff member.');
        }
        
        // Create embed
        const embed = new EmbedBuilder()
            .setTitle(client.locals.Staff.StaffStats.Title.replace('{user}', user.tag))
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
            .setThumbnail(user.displayAvatarURL({ dynamic: true }))
            .setTimestamp();
        
        // Add region info
        if (staffMember.region_info && staffMember.region_info.region) {
            const regionFlag = getRegionFlag(staffMember.region_info.region);
            const regionName = getRegionName(staffMember.region_info.region);
            
            embed.addFields({
                name: client.locals.Staff.StaffStats.Region || 'Region',
                value: `${regionFlag} ${regionName}`,
                inline: true
            });
        }
        
        // Add timezone info
        if (staffMember.region_info && staffMember.region_info.timezone) {
            embed.addFields({
                name: client.locals.Staff.StaffStats.Timezone || 'Timezone',
                value: staffMember.region_info.timezone,
                inline: true
            });
        }
        
        // Add last active info
        if (staffActivity.lastUpdated) {
            embed.addFields({
                name: client.locals.Staff.StaffStats.LastActive || 'Last Active',
                value: moment(staffActivity.lastUpdated).fromNow(),
                inline: true
            });
        }
        
        // Add weekly stats
        embed.addFields({
            name: client.locals.Staff.StaffStats.WeeklyStats || 'Weekly Stats',
            value: `> • **${staffActivity.messages.weekly}** ${client.locals.Staff.StaffStats.Messages || 'Messages'}\n` +
                   `> • **${staffActivity.voice.weekly.toFixed(1)}** ${client.locals.Staff.StaffStats.VoiceHours || 'Voice Hours'}\n` +
                   `> • **${staffActivity.tickets.weekly}** ${client.locals.Staff.StaffStats.Tickets || 'Tickets'}\n` +
                   `> • **${staffActivity.bans.weekly}** ${client.locals.Staff.StaffStats.Bans || 'Bans'}`,
            inline: false
        });
        
        // Add all-time stats
        embed.addFields({
            name: client.locals.Staff.StaffStats.AllTimeStats || 'All Time Stats',
            value: `> • **${staffActivity.messages.total}** ${client.locals.Staff.StaffStats.Messages || 'Messages'}\n` +
                   `> • **${staffActivity.voice.total.toFixed(1)}** ${client.locals.Staff.StaffStats.VoiceHours || 'Voice Hours'}\n` +
                   `> • **${staffActivity.ingame.total.toFixed(1)}** ${client.locals.Staff.StaffStats.IngameHours || 'Ingame Hours'}\n` +
                   `> • **${staffActivity.tickets.total}** ${client.locals.Staff.StaffStats.Tickets || 'Tickets'}\n` +
                   `> • **${staffActivity.bans.total}** ${client.locals.Staff.StaffStats.Bans || 'Bans'}`,
            inline: false
        });
        
        // Add buttons for onload/offload if the user has permission
        const hasPermission = interaction.member.permissions.has('MANAGE_ROLES');
        
        if (hasPermission) {
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`staff_onload_${user.id}`)
                        .setLabel('Onload Staff')
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId(`staff_offload_${user.id}`)
                        .setLabel('Offload Staff')
                        .setStyle(ButtonStyle.Danger)
                );
            
            return interaction.editReply({ embeds: [embed], components: [row] });
        }
        
        return interaction.editReply({ embeds: [embed] });
    }
};

/**
 * Get flag emoji for region
 * @param {string} region - Region name
 * @returns {string} - Flag emoji
 */
function getRegionFlag(region) {
    if (!region) return '';
    
    const regionFlags = {
        'na': '🇺🇸',
        'eu': '🇪🇺',
        'au': '🇦🇺',
        'sa': '🇧🇷',
        'as': '🇯🇵',
        'af': '🇿🇦',
        'uk': '🇬🇧',
        'ca': '🇨🇦',
        'us': '🇺🇸',
        'gb': '🇬🇧',
        'de': '🇩🇪',
        'fr': '🇫🇷',
        'es': '🇪🇸',
        'it': '🇮🇹',
        'ru': '🇷🇺',
        'cn': '🇨🇳',
        'jp': '🇯🇵',
        'kr': '🇰🇷',
        'br': '🇧🇷',
        'mx': '🇲🇽',
        'au': '🇦🇺',
        'nz': '🇳🇿'
    };
    
    return regionFlags[region.toLowerCase()] || '';
}

/**
 * Get region name
 * @param {string} region - Region code
 * @returns {string} - Region name
 */
function getRegionName(region) {
    if (!region) return 'Unknown';
    
    const regionNames = {
        'na': 'North America',
        'eu': 'Europe',
        'au': 'Australia',
        'sa': 'South America',
        'as': 'Asia',
        'af': 'Africa',
        'uk': 'United Kingdom',
        'ca': 'Canada',
        'us': 'United States',
        'gb': 'United Kingdom',
        'de': 'Germany',
        'fr': 'France',
        'es': 'Spain',
        'it': 'Italy',
        'ru': 'Russia',
        'cn': 'China',
        'jp': 'Japan',
        'kr': 'South Korea',
        'br': 'Brazil',
        'mx': 'Mexico',
        'au': 'Australia',
        'nz': 'New Zealand'
    };
    
    return regionNames[region.toLowerCase()] || region.toUpperCase();
}
