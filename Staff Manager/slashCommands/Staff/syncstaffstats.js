const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
const StaffBanActivity = require('../../models/staffBanActivity');
const SharedStaffStats = require('../../models/sharedStaffStats');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('syncstaffstats')
        .setDescription('Manually sync staff statistics and test tracking systems')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('tickets')
                .setDescription('Sync all claimed tickets from the ticket database'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('bans')
                .setDescription('Check recent ban tracking status'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset weekly statistics for all staff')
                .addStringOption(option =>
                    option.setName('confirm')
                        .setDescription('Type "CONFIRM" to reset weekly stats')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Check the status of all tracking systems')),

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'tickets':
                    await handleTicketSync(interaction);
                    break;
                case 'bans':
                    await handleBanStatus(interaction);
                    break;
                case 'reset':
                    await handleResetStats(interaction);
                    break;
                case 'status':
                    await handleSystemStatus(interaction);
                    break;
                default:
                    await interaction.editReply({
                        content: '❌ Unknown subcommand.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('Error in syncstaffstats command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while syncing staff statistics.',
                ephemeral: true
            });
        }
    }
};

async function handleTicketSync(interaction) {
    try {
        const client = interaction.client;
        
        if (!client.ticketClaimTracker) {
            return await interaction.editReply({
                content: '❌ Ticket claim tracker is not initialized.',
                ephemeral: true
            });
        }

        await interaction.editReply({
            content: '🔄 Starting ticket claim sync... This may take a moment.',
            ephemeral: true
        });

        // Run the sync
        await client.ticketClaimTracker.syncAllClaimedTickets();

        const embed = new EmbedBuilder()
            .setTitle('✅ Ticket Sync Complete')
            .setColor('#4CAF50')
            .setDescription('All claimed tickets have been synced with staff statistics.')
            .addFields([
                { name: 'Process', value: 'Ticket Claim Sync', inline: true },
                { name: 'Status', value: 'Completed', inline: true },
                { name: 'Time', value: new Date().toLocaleTimeString(), inline: true }
            ])
            .setTimestamp()
            .setFooter({ text: 'Staff Statistics Sync' });

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error syncing tickets:', error);
        await interaction.editReply({
            content: '❌ An error occurred while syncing ticket claims.',
            ephemeral: true
        });
    }
}

async function handleBanStatus(interaction) {
    try {
        // Get recent ban activity
        const recentBans = await StaffBanActivity.find({
            issuedAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        }).sort({ issuedAt: -1 }).limit(10);

        const embed = new EmbedBuilder()
            .setTitle('🔨 Recent Ban Tracking Status')
            .setColor('#2196F3')
            .setDescription('Status of staff ban tracking system')
            .setTimestamp();

        if (recentBans.length === 0) {
            embed.addFields([{
                name: 'Recent Bans',
                value: 'No bans tracked in the last 7 days',
                inline: false
            }]);
        } else {
            const banList = recentBans.map(ban => 
                `• ${ban.playerName} by <@${ban.staffDiscordId}> (${new Date(ban.issuedAt).toLocaleDateString()})`
            ).join('\n');

            embed.addFields([{
                name: `Recent Bans (${recentBans.length})`,
                value: banList.length > 1024 ? banList.substring(0, 1021) + '...' : banList,
                inline: false
            }]);
        }

        // Check tracking system status
        const client = interaction.client;
        const trackingStatus = client.staffBanTracker?.isTracking ? '✅ Running' : '❌ Not Running';
        
        embed.addFields([
            { name: 'Ban Tracker Status', value: trackingStatus, inline: true },
            { name: 'Total Tracked Bans', value: `${await StaffBanActivity.countDocuments()}`, inline: true },
            { name: 'Last Check', value: client.staffBanTracker?.lastChecked ? new Date(client.staffBanTracker.lastChecked).toLocaleTimeString() : 'Unknown', inline: true }
        ]);

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error getting ban status:', error);
        await interaction.editReply({
            content: '❌ An error occurred while checking ban status.',
            ephemeral: true
        });
    }
}

async function handleResetStats(interaction) {
    const confirmation = interaction.options.getString('confirm');
    
    if (confirmation !== 'CONFIRM') {
        return await interaction.editReply({
            content: '❌ You must type "CONFIRM" to reset weekly statistics.',
            ephemeral: true
        });
    }

    try {
        // Reset weekly stats
        await SharedStaffStats.updateMany(
            {},
            {
                $set: {
                    'tickets.weeklyClaimed': 0,
                    'messages.weekly': 0,
                    'bans.weekly': 0,
                    'voice.weeklyMinutes': 0,
                    'ingame.weeklyMinutes': 0,
                    weeklyResetDate: new Date()
                }
            }
        );

        const embed = new EmbedBuilder()
            .setTitle('✅ Weekly Statistics Reset')
            .setColor('#FF9800')
            .setDescription('All weekly statistics have been reset to zero.')
            .addFields([
                { name: 'Reset Items', value: '• Tickets Claimed\n• Messages Sent\n• Bans Issued\n• Voice Minutes\n• In-Game Minutes', inline: false },
                { name: 'Reset Time', value: new Date().toLocaleString(), inline: true },
                { name: 'Next Auto Reset', value: 'Next Monday at midnight', inline: true }
            ])
            .setTimestamp()
            .setFooter({ text: 'Weekly Statistics Reset' });

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error resetting stats:', error);
        await interaction.editReply({
            content: '❌ An error occurred while resetting weekly statistics.',
            ephemeral: true
        });
    }
}

async function handleSystemStatus(interaction) {
    try {
        const client = interaction.client;

        const embed = new EmbedBuilder()
            .setTitle('📊 Staff Tracking System Status')
            .setColor('#2196F3')
            .setDescription('Status of all staff activity tracking systems')
            .setTimestamp();

        // Check each system status
        const systems = [
            {
                name: 'Ban Monitor (Public)',
                status: client.banMonitor?.isMonitoring ? '✅ Running' : '❌ Not Running',
                lastCheck: client.banMonitor?.lastChecked ? new Date(client.banMonitor.lastChecked).toLocaleTimeString() : 'Unknown'
            },
            {
                name: 'Staff Ban Tracker',
                status: client.staffBanTracker?.isTracking ? '✅ Running' : '❌ Not Running',
                lastCheck: client.staffBanTracker?.lastChecked ? new Date(client.staffBanTracker.lastChecked).toLocaleTimeString() : 'Unknown'
            },
            {
                name: 'Ticket Claim Tracker',
                status: client.ticketClaimTracker?.isTracking ? '✅ Running' : '❌ Not Running',
                lastCheck: client.ticketClaimTracker?.lastChecked ? new Date(client.ticketClaimTracker.lastChecked).toLocaleTimeString() : 'Unknown'
            }
        ];

        for (const system of systems) {
            embed.addFields([
                { name: system.name, value: `Status: ${system.status}\nLast Check: ${system.lastCheck}`, inline: true }
            ]);
        }

        // Add database stats
        const totalBans = await StaffBanActivity.countDocuments();
        const totalStats = await SharedStaffStats.countDocuments();

        embed.addFields([
            { name: 'Database Stats', value: `Tracked Bans: ${totalBans}\nStaff Records: ${totalStats}`, inline: true }
        ]);

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error getting system status:', error);
        await interaction.editReply({
            content: '❌ An error occurred while checking system status.',
            ephemeral: true
        });
    }
}
