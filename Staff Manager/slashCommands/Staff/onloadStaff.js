const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, PermissionFlagsBits } = require('discord.js');
const StaffManager = require('../../utils/staffManager');
const RconClient = require('../../utils/rconClient');
const ConfigPermissionManager = require('../../utils/configPermissionManager');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('onloadstaff')
        .setDescription('Onload a staff member to a server')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The staff member to onload')
                .setRequired(true))
        // Permission level selection is now handled via button interaction
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: false });

        // Get options
        const user = interaction.options.getUser('user');

        // Check if user is a staff member
        const staffMember = await StaffManager.getStaffMember(user.id);

        if (!staffMember) {
            return interaction.editReply(client.locals.Staff.RemoveStaff.NotStaff.replace('{user}', user.tag));
        }

        // This command now redirects to the button-based system for better UX
        return interaction.editReply({
            content: `⚠️ **Command Deprecated**\n\nThe onload staff command has been replaced with an improved button-based system.\n\n**To onload staff:**\n1. Use \`/staffstats @${user.tag}\` to view their statistics\n2. Click the "Onload Staff" button\n3. Select permission level from the dropdown\n4. Choose servers to apply permissions\n\nThis new system provides better permission management and real-time feedback.`,
            ephemeral: true
        });
    }
};
