const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, PermissionFlagsBits } = require('discord.js');
const StaffManager = require('../../utils/staffManager');
const RconClient = require('../../utils/rconClient');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('onloadstaff')
        .setDescription('Onload a staff member to a server')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The staff member to onload')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: false });

        // Get options
        const user = interaction.options.getUser('user');

        // Check if user is a staff member
        const staffMember = await StaffManager.getStaffMember(user.id);

        if (!staffMember) {
            return interaction.editReply(client.locals.Staff.RemoveStaff.NotStaff.replace('{user}', user.tag));
        }

        // Create server selection menu
        const servers = client.config.Servers;

        if (!servers || servers.length === 0) {
            return interaction.editReply('No servers configured');
        }

        const serverOptions = servers.map(server => ({
            label: server.name,
            value: server.name,
            description: `Onload to ${server.name}`
        }));

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`server_select_${interaction.user.id}`)
            .setPlaceholder('Select servers')
            .setMinValues(1)
            .setMaxValues(servers.length)
            .addOptions(serverOptions);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        // Store staff ID for later use
        client.userSelection[interaction.user.id] = {
            action: 'onload',
            staffId: user.id,
            steamId: staffMember.steam_id
        };

        await interaction.editReply({
            content: client.locals.Staff.OnloadStaff.ServerSelection,
            components: [row]
        });

        // Wait for server selection
        const filter = i => i.customId === `server_select_${interaction.user.id}` && i.user.id === interaction.user.id;

        try {
            const serverSelection = await interaction.channel.awaitMessageComponent({ filter, time: 60000 });

            // Process server selection
            const selectedServers = serverSelection.values;

            // Create embed for results
            const embed = new EmbedBuilder()
                .setTitle(`Onloading Staff Member`)
                .setDescription(`Onloading <@${user.id}> to selected servers...`)
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setTimestamp();

            await serverSelection.update({
                content: '',
                embeds: [embed],
                components: []
            });

            // Process each selected server
            for (const serverName of selectedServers) {
                const server = servers.find(s => s.name === serverName);

                if (!server) {
                    embed.addFields({
                        name: `❌ ${serverName}`,
                        value: 'Server not found in configuration',
                        inline: false
                    });
                    continue;
                }

                // Run onloading commands
                const onloadingCommands = client.config.StaffManagement.OnloadingCommands;

                if (!onloadingCommands || onloadingCommands.length === 0) {
                    embed.addFields({
                        name: `❌ ${serverName}`,
                        value: 'No onloading commands configured',
                        inline: false
                    });
                    continue;
                }

                let serverField = `**Server:** ${serverName}\n`;
                let failed = false;

                for (const cmdInfo of onloadingCommands) {
                    const commandToRun = cmdInfo.cmd.replace('{steam_id}', staffMember.steam_id);

                    try {
                        // Get RCON connection from client
                        const rcon = client.rconConnections[server.name];

                        if (!rcon || !rcon.ws || rcon.ws.readyState !== 1) {
                            throw new Error('No active connection to server');
                        }

                        // Send command to server
                        const identifier = `onload_${Math.floor(Math.random() * 900000) + 100000}`;
                        const response = await rcon.runCommand(
                            { ip: rcon.ip, rcon_port: rcon.port, rcon_password: rcon.password, name: rcon.nickname },
                            commandToRun,
                            identifier,
                            3 // retries
                        );

                        // Parse response
                        const responseData = JSON.parse(response);
                        const responseMessage = responseData.Message || 'Success, but server returned no message.';

                        serverField += `**${cmdInfo.title}:**\n\`\`\`${responseMessage}\`\`\`\n`;
                    } catch (error) {
                        failed = true;
                        serverField += `**${cmdInfo.title}:**\n\`\`\`Failed: ${error.message}\`\`\`\n`;
                    }

                    // Update embed
                    embed.spliceFields(
                        embed.data.fields?.findIndex(f => f.name.includes(serverName)) ?? -1,
                        1,
                        {
                            name: `${failed ? '❌' : '✅'} ${serverName}`,
                            value: serverField,
                            inline: false
                        }
                    );

                    await serverSelection.editReply({ embeds: [embed] });
                }
            }

            // Final update
            embed.setDescription(`Onloading completed for <@${user.id}>`);
            await serverSelection.editReply({ embeds: [embed] });
        } catch (error) {
            if (error.code === 'INTERACTION_COLLECTOR_ERROR') {
                return interaction.followUp({
                    content: 'Server selection timed out',
                    ephemeral: true
                });
            }

            console.error('Error handling staff onload:', error);
            return interaction.followUp({
                content: `Error: ${error.message}`,
                ephemeral: true
            });
        }
    }
};
