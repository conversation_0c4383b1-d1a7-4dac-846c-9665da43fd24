const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON><PERSON>er, PermissionFlagsBits } = require('discord.js');
const StaffStatsMessage = require('../../models/staffStatsMessage');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('staffstatsadmin')
        .setDescription('Admin commands for staff stats message management')
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('View statistics about staff stats messages'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('cleanup')
                .setDescription('Clean up old staff stats message records')
                .addIntegerOption(option =>
                    option.setName('days')
                        .setDescription('Remove records older than this many days (default: 7)')
                        .setRequired(false)
                        .setMinValue(1)
                        .setMaxValue(30)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all staff stats messages in this channel'))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages),

    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });

        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'stats':
                return await this.handleStats(interaction, client);
            case 'cleanup':
                return await this.handleCleanup(interaction, client);
            case 'list':
                return await this.handleList(interaction, client);
            default:
                return interaction.editReply('❌ Unknown subcommand.');
        }
    },

    /**
     * Handle stats subcommand
     */
    async handleStats(interaction, client) {
        try {
            const stats = await client.staffStatsMessageManager.getStats();
            
            const embed = new EmbedBuilder()
                .setTitle('📊 Staff Stats Message Statistics')
                .setColor('#4CAF50')
                .addFields([
                    { name: '📝 Total Messages', value: `${stats.totalMessages}`, inline: true },
                    { name: '🕐 Recent (24h)', value: `${stats.recentMessages}`, inline: true },
                    { name: '🗑️ Old Messages', value: `${stats.oldMessages}`, inline: true }
                ])
                .setTimestamp()
                .setFooter({ text: 'Staff Manager • Message Tracking System' });

            // Add cleanup info
            embed.addFields([{
                name: '🧹 Automatic Cleanup',
                value: 'Old message records (7+ days) are automatically cleaned up every 6 hours',
                inline: false
            }]);

            return interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error getting stats:', error);
            return interaction.editReply('❌ An error occurred while getting statistics.');
        }
    },

    /**
     * Handle cleanup subcommand
     */
    async handleCleanup(interaction, client) {
        try {
            const days = interaction.options.getInteger('days') || 7;
            
            // Perform cleanup
            const result = await StaffStatsMessage.cleanupOldMessages(days);
            
            const embed = new EmbedBuilder()
                .setTitle('🧹 Manual Cleanup Complete')
                .setColor('#4CAF50')
                .addFields([
                    { name: '🗑️ Records Removed', value: `${result.deletedCount}`, inline: true },
                    { name: '📅 Older Than', value: `${days} days`, inline: true },
                    { name: '⏰ Completed At', value: new Date().toLocaleTimeString(), inline: true }
                ])
                .setTimestamp()
                .setFooter({ text: 'Manual cleanup completed successfully' });

            if (result.deletedCount === 0) {
                embed.setDescription('No old records found to clean up.');
            } else {
                embed.setDescription(`Successfully removed ${result.deletedCount} old message records.`);
            }

            return interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error during manual cleanup:', error);
            return interaction.editReply('❌ An error occurred during cleanup.');
        }
    },

    /**
     * Handle list subcommand
     */
    async handleList(interaction, client) {
        try {
            const messages = await StaffStatsMessage.getChannelStatsMessages(interaction.channel.id);
            
            if (messages.length === 0) {
                return interaction.editReply('ℹ️ No staff stats messages found in this channel.');
            }

            const embed = new EmbedBuilder()
                .setTitle(`📋 Staff Stats Messages in #${interaction.channel.name}`)
                .setColor('#2196F3')
                .setTimestamp()
                .setFooter({ text: `Found ${messages.length} message(s)` });

            // Group messages by staff member
            const staffGroups = {};
            for (const msg of messages) {
                if (!staffGroups[msg.staffDiscordId]) {
                    staffGroups[msg.staffDiscordId] = [];
                }
                staffGroups[msg.staffDiscordId].push(msg);
            }

            // Add fields for each staff member
            let fieldCount = 0;
            for (const [staffId, staffMessages] of Object.entries(staffGroups)) {
                if (fieldCount >= 25) break; // Discord embed field limit

                const latestMessage = staffMessages[0]; // Already sorted by lastUpdated desc
                const messageLink = `https://discord.com/channels/${interaction.guild.id}/${interaction.channel.id}/${latestMessage.messageId}`;
                
                embed.addFields([{
                    name: `<@${staffId}>`,
                    value: `**Message ID:** \`${latestMessage.messageId}\`\n**Last Updated:** ${latestMessage.lastUpdated.toLocaleString()}\n**Requested By:** <@${latestMessage.requestedBy}>\n[Jump to Message](${messageLink})`,
                    inline: true
                }]);
                
                fieldCount++;
            }

            if (Object.keys(staffGroups).length > 25) {
                embed.addFields([{
                    name: '⚠️ Note',
                    value: `Only showing first 25 staff members. Total: ${Object.keys(staffGroups).length}`,
                    inline: false
                }]);
            }

            return interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error listing messages:', error);
            return interaction.editReply('❌ An error occurred while listing messages.');
        }
    }
};
