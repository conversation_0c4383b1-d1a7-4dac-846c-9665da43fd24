const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON><PERSON>er, PermissionFlagsBits } = require('discord.js');
const StaffManager = require('../../utils/staffManager');
const BattleMetricsClient = require('../../utils/battlemetricsClient');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('addstaff')
        .setDescription('Add a staff member to the database')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to add as staff')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('steamid')
                .setDescription('The Steam ID of the staff member')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });

        // Get options
        const user = interaction.options.getUser('user');
        const steamId = interaction.options.getString('steamid');

        // Validate Steam ID format
        if (!steamId.match(/^[0-9]{17}$/)) {
            return interaction.editReply(client.locals.General.InvalidSteamID || 'Invalid Steam ID format. Please provide a valid Steam ID.');
        }

        // Try to find BattleMetrics ID and region
        let bmId = null;
        let regionInfo = { region: 'unknown', flag: null };
        try {
            const response = await BattleMetricsClient.searchPlayerBySteamId(steamId);

            if (response.status === 200 && response.dict.data && response.dict.data.length > 0) {
                bmId = response.dict.data[0].id;
                
                // Fetch region information using getPlayerRegionInfo
                const regionResponse = await BattleMetricsClient.getPlayerRegionInfo(bmId);
                if (regionResponse.status === 200 && regionResponse.regionInfo) {
                    regionInfo = regionResponse.regionInfo;
                } else {
                    console.warn(`Failed to get region info for BM ID ${bmId}: ${regionResponse.message}`);
                }
            } else {
                console.warn(`No BattleMetrics player found for Steam ID ${steamId}`);
            }
        } catch (error) {
            console.error('Error searching for BattleMetrics data:', error);
        }

        // Create staff member
        const staffData = {
            _id: user.id,
            discord_name: user.tag,
            steam_id: steamId,
            bm_id: bmId,
            region_info: {
                region: regionInfo.region,
                flag: regionInfo.flag,
                timezone: 'UTC'
            },
            active: true
        };

        const { success, message, data } = await StaffManager.addStaffMember(staffData);

        if (!success) {
            return interaction.editReply(client.locals.Staff.AddStaff.Error.replace('{user}', user.tag).replace('{error}', message));
        }

        // Create success embed
        const embed = new EmbedBuilder()
            .setTitle('Staff Member Added')
            .setDescription(client.locals.Staff.AddStaff.Success.replace('{user}', user.tag))
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
            .addFields(
                { name: 'Discord', value: `<@${user.id}>`, inline: true },
                { name: 'Steam ID', value: steamId, inline: true },
                { name: 'Region', value: regionInfo.region !== 'unknown' ? regionInfo.region.toUpperCase() : 'Unknown', inline: true }
            )
            .setTimestamp();

        if (bmId) {
            embed.addFields({ name: 'BattleMetrics ID', value: bmId, inline: true });
        }

        if (regionInfo.flag) {
            embed.addFields({ name: 'Country', value: regionInfo.flag, inline: true });
        }

        return interaction.editReply({ embeds: [embed] });
    }
};