const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('statsembed')
        .setDescription('Manage the staff statistics embed')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels)
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Setup the stats embed in a channel')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('Channel to display the stats embed')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('update')
                .setDescription('Force update the stats embed'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('start')
                .setDescription('Start automatic stats embed updates'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('stop')
                .setDescription('Stop automatic stats embed updates'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Check stats embed updater status')),

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'setup':
                    await handleSetup(interaction);
                    break;
                case 'update':
                    await handleUpdate(interaction);
                    break;
                case 'start':
                    await handleStart(interaction);
                    break;
                case 'stop':
                    await handleStop(interaction);
                    break;
                case 'status':
                    await handleStatus(interaction);
                    break;
                default:
                    await interaction.editReply({
                        content: '❌ Unknown subcommand.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('Error in statsembed command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while managing the stats embed.',
                ephemeral: true
            });
        }
    }
};

async function handleSetup(interaction) {
    const channel = interaction.options.getChannel('channel');

    if (!channel.isTextBased()) {
        return await interaction.editReply({
            content: '❌ Please select a text channel.',
            ephemeral: true
        });
    }

    // Check if bot has permissions in the channel
    const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
    const permissions = channel.permissionsFor(botMember);

    if (!permissions.has(['SendMessages', 'EmbedLinks', 'ViewChannel'])) {
        return await interaction.editReply({
            content: '❌ I need Send Messages, Embed Links, and View Channel permissions in that channel.',
            ephemeral: true
        });
    }

    // Update config and initialize stats embed updater
    if (!interaction.client.statsEmbedUpdater) {
        const StatsEmbedUpdater = require('../../utils/statsEmbedUpdater');
        interaction.client.statsEmbedUpdater = new StatsEmbedUpdater(interaction.client);
    }

    // Set the stats channel
    interaction.client.statsEmbedUpdater.setStatsChannel(channel.id);

    // Update config (this would need to be persisted to config file in a real implementation)
    if (!interaction.client.config.StaffManagement) {
        interaction.client.config.StaffManagement = {};
    }
    if (!interaction.client.config.StaffManagement.StatsEmbed) {
        interaction.client.config.StaffManagement.StatsEmbed = {};
    }
    interaction.client.config.StaffManagement.StatsEmbed.ChannelID = channel.id;

    // Initialize and force first update
    await interaction.client.statsEmbedUpdater.initialize();

    const embed = new EmbedBuilder()
        .setTitle('✅ Stats Embed Setup Complete')
        .setColor('#4CAF50')
        .addFields([
            { name: 'Channel', value: `<#${channel.id}>`, inline: true },
            { name: 'Update Frequency', value: '5 minutes', inline: true },
            { name: 'Status', value: '✅ Active', inline: true },
            { name: 'Features', value: '• Team overview statistics\n• Top weekly performers\n• Recent activity tracking\n• System status monitoring', inline: false }
        ])
        .setTimestamp()
        .setFooter({ text: 'Stats embed will update automatically every 5 minutes' });

    await interaction.editReply({ embeds: [embed] });
}

async function handleUpdate(interaction) {
    if (!interaction.client.statsEmbedUpdater) {
        return await interaction.editReply({
            content: '❌ Stats embed updater is not initialized. Use `/statsembed setup` first.',
            ephemeral: true
        });
    }

    await interaction.editReply({
        content: '🔄 Updating stats embed...',
        ephemeral: true
    });

    try {
        await interaction.client.statsEmbedUpdater.forceUpdate();

        const embed = new EmbedBuilder()
            .setTitle('✅ Stats Embed Updated')
            .setColor('#4CAF50')
            .addFields([
                { name: 'Status', value: 'Successfully updated', inline: true },
                { name: 'Time', value: new Date().toLocaleTimeString(), inline: true }
            ])
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('Error updating stats embed:', error);
        await interaction.editReply({
            content: '❌ Failed to update stats embed. Check logs for details.',
            ephemeral: true
        });
    }
}

async function handleStart(interaction) {
    if (!interaction.client.statsEmbedUpdater) {
        return await interaction.editReply({
            content: '❌ Stats embed updater is not initialized. Use `/statsembed setup` first.',
            ephemeral: true
        });
    }

    if (interaction.client.statsEmbedUpdater.isUpdating) {
        return await interaction.editReply({
            content: '❌ Stats embed updater is already running.',
            ephemeral: true
        });
    }

    interaction.client.statsEmbedUpdater.startUpdating();

    const embed = new EmbedBuilder()
        .setTitle('✅ Stats Embed Updater Started')
        .setColor('#4CAF50')
        .addFields([
            { name: 'Status', value: '🟢 Running', inline: true },
            { name: 'Update Frequency', value: '5 minutes', inline: true },
            { name: 'Started At', value: new Date().toLocaleTimeString(), inline: true }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed] });
}

async function handleStop(interaction) {
    if (!interaction.client.statsEmbedUpdater) {
        return await interaction.editReply({
            content: '❌ Stats embed updater is not initialized.',
            ephemeral: true
        });
    }

    if (!interaction.client.statsEmbedUpdater.isUpdating) {
        return await interaction.editReply({
            content: '❌ Stats embed updater is not currently running.',
            ephemeral: true
        });
    }

    interaction.client.statsEmbedUpdater.stopUpdating();

    const embed = new EmbedBuilder()
        .setTitle('⏹️ Stats Embed Updater Stopped')
        .setColor('#FF9800')
        .addFields([
            { name: 'Status', value: '🔴 Stopped', inline: true },
            { name: 'Stopped At', value: new Date().toLocaleTimeString(), inline: true }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed] });
}

async function handleStatus(interaction) {
    const embed = new EmbedBuilder()
        .setTitle('📊 Stats Embed Status')
        .setColor('#2196F3')
        .setTimestamp();

    if (!interaction.client.statsEmbedUpdater) {
        embed.addFields([
            { name: 'Status', value: '❌ Not Initialized', inline: true },
            { name: 'Action Required', value: 'Use `/statsembed setup` to initialize', inline: false }
        ]);
    } else {
        const updater = interaction.client.statsEmbedUpdater;
        const channelId = updater.statsChannelId;
        const isRunning = updater.isUpdating;

        embed.addFields([
            { name: 'Status', value: isRunning ? '🟢 Running' : '🔴 Stopped', inline: true },
            { name: 'Channel', value: channelId ? `<#${channelId}>` : 'Not set', inline: true },
            { name: 'Update Frequency', value: '5 minutes', inline: true },
            { name: 'Message ID', value: updater.statsMessageId || 'Not created yet', inline: true }
        ]);

        // Add system status
        const systemStatus = [
            `📊 Stats Updater: ${isRunning ? '✅ Running' : '❌ Stopped'}`,
            `🔨 Ban Tracker: ${interaction.client.staffBanTracker?.isTracking ? '✅ Running' : '❌ Offline'}`,
            `🎫 Ticket Tracker: ${interaction.client.ticketClaimTracker?.isTracking ? '✅ Running' : '❌ Offline'}`
        ].join('\n');

        embed.addFields([{
            name: 'Related Systems',
            value: systemStatus,
            inline: false
        }]);

        if (isRunning) {
            embed.addFields([{
                name: 'Controls',
                value: '• Use `/statsembed update` to force update\n• Use `/statsembed stop` to stop automatic updates',
                inline: false
            }]);
        } else {
            embed.addFields([{
                name: 'Controls',
                value: '• Use `/statsembed start` to begin automatic updates\n• Use `/statsembed update` to manually update once',
                inline: false
            }]);
        }
    }

    await interaction.editReply({ embeds: [embed] });
}
