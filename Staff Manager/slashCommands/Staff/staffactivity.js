const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const SharedStaffStats = require('../../models/sharedStaffStats');
const StaffBanActivity = require('../../models/staffBanActivity');
const StaffMember = require('../../models/staffMember');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('staffactivity')
        .setDescription('View comprehensive staff activity statistics')
        .addUserOption(option =>
            option.setName('staff')
                .setDescription('Specific staff member to view (leave empty for all staff)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('timeframe')
                .setDescription('Time period to view')
                .setRequired(false)
                .addChoices(
                    { name: 'This Week', value: 'week' },
                    { name: 'This Month', value: 'month' },
                    { name: 'All Time', value: 'all' }
                )),

    async execute(interaction) {
        await interaction.deferReply();

        try {
            const targetUser = interaction.options.getUser('staff');
            const timeframe = interaction.options.getString('timeframe') || 'week';

            if (targetUser) {
                // Show specific staff member stats
                await showIndividualStats(interaction, targetUser, timeframe);
            } else {
                // Show all staff stats
                await showAllStaffStats(interaction, timeframe);
            }

        } catch (error) {
            console.error('Error in staffactivity command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while fetching staff activity data.',
                ephemeral: true
            });
        }
    }
};

async function showIndividualStats(interaction, user, timeframe) {
    try {
        // Check if user is a staff member
        const staffMember = await StaffMember.findById(user.id);
        if (!staffMember) {
            return await interaction.editReply({
                content: '❌ The specified user is not a registered staff member.',
                ephemeral: true
            });
        }

        // Get shared stats
        const sharedStats = await SharedStaffStats.findOne({ staffDiscordId: user.id });
        
        // Get ban activity
        const banQuery = { staffDiscordId: user.id };
        if (timeframe === 'week') {
            const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            banQuery.issuedAt = { $gte: weekAgo };
        } else if (timeframe === 'month') {
            const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            banQuery.issuedAt = { $gte: monthAgo };
        }
        
        const banActivity = await StaffBanActivity.find(banQuery).sort({ issuedAt: -1 }).limit(5);

        // Build stats based on timeframe
        let stats = {
            tickets: 0,
            messages: 0,
            bans: 0,
            voice: 0,
            ingame: 0
        };

        if (sharedStats) {
            switch (timeframe) {
                case 'week':
                    stats.tickets = sharedStats.tickets?.weeklyClaimed || 0;
                    stats.messages = sharedStats.messages?.weekly || 0;
                    stats.bans = sharedStats.bans?.weekly || 0;
                    stats.voice = sharedStats.voice?.weeklyMinutes || 0;
                    stats.ingame = sharedStats.ingame?.weeklyMinutes || 0;
                    break;
                case 'month':
                    stats.tickets = sharedStats.tickets?.monthlyClaimed || 0;
                    stats.messages = sharedStats.messages?.monthly || 0;
                    stats.bans = sharedStats.bans?.monthly || 0;
                    stats.voice = sharedStats.voice?.monthlyMinutes || 0;
                    stats.ingame = sharedStats.ingame?.monthlyMinutes || 0;
                    break;
                case 'all':
                    stats.tickets = sharedStats.tickets?.totalClaimed || 0;
                    stats.messages = sharedStats.messages?.total || 0;
                    stats.bans = sharedStats.bans?.total || 0;
                    stats.voice = sharedStats.voice?.totalMinutes || 0;
                    stats.ingame = sharedStats.ingame?.totalMinutes || 0;
                    break;
            }
        }

        const embed = new EmbedBuilder()
            .setTitle(`📊 Staff Activity - ${user.displayName}`)
            .setColor('#4CAF50')
            .setThumbnail(user.displayAvatarURL())
            .addFields([
                { 
                    name: '🎫 Tickets Claimed', 
                    value: `${stats.tickets}`, 
                    inline: true 
                },
                { 
                    name: '💬 Messages Sent', 
                    value: `${stats.messages}`, 
                    inline: true 
                },
                { 
                    name: '🔨 Bans Issued', 
                    value: `${stats.bans}`, 
                    inline: true 
                },
                { 
                    name: '🎤 Voice Time', 
                    value: `${Math.round(stats.voice / 60)} hours`, 
                    inline: true 
                },
                { 
                    name: '🎮 In-Game Time', 
                    value: `${Math.round(stats.ingame / 60)} hours`, 
                    inline: true 
                },
                { 
                    name: '⏱️ Total Activity', 
                    value: `${Math.round((stats.voice + stats.ingame) / 60)} hours`, 
                    inline: true 
                }
            ])
            .setFooter({ 
                text: `Timeframe: ${timeframe.charAt(0).toUpperCase() + timeframe.slice(1)} | Last updated: ${sharedStats?.lastUpdated ? new Date(sharedStats.lastUpdated).toLocaleDateString() : 'Never'}` 
            })
            .setTimestamp();

        // Add recent ban activity if any
        if (banActivity.length > 0) {
            const recentBans = banActivity.slice(0, 3).map(ban => 
                `• ${ban.playerName} - ${ban.reason.substring(0, 50)}${ban.reason.length > 50 ? '...' : ''}`
            ).join('\n');
            
            embed.addFields([{
                name: '🔨 Recent Bans',
                value: recentBans || 'No recent bans',
                inline: false
            }]);
        }

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error showing individual stats:', error);
        await interaction.editReply({
            content: '❌ An error occurred while fetching individual staff stats.',
            ephemeral: true
        });
    }
}

async function showAllStaffStats(interaction, timeframe) {
    try {
        // Get all staff members
        const staffMembers = await StaffMember.find({ active: true });
        
        if (staffMembers.length === 0) {
            return await interaction.editReply({
                content: '❌ No active staff members found.',
                ephemeral: true
            });
        }

        // Get stats for all staff members
        const allStats = await Promise.all(
            staffMembers.map(async (staff) => {
                const sharedStats = await SharedStaffStats.findOne({ staffDiscordId: staff._id });
                
                let stats = {
                    id: staff._id,
                    name: staff.discord_name,
                    tickets: 0,
                    messages: 0,
                    bans: 0,
                    voice: 0,
                    ingame: 0
                };

                if (sharedStats) {
                    switch (timeframe) {
                        case 'week':
                            stats.tickets = sharedStats.tickets?.weeklyClaimed || 0;
                            stats.messages = sharedStats.messages?.weekly || 0;
                            stats.bans = sharedStats.bans?.weekly || 0;
                            stats.voice = sharedStats.voice?.weeklyMinutes || 0;
                            stats.ingame = sharedStats.ingame?.weeklyMinutes || 0;
                            break;
                        case 'month':
                            stats.tickets = sharedStats.tickets?.monthlyClaimed || 0;
                            stats.messages = sharedStats.messages?.monthly || 0;
                            stats.bans = sharedStats.bans?.monthly || 0;
                            stats.voice = sharedStats.voice?.monthlyMinutes || 0;
                            stats.ingame = sharedStats.ingame?.monthlyMinutes || 0;
                            break;
                        case 'all':
                            stats.tickets = sharedStats.tickets?.totalClaimed || 0;
                            stats.messages = sharedStats.messages?.total || 0;
                            stats.bans = sharedStats.bans?.total || 0;
                            stats.voice = sharedStats.voice?.totalMinutes || 0;
                            stats.ingame = sharedStats.ingame?.totalMinutes || 0;
                            break;
                    }
                }

                return stats;
            })
        );

        // Sort by total activity (tickets + messages + bans)
        allStats.sort((a, b) => {
            const aTotal = a.tickets + a.messages + a.bans;
            const bTotal = b.tickets + b.messages + b.bans;
            return bTotal - aTotal;
        });

        const embed = new EmbedBuilder()
            .setTitle(`📊 Staff Activity Overview - ${timeframe.charAt(0).toUpperCase() + timeframe.slice(1)}`)
            .setColor('#2196F3')
            .setDescription('Staff activity statistics across all team members')
            .setTimestamp();

        // Add top performers
        const topPerformers = allStats.slice(0, 10).map((staff, index) => {
            const totalHours = Math.round((staff.voice + staff.ingame) / 60);
            return `${index + 1}. <@${staff.id}> - 🎫${staff.tickets} 💬${staff.messages} 🔨${staff.bans} ⏱️${totalHours}h`;
        }).join('\n');

        embed.addFields([{
            name: '🏆 Top Staff Members',
            value: topPerformers || 'No activity data available',
            inline: false
        }]);

        // Add summary statistics
        const totalStats = allStats.reduce((acc, staff) => ({
            tickets: acc.tickets + staff.tickets,
            messages: acc.messages + staff.messages,
            bans: acc.bans + staff.bans,
            voice: acc.voice + staff.voice,
            ingame: acc.ingame + staff.ingame
        }), { tickets: 0, messages: 0, bans: 0, voice: 0, ingame: 0 });

        embed.addFields([
            { name: '📈 Total Tickets', value: `${totalStats.tickets}`, inline: true },
            { name: '📈 Total Messages', value: `${totalStats.messages}`, inline: true },
            { name: '📈 Total Bans', value: `${totalStats.bans}`, inline: true },
            { name: '📈 Total Voice Time', value: `${Math.round(totalStats.voice / 60)} hours`, inline: true },
            { name: '📈 Total In-Game Time', value: `${Math.round(totalStats.ingame / 60)} hours`, inline: true },
            { name: '📈 Combined Activity', value: `${Math.round((totalStats.voice + totalStats.ingame) / 60)} hours`, inline: true }
        ]);

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error showing all staff stats:', error);
        await interaction.editReply({
            content: '❌ An error occurred while fetching staff statistics.',
            ephemeral: true
        });
    }
}
