const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, PermissionFlagsBits } = require('discord.js');
const StaffManager = require('../../utils/staffManager');
const RconClient = require('../../utils/rconClient');
const ConfigPermissionManager = require('../../utils/configPermissionManager');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('offloadstaff')
        .setDescription('Offload a staff member from a server')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The staff member to offload')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles),

    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: false });

        // Get options
        const user = interaction.options.getUser('user');

        // Check if user is a staff member
        const staffMember = await StaffManager.getStaffMember(user.id);

        if (!staffMember) {
            return interaction.editReply(client.locals.Staff.RemoveStaff.NotStaff.replace('{user}', user.tag));
        }

        // This command now redirects to the button-based system for better UX
        return interaction.editReply({
            content: `⚠️ **Command Deprecated**\n\nThe offload staff command has been replaced with an improved button-based system.\n\n**To offload staff:**\n1. Use \`/staffstats @${user.tag}\` to view their statistics\n2. Click the "Offload Staff" button\n3. Select servers to remove permissions from\n4. System will execute comprehensive permission cleanup\n\nThis new system provides better permission management and real-time feedback.`,
            ephemeral: true
        });
    }
};
