const { <PERSON><PERSON><PERSON><PERSON>mandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const StaffManager = require('../../utils/staffManager');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('removestaff')
        .setDescription('Remove a staff member from the database')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to remove from staff')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });

        // Get options
        const user = interaction.options.getUser('user');

        // Check if user is a staff member
        const staffMember = await StaffManager.getStaffMember(user.id);

        if (!staffMember) {
            return interaction.editReply(client.locals.Staff.RemoveStaff.NotStaff.replace('{user}', user.tag));
        }

        // Remove staff member
        const { success, message } = await StaffManager.removeStaffMember(user.id);

        if (!success) {
            return interaction.editReply(client.locals.Staff.RemoveStaff.Error.replace('{user}', user.tag).replace('{error}', message));
        }

        // Create success embed
        const embed = new EmbedBuilder()
            .setTitle('Staff Member Removed')
            .setDescription(client.locals.Staff.RemoveStaff.Success.replace('{user}', user.tag))
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
            .addFields(
                { name: 'Discord', value: `<@${user.id}>`, inline: true },
                { name: 'Steam ID', value: staffMember.steam_id || 'Unknown', inline: true }
            )
            .setTimestamp();

        return interaction.editReply({ embeds: [embed] });
    }
};
