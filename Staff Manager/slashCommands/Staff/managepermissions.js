const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON>uild<PERSON>, PermissionFlagsBits } = require('discord.js');
const PermissionLevel = require('../../models/permissionLevel');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('managepermissions')
        .setDescription('Manage permission levels and configurations')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a new permission level')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Internal name for the permission level (lowercase, no spaces)')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('displayname')
                        .setDescription('Display name for the permission level')
                        .setRequired(true))
                .addIntegerOption(option =>
                    option.setName('priority')
                        .setDescription('Priority level (higher = more permissions)')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('description')
                        .setDescription('Description of this permission level')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all permission levels'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Delete a permission level')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('Name of the permission level to delete')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('addrole')
                .setDescription('Add a Discord role to a permission level')
                .addStringOption(option =>
                    option.setName('level')
                        .setDescription('Permission level name')
                        .setRequired(true))
                .addRoleOption(option =>
                    option.setName('role')
                        .setDescription('Discord role to add')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('addcommand')
                .setDescription('Add a server command to a permission level')
                .addStringOption(option =>
                    option.setName('level')
                        .setDescription('Permission level name')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('title')
                        .setDescription('Command title/description')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('command')
                        .setDescription('RCON command (use {steam_id} placeholder)')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Setup default permission levels')),

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'create':
                    await handleCreate(interaction);
                    break;
                case 'list':
                    await handleList(interaction);
                    break;
                case 'delete':
                    await handleDelete(interaction);
                    break;
                case 'addrole':
                    await handleAddRole(interaction);
                    break;
                case 'addcommand':
                    await handleAddCommand(interaction);
                    break;
                case 'setup':
                    await handleSetup(interaction);
                    break;
                default:
                    await interaction.editReply({
                        content: '❌ Unknown subcommand.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('Error in managepermissions command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while managing permissions.',
                ephemeral: true
            });
        }
    }
};

async function handleCreate(interaction) {
    const name = interaction.options.getString('name').toLowerCase().replace(/\s+/g, '');
    const displayName = interaction.options.getString('displayname');
    const priority = interaction.options.getInteger('priority');
    const description = interaction.options.getString('description') || '';

    // Check if permission level already exists
    const existing = await PermissionLevel.findOne({ name });
    if (existing) {
        return await interaction.editReply({
            content: '❌ A permission level with this name already exists.',
            ephemeral: true
        });
    }

    // Create new permission level
    const permissionLevel = new PermissionLevel({
        name,
        displayName,
        priority,
        description,
        discordRoles: [],
        serverCommands: [],
        offloadCommands: [],
        permissions: {
            canManageStaff: priority >= 100,
            canViewAllStats: priority >= 50,
            canManageServers: priority >= 80,
            canIssueBans: priority >= 60,
            canManageTickets: priority >= 30,
            canAccessAdminCommands: priority >= 90
        }
    });

    await permissionLevel.save();

    const embed = new EmbedBuilder()
        .setTitle('✅ Permission Level Created')
        .setColor('#4CAF50')
        .addFields([
            { name: 'Name', value: name, inline: true },
            { name: 'Display Name', value: displayName, inline: true },
            { name: 'Priority', value: `${priority}`, inline: true },
            { name: 'Description', value: description || 'No description', inline: false }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed] });
}

async function handleList(interaction) {
    const permissionLevels = await PermissionLevel.find({}).sort({ priority: -1 });

    if (permissionLevels.length === 0) {
        return await interaction.editReply({
            content: '❌ No permission levels configured. Use `/managepermissions setup` to create default levels.',
            ephemeral: true
        });
    }

    const embed = new EmbedBuilder()
        .setTitle('📋 Permission Levels')
        .setColor('#2196F3')
        .setDescription('All configured permission levels')
        .setTimestamp();

    for (const level of permissionLevels) {
        const roleCount = level.discordRoles ? level.discordRoles.length : 0;
        const commandCount = level.serverCommands ? level.serverCommands.length : 0;
        
        embed.addFields([{
            name: `${level.displayName} (${level.name})`,
            value: `**Priority:** ${level.priority}\n**Status:** ${level.active ? '✅ Active' : '❌ Inactive'}\n**Discord Roles:** ${roleCount}\n**Server Commands:** ${commandCount}\n**Description:** ${level.description || 'No description'}`,
            inline: true
        }]);
    }

    await interaction.editReply({ embeds: [embed] });
}

async function handleDelete(interaction) {
    const name = interaction.options.getString('name').toLowerCase();
    
    const permissionLevel = await PermissionLevel.findOne({ name });
    if (!permissionLevel) {
        return await interaction.editReply({
            content: '❌ Permission level not found.',
            ephemeral: true
        });
    }

    await PermissionLevel.deleteOne({ name });

    const embed = new EmbedBuilder()
        .setTitle('✅ Permission Level Deleted')
        .setColor('#FF9800')
        .addFields([
            { name: 'Deleted Level', value: permissionLevel.displayName, inline: true },
            { name: 'Priority', value: `${permissionLevel.priority}`, inline: true }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed] });
}

async function handleAddRole(interaction) {
    const levelName = interaction.options.getString('level').toLowerCase();
    const role = interaction.options.getRole('role');

    const permissionLevel = await PermissionLevel.findOne({ name: levelName });
    if (!permissionLevel) {
        return await interaction.editReply({
            content: '❌ Permission level not found.',
            ephemeral: true
        });
    }

    // Add role if not already present
    if (!permissionLevel.discordRoles.includes(role.id)) {
        permissionLevel.discordRoles.push(role.id);
        await permissionLevel.save();
    }

    const embed = new EmbedBuilder()
        .setTitle('✅ Discord Role Added')
        .setColor('#4CAF50')
        .addFields([
            { name: 'Permission Level', value: permissionLevel.displayName, inline: true },
            { name: 'Added Role', value: `<@&${role.id}>`, inline: true }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed] });
}

async function handleAddCommand(interaction) {
    const levelName = interaction.options.getString('level').toLowerCase();
    const title = interaction.options.getString('title');
    const command = interaction.options.getString('command');

    const permissionLevel = await PermissionLevel.findOne({ name: levelName });
    if (!permissionLevel) {
        return await interaction.editReply({
            content: '❌ Permission level not found.',
            ephemeral: true
        });
    }

    // Add command
    permissionLevel.serverCommands.push({
        title,
        command,
        description: `Command: ${command}`
    });
    await permissionLevel.save();

    const embed = new EmbedBuilder()
        .setTitle('✅ Server Command Added')
        .setColor('#4CAF50')
        .addFields([
            { name: 'Permission Level', value: permissionLevel.displayName, inline: true },
            { name: 'Command Title', value: title, inline: true },
            { name: 'Command', value: `\`${command}\``, inline: false }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed] });
}

async function handleSetup(interaction) {
    // Create default permission levels
    const defaultLevels = [
        {
            name: 'admin',
            displayName: 'Administrator',
            priority: 100,
            description: 'Full administrative access with all permissions',
            color: '#FF0000'
        },
        {
            name: 'moderator',
            displayName: 'Moderator',
            priority: 70,
            description: 'Moderation permissions including bans and ticket management',
            color: '#FF9800'
        },
        {
            name: 'helper',
            displayName: 'Helper',
            priority: 30,
            description: 'Basic staff permissions for ticket support',
            color: '#4CAF50'
        },
        {
            name: 'trial',
            displayName: 'Trial Staff',
            priority: 10,
            description: 'Limited permissions for trial staff members',
            color: '#2196F3'
        }
    ];

    let created = 0;
    let skipped = 0;

    for (const levelData of defaultLevels) {
        const existing = await PermissionLevel.findOne({ name: levelData.name });
        if (existing) {
            skipped++;
            continue;
        }

        const permissionLevel = new PermissionLevel({
            ...levelData,
            discordRoles: [],
            serverCommands: [],
            offloadCommands: [],
            permissions: {
                canManageStaff: levelData.priority >= 100,
                canViewAllStats: levelData.priority >= 50,
                canManageServers: levelData.priority >= 80,
                canIssueBans: levelData.priority >= 60,
                canManageTickets: levelData.priority >= 30,
                canAccessAdminCommands: levelData.priority >= 90
            }
        });

        await permissionLevel.save();
        created++;
    }

    const embed = new EmbedBuilder()
        .setTitle('✅ Default Permission Levels Setup')
        .setColor('#4CAF50')
        .addFields([
            { name: 'Created', value: `${created} new levels`, inline: true },
            { name: 'Skipped', value: `${skipped} existing levels`, inline: true },
            { name: 'Next Steps', value: 'Use `/managepermissions addrole` and `/managepermissions addcommand` to configure each level', inline: false }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed] });
}
