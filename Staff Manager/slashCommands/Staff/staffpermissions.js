const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const StaffMember = require('../../models/staffMember');
const ConfigPermissionManager = require('../../utils/configPermissionManager');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('staffpermissions')
        .setDescription('Manage staff member permissions and onboarding/offboarding')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addSubcommand(subcommand =>
            subcommand
                .setName('onboard')
                .setDescription('Onboard a staff member with specific permissions')
                .addUserOption(option =>
                    option.setName('staff')
                        .setDescription('Staff member to onboard')
                        .setRequired(true))
                .addStringOption(option =>
                    option.setName('steamid')
                        .setDescription('Steam ID of the staff member (required for server permissions)')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('offboard')
                .setDescription('Offboard a staff member and remove permissions')
                .addUserOption(option =>
                    option.setName('staff')
                        .setDescription('Staff member to offboard')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('modify')
                .setDescription('Modify existing staff member permissions')
                .addUserOption(option =>
                    option.setName('staff')
                        .setDescription('Staff member to modify')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('view')
                .setDescription('View staff member current permissions')
                .addUserOption(option =>
                    option.setName('staff')
                        .setDescription('Staff member to view')
                        .setRequired(true))),

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        const subcommand = interaction.options.getSubcommand();
        const targetUser = interaction.options.getUser('staff');
        const steamId = interaction.options.getString('steamid'); // Only for onboard command

        try {
            switch (subcommand) {
                case 'onboard':
                    await handleOnboard(interaction, targetUser, steamId);
                    break;
                case 'offboard':
                    await handleOffboard(interaction, targetUser);
                    break;
                case 'modify':
                    await handleModify(interaction, targetUser);
                    break;
                case 'view':
                    await handleView(interaction, targetUser);
                    break;
                default:
                    await interaction.editReply({
                        content: '❌ Unknown subcommand.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('Error in staffpermissions command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while managing staff permissions.',
                ephemeral: true
            });
        }
    }
};

async function handleOnboard(interaction, targetUser, steamId) {
    // Check if user is already a staff member
    const existingStaff = await StaffMember.findById(targetUser.id);
    if (existingStaff) {
        return await interaction.editReply({
            content: '❌ This user is already a staff member. Use `/staffpermissions modify` to change their permissions.',
            ephemeral: true
        });
    }

    // Get available permission levels from config
    const permissionManager = new ConfigPermissionManager(interaction.client);
    const permissionLevels = permissionManager.getAllPermissionLevels();
    if (permissionLevels.length === 0) {
        return await interaction.editReply({
            content: '❌ No permission levels configured in config file.',
            ephemeral: true
        });
    }

    // Store Steam ID for this onboarding process
    interaction.client.permissionHandler.storePendingOnboard(targetUser.id, interaction.user.id, steamId);

    // Create permission level selection menu
    const selectOptions = permissionManager.getSelectMenuOptions();

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`permission_select_onboard_${targetUser.id}_${interaction.user.id}`)
        .setPlaceholder('Select permission level for onboarding')
        .addOptions(selectOptions);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    const embed = new EmbedBuilder()
        .setTitle('🎯 Staff Onboarding')
        .setDescription(`Select the permission level to assign to <@${targetUser.id}>`)
        .setColor('#4CAF50')
        .addFields([
            { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
            { name: 'Steam ID', value: steamId, inline: true },
            { name: 'Action', value: 'Onboarding', inline: true },
            { name: 'Status', value: 'Awaiting permission selection', inline: true }
        ])
        .setTimestamp();

    // Add permission level details
    const levelDetails = permissionLevels.map(level =>
        `**${level.displayName}** (Priority: ${level.priority})\n${level.description || 'No description'}`
    ).join('\n\n');

    if (levelDetails.length <= 1024) {
        embed.addFields([{
            name: 'Available Permission Levels',
            value: levelDetails,
            inline: false
        }]);
    }

    await interaction.editReply({ embeds: [embed], components: [row] });
}

async function handleOffboard(interaction, targetUser) {
    // Check if user is a staff member
    const staffMember = await StaffMember.findById(targetUser.id);
    if (!staffMember) {
        return await interaction.editReply({
            content: '❌ This user is not a staff member.',
            ephemeral: true
        });
    }

    // Get all permission levels to show offload options
    const permissionManager = new ConfigPermissionManager(interaction.client);
    const permissionLevels = permissionManager.getAllPermissionLevels();

    // Create confirmation embed
    const embed = new EmbedBuilder()
        .setTitle('⚠️ Staff Offboarding Confirmation')
        .setDescription(`Are you sure you want to offboard <@${targetUser.id}>?`)
        .setColor('#FF9800')
        .addFields([
            { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
            { name: 'Current Status', value: 'Active Staff Member', inline: true },
            { name: 'Action', value: 'Complete Offboarding', inline: true },
            { name: '⚠️ Warning', value: 'This will:\n• Remove all Discord roles\n• Remove server permissions\n• Deactivate staff record\n• Cannot be easily undone', inline: false }
        ])
        .setTimestamp();

    const confirmButton = new ButtonBuilder()
        .setCustomId(`confirm_offboard_${targetUser.id}_${interaction.user.id}`)
        .setLabel('Confirm Offboarding')
        .setStyle(ButtonStyle.Danger)
        .setEmoji('⚠️');

    const cancelButton = new ButtonBuilder()
        .setCustomId(`cancel_offboard_${targetUser.id}_${interaction.user.id}`)
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary);

    const row = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

    await interaction.editReply({ embeds: [embed], components: [row] });
}

async function handleModify(interaction, targetUser) {
    // Check if user is a staff member
    const staffMember = await StaffMember.findById(targetUser.id);
    if (!staffMember) {
        return await interaction.editReply({
            content: '❌ This user is not a staff member.',
            ephemeral: true
        });
    }

    // Get available permission levels from config
    const permissionManager = new ConfigPermissionManager(interaction.client);
    const permissionLevels = permissionManager.getAllPermissionLevels();
    if (permissionLevels.length === 0) {
        return await interaction.editReply({
            content: '❌ No permission levels configured in config file.',
            ephemeral: true
        });
    }

    // Create permission level selection menu
    const selectOptions = permissionManager.getSelectMenuOptions();

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`permission_select_modify_${targetUser.id}_${interaction.user.id}`)
        .setPlaceholder('Select new permission level')
        .addOptions(selectOptions);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    const embed = new EmbedBuilder()
        .setTitle('🔄 Modify Staff Permissions')
        .setDescription(`Select the new permission level for <@${targetUser.id}>`)
        .setColor('#2196F3')
        .addFields([
            { name: 'Target User', value: `<@${targetUser.id}>`, inline: true },
            { name: 'Current Status', value: 'Active Staff Member', inline: true },
            { name: 'Action', value: 'Modify Permissions', inline: true }
        ])
        .setTimestamp();

    await interaction.editReply({ embeds: [embed], components: [row] });
}

async function handleView(interaction, targetUser) {
    // Check if user is a staff member
    const staffMember = await StaffMember.findById(targetUser.id);
    if (!staffMember) {
        return await interaction.editReply({
            content: '❌ This user is not a staff member.',
            ephemeral: true
        });
    }

    // Get current permission level (this would need to be implemented based on your system)
    // For now, we'll show basic staff information
    const embed = new EmbedBuilder()
        .setTitle('👤 Staff Member Information')
        .setDescription(`Permission details for <@${targetUser.id}>`)
        .setColor('#2196F3')
        .addFields([
            { name: 'Discord Name', value: staffMember.discord_name, inline: true },
            { name: 'Steam ID', value: staffMember.steam_id, inline: true },
            { name: 'Status', value: staffMember.active ? '✅ Active' : '❌ Inactive', inline: true },
            { name: 'BattleMetrics ID', value: staffMember.bm_id || 'Not set', inline: true },
            { name: 'BattleMetrics Email', value: staffMember.bm_email || 'Not set', inline: true },
            { name: 'Region', value: staffMember.region_info?.region || 'Unknown', inline: true }
        ])
        .setTimestamp()
        .setFooter({ text: `Staff ID: ${staffMember._id}` });

    // Add current Discord roles
    try {
        const guild = interaction.guild;
        const member = await guild.members.fetch(targetUser.id);
        const roles = member.roles.cache
            .filter(role => role.id !== guild.id) // Exclude @everyone
            .map(role => role.name)
            .join(', ');

        embed.addFields([{
            name: 'Current Discord Roles',
            value: roles || 'No roles assigned',
            inline: false
        }]);
    } catch (error) {
        console.error('Error fetching member roles:', error);
    }

    await interaction.editReply({ embeds: [embed] });
}
