const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Watchlist = require('../../models/watchlistModel');
const moment = require('moment');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('listwatchlist')
        .setDescription('List players on your watchlist'),
    
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });
        
        // Get watchlist entries
        const watchlistEntries = await Watchlist.find({ 
            requestedBy: interaction.user.id,
            isActive: true
        }).sort({ lastSeen: -1 });
        
        if (!watchlistEntries || watchlistEntries.length === 0) {
            return interaction.editReply(client.locals.Watchlist.List.Empty);
        }
        
        // Create embed
        const embed = new EmbedBuilder()
            .setTitle(client.locals.Watchlist.List.Title)
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
            .setTimestamp();
        
        // Add entries
        let description = '';
        
        for (const entry of watchlistEntries) {
            const lastSeen = entry.lastSeen ? moment(entry.lastSeen).fromNow() : 'Never';
            
            description += `**${entry.playerName}** - \`${entry.steamId}\`\n`;
            description += `${client.locals.Watchlist.List.Entry.replace('{player}', entry.playerName).replace('{lastSeen}', lastSeen)}\n\n`;
        }
        
        embed.setDescription(description);
        
        return interaction.editReply({ embeds: [embed] });
    }
};
