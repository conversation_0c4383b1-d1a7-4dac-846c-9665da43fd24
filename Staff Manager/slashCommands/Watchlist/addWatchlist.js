const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Watchlist = require('../../models/watchlistModel');
const BattleMetricsClient = require('../../utils/battlemetricsClient');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('addwatchlist')
        .setDescription('Add a player to your watchlist')
        .addStringOption(option => 
            option.setName('steamid')
                .setDescription('The Steam ID of the player')
                .setRequired(true)),
    
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });
        
        // Get options
        const steamId = interaction.options.getString('steamid');
        
        // Validate Steam ID format
        if (!steamId.match(/^[0-9]{17}$/)) {
            return interaction.editReply(client.locals.General.InvalidSteamID || 'Invalid Steam ID format. Please provide a valid Steam ID.');
        }
        
        // Check if player is already on watchlist
        const existingWatchlist = await Watchlist.findOne({ 
            steamId: steamId,
            requestedBy: interaction.user.id,
            isActive: true
        });
        
        if (existingWatchlist) {
            return interaction.editReply(client.locals.Watchlist.Add.AlreadyWatching.replace('{player}', existingWatchlist.playerName));
        }
        
        // Try to find BattleMetrics ID and player name
        let bmId = null;
        let playerName = 'Unknown';
        let playerDetails = null;
        
        try {
            const response = await BattleMetricsClient.searchPlayerBySteamId(steamId);
            
            if (response.status === 200 && response.dict.data && response.dict.data.length > 0) {
                bmId = response.dict.data[0].id;
                playerName = response.dict.data[0].attributes.name;
                playerDetails = response.dict.data[0];
            }
        } catch (error) {
            console.error('Error searching for BattleMetrics ID:', error);
        }
        
        // Create watchlist entry
        const watchlistEntry = new Watchlist({
            steamId: steamId,
            battlemetricsId: bmId,
            playerName: playerName,
            requestedBy: interaction.user.id,
            playerDetails: playerDetails
        });
        
        try {
            await watchlistEntry.save();
            
            // Create success embed
            const embed = new EmbedBuilder()
                .setTitle('Player Added to Watchlist')
                .setDescription(client.locals.Watchlist.Add.Success.replace('{player}', playerName))
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .addFields(
                    { name: 'Player', value: playerName, inline: true },
                    { name: 'Steam ID', value: steamId, inline: true }
                )
                .setTimestamp();
            
            if (bmId) {
                embed.addFields({ name: 'BattleMetrics ID', value: bmId, inline: true });
            }
            
            return interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.error('Error adding player to watchlist:', error);
            return interaction.editReply(client.locals.Watchlist.Add.Error.replace('{player}', playerName).replace('{error}', error.message));
        }
    }
};
