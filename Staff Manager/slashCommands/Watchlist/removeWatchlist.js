const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const Watchlist = require('../../models/watchlistModel');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('removewatchlist')
        .setDescription('Remove a player from your watchlist')
        .addStringOption(option => 
            option.setName('steamid')
                .setDescription('The Steam ID of the player')
                .setRequired(true)),
    
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });
        
        // Get options
        const steamId = interaction.options.getString('steamid');
        
        // Validate Steam ID format
        if (!steamId.match(/^[0-9]{17}$/)) {
            return interaction.editReply(client.locals.General.InvalidSteamID || 'Invalid Steam ID format. Please provide a valid Steam ID.');
        }
        
        // Check if player is on watchlist
        const watchlistEntry = await Watchlist.findOne({ 
            steamId: steamId,
            requestedBy: interaction.user.id,
            isActive: true
        });
        
        if (!watchlistEntry) {
            return interaction.editReply(client.locals.Watchlist.Remove.NotWatching.replace('{player}', steamId));
        }
        
        // Remove from watchlist (set as inactive)
        watchlistEntry.isActive = false;
        
        try {
            await watchlistEntry.save();
            
            // Create success embed
            const embed = new EmbedBuilder()
                .setTitle('Player Removed from Watchlist')
                .setDescription(client.locals.Watchlist.Remove.Success.replace('{player}', watchlistEntry.playerName))
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .addFields(
                    { name: 'Player', value: watchlistEntry.playerName, inline: true },
                    { name: 'Steam ID', value: steamId, inline: true }
                )
                .setTimestamp();
            
            return interaction.editReply({ embeds: [embed] });
        } catch (error) {
            console.error('Error removing player from watchlist:', error);
            return interaction.editReply(client.locals.Watchlist.Remove.Error.replace('{player}', watchlistEntry.playerName).replace('{error}', error.message));
        }
    }
};
