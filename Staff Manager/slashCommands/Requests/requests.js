const { <PERSON><PERSON><PERSON>ommandBuilder, EmbedBuilder, PermissionFlagsBits, StringSelectMenuBuilder, ActionRowBuilder } = require('discord.js');
const BattleMetricsClient = require('../../utils/battlemetricsClient');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('requests')
        .setDescription('Submit a request for ban, unban, spectate, or blacklist')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addStringOption(option =>
            option.setName('action')
                .setDescription('The action to perform')
                .setRequired(true)
                .addChoices(
                    { name: 'Ban', value: 'ban' },
                    { name: 'Unban', value: 'unban' },
                    { name: 'Spectate', value: 'spectate' },
                    { name: 'Blacklist', value: 'blacklist' }
                )),

    async execute(interaction, client) {
        const selectedAction = interaction.options.getString('action');

        // If the action is blacklist, skip server selection and show the modal directly
        if (selectedAction === 'blacklist') {
            const modal = new ModalBuilder()
                .setCustomId(`request_modal_${selectedAction}_none`)
                .setTitle(`${selectedAction.charAt(0).toUpperCase() + selectedAction.slice(1)} Request`);

            const steamIdInput = new TextInputBuilder()
                .setCustomId('steam_id')
                .setLabel('Discord ID')
                .setStyle(TextInputStyle.Short)
                .setRequired(true); // Required for blacklist

            const evidenceInput = new TextInputBuilder()
                .setCustomId('evidence')
                .setLabel('Evidence (URLs, comma-separated)')
                .setStyle(TextInputStyle.Paragraph)
                .setRequired(false);

            const reasonInput = new TextInputBuilder()
                .setCustomId('reason')
                .setLabel('Reason')
                .setStyle(TextInputStyle.Paragraph)
                .setRequired(true);

            const steamIdRow = new ActionRowBuilder().addComponents(steamIdInput);
            const evidenceRow = new ActionRowBuilder().addComponents(evidenceInput);
            const reasonRow = new ActionRowBuilder().addComponents(reasonInput);

            modal.addComponents(steamIdRow, evidenceRow, reasonRow);

            await interaction.showModal(modal);
            return;
        }

        // Step 1: Fetch servers from BattleMetrics
        const servers = await BattleMetricsClient.getOrganizationServers();

        if (servers.length === 0) {
            console.error('No servers found in BattleMetrics organization. Check logs for details.');
            return interaction.reply({ content: 'No servers found in BattleMetrics organization. Please check the configuration and logs.', ephemeral: true });
        }

        console.log('Servers available for selection:', servers);

        // Step 2: Create a select menu for servers
        const serverSelect = new StringSelectMenuBuilder()
            .setCustomId(`request_server_${selectedAction}_${interaction.user.id}`)
            .setPlaceholder('Select a server')
            .addOptions(servers.map(server => ({
                label: server.name,
                value: server.id
            })));

        const serverRow = new ActionRowBuilder().addComponents(serverSelect);

        // Step 3: Reply with the select menu
        const embed = new EmbedBuilder()
            .setTitle('Submit a Request')
            .setDescription(`Action selected: **${selectedAction}**\nPlease select a server to proceed.`)
            .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC');

        await interaction.reply({
            embeds: [embed],
            components: [serverRow],
            ephemeral: true
        });
    }
};