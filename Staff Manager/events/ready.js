const { ActivityType } = require('discord.js');
const RconClient = require('../utils/rconClient');
const { updateStaffStatusEmbed } = require('../utils/staffStatusEmbed');
const StaffInfoUpdater = require('../tasks/staffInfoUpdater');
const fs = require('fs');

module.exports = async (client) => {
    console.log(`Bot is ready as ${client.user.tag}`);

    // Set bot activity
    client.user.setActivity('Staff Activity', { type: ActivityType.Watching });

    // Verify guild exists
    const guild = client.guilds.cache.get(client.config.BotSettings.GuildID);
    if (!guild) {
        console.error(`[ERROR] The guild ID specified in the config is invalid or the bot is not in the server!`);
        console.error(`You can use the link below to invite the bot to your server:`);
        console.error(`https://discord.com/api/oauth2/authorize?client_id=${client.user.id}&permissions=8&scope=bot%20applications.commands`);
        process.exit(1);
    }

    // Initialize RCON connections
    try {
        console.log('Initializing RCON connections...');

        // Create logs directory if it doesn't exist
        if (!fs.existsSync('./logs')) {
            fs.mkdirSync('./logs', { recursive: true });
        }

        // Initialize RCON connections for each server
        const servers = client.config.Servers || [];
        client.rconConnections = {};

        for (const server of servers) {
            try {
                console.log(`Initializing RCON connection to ${server.name}...`);
                const rcon = new RconClient(
                    server.ip,
                    server.rcon_port,
                    server.rcon_password,
                    server.name
                );

                // Start connection manager
                rcon.startConnectionManager();

                // Store in client for later use
                client.rconConnections[server.name] = rcon;
            } catch (error) {
                console.error(`Failed to initialize RCON connection to ${server.name}:`, error);
            }
        }

        console.log('RCON connections initialized');

        // Log RCON initialization
        fs.appendFile('./logs/rcon.log', `\n[${new Date().toISOString()}] [SYSTEM] RCON connections initialized\n`, (err) => {
            if (err) console.error('Failed to write to RCON log file:', err);
        });
    } catch (error) {
        console.error('Failed to initialize RCON connections:', error);
    }

    // Initialize staff info updater
    try {
        StaffInfoUpdater.init();
        console.log('Staff info updater initialized');
    } catch (error) {
        console.error('Failed to initialize staff info updater:', error);
    }

    // Initialize ban monitoring
    try {
        console.log('Starting BattleMetrics ban monitoring...');
        await client.banMonitor.startMonitoring();
        console.log('Ban monitoring initialized successfully');
    } catch (error) {
        console.error('Failed to initialize ban monitoring:', error);
    }

    // Initialize staff status embed (after RCON connections are established)
    try {
        // Wait longer for connections to establish (45 seconds)
        console.log('Waiting for RCON connections to establish before updating staff status...');
        await new Promise(resolve => setTimeout(resolve, 45000));

        // Check RCON connections status
        console.log('Checking RCON connections status:');
        for (const [serverName, rcon] of Object.entries(client.rconConnections)) {
            console.log(`- ${serverName}: ${rcon.connected ? '✅ Connected' : '❌ Disconnected'}`);

            // If not connected, try to reconnect
            if (!rcon.connected) {
                console.log(`  Attempting to reconnect to ${serverName}...`);
                rcon.connect();
            }
        }

        // Wait a bit more for reconnection attempts
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Update staff status embed
        console.log('Updating staff status embed...');
        await updateStaffStatusEmbed(client);
        console.log('Staff status embed initialized');

        // Update staff status every 3 minutes (more frequent updates)
        setInterval(() => {
            console.log('Running scheduled staff status update...');
            updateStaffStatusEmbed(client);
        }, 60 * 1000);
    } catch (error) {
        console.error('Failed to initialize staff status embed:', error);
    }

    // Register slash commands
    try {
        const { REST, Routes } = require('discord.js');
        const commands = Array.from(client.slashCommands.values()).map(command => command.data.toJSON());

        const rest = new REST({ version: '10' }).setToken(client.config.BotSettings.Token);

        await rest.put(
            Routes.applicationGuildCommands(client.user.id, client.config.BotSettings.GuildID),
            { body: commands }
        );

        console.log(`Successfully registered ${commands.length} application commands`);
    } catch (error) {
        console.error('Error registering application commands:', error);
    }
};
