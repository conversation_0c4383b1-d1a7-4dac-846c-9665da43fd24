const { InteractionType, <PERSON>ton<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, ChannelType, ButtonBuilder } = require('discord.js');
const BattleMetricsClient = require('../utils/battlemetricsClient');
const mongoose = require('mongoose');
const Users = require('../models/Users');
const axios = require('axios');
const cheerio = require('cheerio');

module.exports = async (client, interaction) => {
    // Define helper functions
    async function getSteamData(steamID) {
        if (!client.config.APIKeys.steam_api_key) {
            console.error("Missing Steam API Key!");
            return { playtime: "N/A", banStatus: "N/A", gameBans: "N/A" };
        }

        const bansURL = `https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=${client.config.APIKeys.steam_api_key}&steamids=${steamID}`;
        const playtimeURI = `https://api.steampowered.com/IPlayerService/GetOwnedGames/v1/?key=${client.config.APIKeys.steam_api_key}&steamid=${steamID}&include_played_free_games=true`;
        const playerSummariesURL = `https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/?key=${client.config.APIKeys.steam_api_key}&steamids=${steamID}`;

        try {
            const [banResponse, playtimeResponse, summaryResponse] = await Promise.all([
                axios.get(bansURL),
                axios.get(playtimeURI),
                axios.get(playerSummariesURL)
            ]);

            let profileStatus = "Public";
            let playerData = summaryResponse.data?.response?.players?.[0];

            if (playerData && playerData.communityvisibilitystate !== 3) {
                profileStatus = "Private";
                console.log(`ℹ️ Steam profile ${steamID} is private.`);
            }

            let playtime = "N/A";
            if (playtimeResponse.data?.response?.games && profileStatus === "Public") {
                let rustGame = playtimeResponse.data.response.games.find(game => game.appid === 252490);
                if (rustGame) {
                    let totalMinutes = rustGame.playtime_forever || 0;
                    playtime = (totalMinutes / 60).toFixed(1);
                }
            }

            const banData = banResponse.data?.players?.[0] || {};
            let banStatus = banData.VACBanned ? `VAC Banned: ${banData.NumberOfVACBans} times` : "No Bans";
            let gameBans = banData.NumberOfGameBans > 0 ? `Game Bans: ${banData.NumberOfGameBans}` : "No Game Bans";

            return { playtime, banStatus, gameBans, profileStatus };
        } catch (error) {
            console.error("❌ Failed to fetch Steam data:", error.message);
            return { playtime: "N/A", banStatus: "N/A", gameBans: "N/A", profileStatus: "Error" };
        }
    }

    async function checkGameBan(steamID) {
        const nexusURL = `https://www.nexusonline.co.uk/bans/profile/?id=${steamID}`;

        try {
            const response = await axios.get(nexusURL);
            const $ = cheerio.load(response.data);
            const banText = $(".header").text().trim();
            let banStatus = "No Game Bans Found";
            if (banText.includes("Player is currently game banned!")) {
                banStatus = "User is Game Banned!";
            }

            return { banStatus, nexusProfile: nexusURL };
        } catch (error) {
            console.error("❌ Failed to fetch game ban status:", error.message);
            return { banStatus: "N/A (Failed to Check)", nexusProfile: nexusURL };
        }
    }

    async function getBattleMetricsData(steamID) {
        if (!steamID || steamID === 'N/A') {
            console.log("❌ Invalid Steam ID provided to getBattleMetricsData");
            return {
                bans: "No Bans Found",
                bannedServers: [],
                f7Total: "N/A",
                altAccounts: "No Alts Found",
                accountAge: "N/A",
                serversPlayed: "N/A",
                profileURL: "N/A",
                rconURL: "N/A",
                BMID: "N/A"
            };
        }

        if (!client.config.APIKeys.bm_api_token) {
            console.log("❌ Missing BattleMetrics API Key!");
            return {
                bans: "No Bans Found",
                bannedServers: [],
                f7Total: "N/A",
                altAccounts: "No Alts Found",
                accountAge: "N/A",
                serversPlayed: "N/A",
                profileURL: "N/A",
                rconURL: "N/A",
                BMID: "N/A"
            };
        }

        // Format the API token correctly
        let apiToken = client.config.APIKeys.bm_api_token;
        if (apiToken.startsWith('Bearer ')) {
            apiToken = apiToken.substring(7);
        }

        const headers = { Authorization: `Bearer ${apiToken}` };
        const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;

        console.log(`Making BattleMetrics API request to: ${bmURL}`);

        try {
            const response = await axios.get(bmURL, {
                headers,
                timeout: 15000 // 15 second timeout
            });

            console.log(`BattleMetrics API response status: ${response.status}`);

            if (!response.data || !response.data.data || response.data.data.length === 0) {
                console.log(`No player data found for Steam ID: ${steamID}`);
                return {
                    bans: "No Bans Found",
                    bannedServers: [],
                    f7Total: "N/A",
                    altAccounts: "No Alts Found",
                    accountAge: "N/A",
                    serversPlayed: "N/A",
                    profileURL: "N/A",
                    rconURL: "N/A",
                    BMID: "N/A"
                };
            }

            const playerData = response.data.data[0];
            console.log(`Found BattleMetrics player: ${playerData.id}`);

            let profileURL = `https://www.battlemetrics.com/players/${playerData.id}`;
            let rconURL = `https://www.battlemetrics.com/rcon/players/${playerData.id}`;
            let bans = playerData.attributes.banCount > 0 ? `🚨 ${playerData.attributes.banCount} Recorded Bans` : "No Bans Found";
            let serversPlayed = playerData.relationships?.servers?.data?.length ? `${playerData.relationships.servers.data.length}` : "N/A";
            let accountCreatedAt = playerData.attributes.createdAt || null;
            let accountAge = accountCreatedAt
                ? `<t:${Math.floor(new Date(accountCreatedAt).getTime() / 1000)}:F>`
                : "N/A";

            let f7Total = "N/A";
            if (playerData.relationships?.playerFlags?.data?.length > 0) {
                f7Total = `${playerData.relationships.playerFlags.data.length}`;
            }

            let bannedServers = [];
            if (playerData.relationships?.flags?.data?.length > 0) {
                bannedServers = playerData.relationships.flags.data
                    .map(flag => `[Server Ban](https://www.battlemetrics.com/players/${flag.id})`);
            }

            console.log(`Successfully processed BattleMetrics data for player ${playerData.id}`);

            return {
                bans,
                bannedServers,
                f7Total,
                accountAge,
                serversPlayed,
                profileURL,
                rconURL,
                BMID: `${playerData.id}`
            };
        } catch (error) {
            console.error(`❌ Failed to fetch BattleMetrics data for ${steamID}:`, error.message);

            if (error.response) {
                console.error(`Response status: ${error.response.status}`);
                console.error(`Response data:`, error.response.data);
            }

            return {
                bans: "No Bans Found",
                bannedServers: [],
                f7Total: "N/A",
                altAccounts: [],
                accountAge: "N/A",
                serversPlayed: "N/A",
                profileURL: "N/A",
                rconURL: "N/A",
                BMID: "N/A"
            };
        }
    }

    async function getRustStats(steamID) {
        if (!client.config.APIKeys.steam_api_key) {
            console.error("❌ Missing Steam API Key!");
            return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
        }

        const rustStatsURL = `https://api.steampowered.com/ISteamUserStats/GetUserStatsForGame/v0002/?appid=252490&key=${client.config.APIKeys.steam_api_key}&steamid=${steamID}`;

        try {
            const response = await axios.get(rustStatsURL);

            if (!response.data?.playerstats?.stats) {
                return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
            }

            const stats = response.data.playerstats.stats;

            const killsStat = stats.find(stat => stat.name === "kill_player");
            const deathsStat = stats.find(stat => stat.name === "deaths");

            const kills = killsStat ? killsStat.value : 0;
            const deaths = deathsStat ? deathsStat.value : 0;
            const kdRatio = deaths > 0 ? (kills / deaths).toFixed(2) : `${kills}.00`;

            return { kills, deaths, kdRatio };
        } catch (error) {
            console.error("❌ Failed to fetch Rust stats:", error.message);
            return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
        }
    }

    async function getKDOver14Days(steamID) {
        if (!client.config.APIKeys.bm_api_token) return "N/A";

        const headers = { Authorization: `Bearer ${client.config.APIKeys.bm_api_token}` };

        const now = new Date();
        const fourteenDaysAgo = new Date(now - 14 * 24 * 60 * 60 * 1000);

        const combatLogURL = `https://api.battlemetrics.com/activity?filter[players]=${steamID}&filter[types]=kill,death&include=server&sort=-timestamp`;

        try {
            const response = await axios.get(combatLogURL, { headers });
            if (!response.data?.data.length) {
                return "N/A";
            }

            let kills = 0, deaths = 0;

            response.data.data.forEach(log => {
                let eventTime = new Date(log.attributes.timestamp);
                if (eventTime >= fourteenDaysAgo && eventTime <= now) {
                    let eventType = log.attributes.type;
                    if (eventType === "kill") kills++;
                    if (eventType === "death") deaths++;
                }
            });

            let kdRatio = deaths > 0 ? (kills / deaths).toFixed(2) : `${kills}.00`;
            console.log(`✅ ${steamID} KD Over 14 Days: ${kills}/${deaths} (K/D: ${kdRatio})`);
            return `${kills}/${deaths} (K/D: ${kdRatio})`;
        } catch (error) {
            console.log(`❌ Failed to fetch K/D Over 14 Days for ${steamID}: ${error.response?.data?.errors?.[0]?.detail || error.message}`);
            return "N/A";
        }
    }

    async function GetBans(steamID) {
        if (!steamID || steamID === 'N/A') {
            console.error("❌ Error: steamID is undefined or invalid, cannot fetch bans.");
            return { bans: "Error: steamID is missing or invalid.", bannedServers: [] };
        }

        try {
            if (!client.config.APIKeys.bm_api_token) {
                console.error("❌ Error: Missing BattleMetrics API token.");
                return { bans: "Error: Missing API token.", bannedServers: [] };
            }

            // Format the API token correctly
            let apiToken = client.config.APIKeys.bm_api_token;
            if (apiToken.startsWith('Bearer ')) {
                apiToken = apiToken.substring(7);
            }

            const headers = { Authorization: `Bearer ${apiToken}` };

            console.log(`Searching for player with Steam ID: ${steamID}`);
            const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;

            const BMData = await axios.get(bmURL, {
                headers,
                timeout: 15000 // 15 second timeout
            });

            console.log(`BattleMetrics player search response status: ${BMData.status}`);

            if (!BMData.data?.data?.length) {
                console.log(`No player found on BattleMetrics for Steam ID: ${steamID}`);
                return { bans: "Error: Player not found on BattleMetrics.", bannedServers: [] };
            }

            const playerData = BMData.data.data[0];
            console.log(`Found BattleMetrics player: ${playerData.id}`);

            const bansURL = `https://api.battlemetrics.com/bans?filter[player]=${playerData.id}&include=server&page[size]=100`;
            console.log(`Fetching bans from: ${bansURL}`);

            const response = await axios.get(bansURL, {
                headers,
                timeout: 15000 // 15 second timeout
            });

            console.log(`BattleMetrics bans response status: ${response.status}`);

            if (response.status === 401) {
                console.error("❌ Unauthorized: Check API token permissions.");
                return { bans: "Error: Unauthorized request. Check API token.", bannedServers: [] };
            }

            if (response.status !== 200) {
                console.error(`⚠️ BattleMetrics API returned status: ${response.status}`);
                return { bans: "Error fetching ban data.", bannedServers: [] };
            }

            const bans = response.data?.data || [];
            console.log(`Found ${bans.length} bans for player ${playerData.id}`);

            if (bans.length === 0) return { bans: "No bans on record.", bannedServers: [] };

            let bannedServers = [];

            for (let ban of bans) {
                let serverName = "Unknown Server";

                if (ban.relationships?.server?.data?.id) {
                    const serverData = response.data.included?.find(s => s.id === ban.relationships.server.data.id);
                    if (serverData) {
                        serverName = serverData.attributes?.name || "Unknown Server";
                    }
                }

                let reason = ban.attributes?.reason || "No reason provided";
                bannedServers.push(`${serverName} - ${reason}\n`);
            }

            const banReasons = response.data?.data.length
                ? response.data.data.map(ban => ban.attributes.reason).join("\n")
                : "No bans on record.";

            return {
                bans: banReasons,
                bannedServers: bannedServers.length > 0 ? bannedServers.join("\n") : "No banned servers found.",
            };
        } catch (error) {
            console.error(`❌ Error fetching bans for ${steamID}:`, error.message);

            if (error.response) {
                console.error(`Response status: ${error.response.status}`);
                console.error(`Response data:`, error.response.data);

                if (error.response.status === 401) {
                    return { bans: "Error: Unauthorized API request. Check API token.", bannedServers: [] };
                }
            }

            return { bans: "Failed to retrieve ban data.", bannedServers: [] };
        }
    }

    async function GetHoursandServerInfo(steamID, retryCount = 0) {
        if (!steamID || steamID === 'N/A') {
            console.error(`❌ Invalid Steam ID provided to GetHoursandServerInfo: ${steamID}`);
            return {
                allHours: 0,
                orgHours: 0,
                aimtrainHours: 0,
                totalServers: 0,
                orgServers: []
            };
        }

        // Format the API token correctly
        let apiToken = client.config.APIKeys.bm_api_token;
        if (apiToken.startsWith('Bearer ')) {
            apiToken = apiToken.substring(7);
        }

        const headers = { Authorization: `Bearer ${apiToken}` };
        const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;

        console.log(`Searching for player with Steam ID: ${steamID} for hours info`);

        try {
            const BMData = await axios.get(bmURL, {
                headers,
                timeout: 15000 // 15 second timeout
            });

            console.log(`BattleMetrics player search response status: ${BMData.status}`);

            const playerData = BMData.data?.data?.[0];
            if (!playerData) {
                console.error(`❌ Player not found for steamID: ${steamID}`);
                return {
                    allHours: 0,
                    orgHours: 0,
                    aimtrainHours: 0,
                    totalServers: 0,
                    orgServers: []
                };
            }

            console.log(`Found BattleMetrics player: ${playerData.id}`);

            const url = `https://api.battlemetrics.com/players/${playerData.id}?include=server&fields[server]=name`;
            console.log(`Fetching player details from: ${url}`);

            const response = await axios.get(url, {
                headers,
                timeout: 15000 // 15 second timeout
            });

            console.log(`BattleMetrics player details response status: ${response.status}`);

            if (response.status !== 200) {
                throw new Error(`Unexpected response status: ${response.status}`);
            }

            let totalHours = 0, aimtrainHours = 0, orgHours = 0, totalServers = 0;
            let orgServers = [];

            if (response.data?.included) {
                console.log(`Found ${response.data.included.length} servers in player details`);

                response.data.included.forEach(server => {
                    if (server.relationships?.game?.data?.id === "rust") {
                        totalServers++;
                        totalHours += server.meta?.timePlayed || 0;

                        const serverName = server.attributes?.name?.toLowerCase() || '';
                        console.log(`Processing server: ${server.attributes?.name || 'Unknown'}, Time played: ${server.meta?.timePlayed || 0} seconds`);

                        if (serverName.includes("aim") ||
                            serverName.includes("training grounds") ||
                            serverName.includes("ukn") ||
                            serverName.includes("aimtrain")) {
                            aimtrainHours += server.meta?.timePlayed || 0;
                            console.log(`Added ${server.meta?.timePlayed || 0} seconds to aimtrain hours`);
                        } else if (server?.relationships?.organization?.data?.id === client.config.StaffManagement.BattlemetricsORGID) {
                            orgHours += server.meta?.timePlayed || 0;
                            console.log(`Added ${server.meta?.timePlayed || 0} seconds to org hours`);
                        }

                        if (server.relationships?.organization?.data?.id === client.config.StaffManagement.BattlemetricsORGID) {
                            orgServers.push({
                                serverName: server.attributes.name || "Unknown",
                                serverId: server.id || "N/A",
                                online: server.meta?.online || false,
                                timePlayed: server.meta?.timePlayed || 0,
                                lastSeen: server.meta?.lastSeen || "Unknown"
                            });
                        }
                    }
                });
            }

            console.log(`Total hours: ${totalHours/3600}, Org hours: ${orgHours/3600}, Aimtrain hours: ${aimtrainHours/3600}`);

            return {
                allHours: totalHours / 3600 || 0,
                orgHours: orgHours / 3600 || 0,
                aimtrainHours: aimtrainHours / 3600 || 0,
                totalServers: totalServers || 0,
                orgServers
            };
        } catch (error) {
            console.error(`❌ Error in GetHoursandServerInfo for ${steamID}:`, error.message);

            if (error.response) {
                console.error(`Response status:`, error.response.status);
                console.error(`Response data:`, error.response.data);

                if (error.response.status === 429 && retryCount < 3) {
                    console.warn(`⚠️ Rate limited! Retrying in 5 seconds (attempt ${retryCount + 1}/3)...`);
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    return await GetHoursandServerInfo(steamID, retryCount + 1);
                }
            }

            return {
                allHours: 0,
                orgHours: 0,
                aimtrainHours: 0,
                totalServers: 0,
                orgServers: []
            };
        }
    }

    async function GetKDandF7(steamID) {
        if (!steamID || steamID === "") {
            console.error("❌ Error: steamID is undefined or empty, cannot fetch K/D and F7 reports.");
            return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
        }

        let kills = 0, deaths = 0;
        let killsOneDay = 0, deathsOneDay = 0;
        let f7reports = 0, f7reportsOneDay = 0;
        let oneDay = Date.now() - (14 * 24 * 60 * 60 * 1000);

        const headers = { Authorization: `Bearer ${client.config.APIKeys.bm_api_token}` };
        const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;

        try {
            const BMData = await axios.get(bmURL, { headers });

            if (!BMData.data?.data?.length) {
                console.log(`❌ Player not found for steamID: ${steamID}`);
                return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
            }

            const playerData = BMData.data.data[0];
            if (!playerData?.id) {
                console.error(`❌ Player data missing ID for steamID: ${steamID}`);
                return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
            }

            let url = `https://api.battlemetrics.com/activity?tagTypeMode=and&filter[types][blacklist]=event:query&filter[players]=${playerData.id}&include=organization,user&page[size]=1000&access_token=${client.config.APIKeys.bm_api_token}`;

            await fetchData(url);
        } catch (error) {
            console.error(`❌ Error fetching K/D and F7 reports: ${error.message}`);
            return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
        }

        function parseEvent(event) {
            if (!event || !event.attributes) return;

            let timestamp = new Date(event.attributes.timestamp).getTime();

            if (event.attributes.messageType === "rustLog:playerDeath:PVP") {
                if (event.attributes.data?.killer_id === playerData.id) {
                    kills++;
                    if (timestamp > oneDay) killsOneDay++;
                } else if (event.attributes.data?.victim_id === playerData.id) {
                    deaths++;
                    if (timestamp > oneDay) deathsOneDay++;
                }
            }

            if (event.attributes.messageType === "rustLog:playerReport") {
                if (event.attributes.data?.forPlayerId === playerData.id) {
                    f7reports++;
                    if (timestamp > oneDay) f7reportsOneDay++;
                }
            }
        }

        async function fetchData(requestUrl) {
            try {
                const response = await axios.get(requestUrl, { timeout: 10000 });

                if (response.status !== 200) throw new Error(`Unexpected response status: ${response.status}`);

                const events = response.data?.data || [];
                events.forEach(parseEvent);

                if (response.data?.links?.next) {
                    await fetchData(response.data.links.next);
                }
            } catch (error) {
                if (error.response?.status === 429) {
                    console.warn(`⚠️ Rate limited! Retrying in 5 seconds...`);
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    await fetchData(requestUrl);
                } else {
                    throw error;
                }
            }
        }

        let kd = deaths > 0 ? (kills / deaths).toFixed(2) : kills.toFixed(2);
        let kdOneDay = deathsOneDay > 0 ? (killsOneDay / deathsOneDay).toFixed(2) : killsOneDay.toFixed(2);

        return { KDTotal: kd, KDDay: kdOneDay, killsTotal: kills, deathsTotal: deaths, kills24hr: killsOneDay, deaths24hr: deathsOneDay, F7Total: f7reports, F7Day: f7reportsOneDay };
    }

    // Handle slash commands
    if (interaction.type === InteractionType.ApplicationCommand) {
        const command = client.slashCommands.get(interaction.commandName);

        if (!command) return;

        try {
            // Log command usage if enabled
            if (client.config.BotSettings.LogCommands) {
                console.log(`[COMMAND] ${interaction.user.tag} used /${interaction.commandName}`);
            }

            await command.execute(interaction, client);
        } catch (error) {
            console.error(`Error executing command ${interaction.commandName}:`, error);

            // Create a more detailed error message
            const errorMessage = `${client.locals.General.CommandError || "An error occurred while executing this command."}\n\`\`\`${error.message}\`\`\``;

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: errorMessage, ephemeral: true }).catch(console.error);
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true }).catch(console.error);
            }
        }
    }

    // Handle button interactions
    if (interaction.isButton()) {
        const [action, type, ...rest] = interaction.customId.split('_');

        // Existing button handlers
        if (action === 'banrequest' && type === 'create') {
            // Handle ban request creation button
            const BanRequestUtils = require('../utils/banRequestUtils');
            await interaction.showModal(BanRequestUtils.createBanRequestModal(rest[0]));
        } else if (action === 'banrequest' && type === 'approve') {
            // Handle ban request approval button
            const BanRequestUtils = require('../utils/banRequestUtils');
            await BanRequestUtils.handleBanRequestApproval(interaction, rest[0]);
        } else if (action === 'banrequest' && type === 'reject') {
            // Handle ban request rejection button
            const BanRequestUtils = require('../utils/banRequestUtils');
            await BanRequestUtils.handleBanRequestRejection(interaction, rest[0]);
        } else if (action === 'spectaterequest' && type === 'create') {
            // Handle spectate request creation button
            const SpectateRequestUtils = require('../utils/spectateRequestUtils');
            await interaction.showModal(SpectateRequestUtils.createSpectateRequestModal(rest[0]));
        } else if (action === 'spectaterequest' && type === 'approve') {
            // Handle spectate request approval button
            const SpectateRequestUtils = require('../utils/spectateRequestUtils');
            await SpectateRequestUtils.handleSpectateRequestApproval(interaction, rest[0]);
        } else if (action === 'spectaterequest' && type === 'reject') {
            // Handle spectate request rejection button
            const SpectateRequestUtils = require('../utils/spectateRequestUtils');
            await SpectateRequestUtils.handleSpectateRequestRejection(interaction, rest[0]);
        } else if (action === 'staff' && type === 'onload') {
            // Handle staff onload button
            try {
                const StaffManager = require('../utils/staffManager');
                await StaffManager.handleStaffOnload(interaction, rest[0]);
            } catch (error) {
                console.error('Error handling staff onload:', error);

                // Handle unknown interaction error
                if (error.code === 10062) {
                    // Send a new message instead
                    await interaction.channel.send({
                        content: `There was an issue with the interaction. Please try the onload command again.`,
                        ephemeral: true
                    }).catch(console.error);
                } else {
                    // For other errors, try to reply if possible
                    try {
                        if (interaction.deferred) {
                            await interaction.followUp({
                                content: `Error: ${error.message}`,
                                ephemeral: true
                            });
                        } else {
                            await interaction.reply({
                                content: `Error: ${error.message}`,
                                ephemeral: true
                            });
                        }
                    } catch (replyError) {
                        console.error('Failed to reply with error message:', replyError);
                        await interaction.channel.send({
                            content: `Error: ${error.message}`,
                            ephemeral: true
                        }).catch(console.error);
                    }
                }
            }
        } else if (action === 'staff' && type === 'offload') {
            // Handle staff offload button
            try {
                const StaffManager = require('../utils/staffManager');
                await StaffManager.handleStaffOffload(interaction, rest[0]);
            } catch (error) {
                console.error('Error handling staff offload:', error);

                // Handle unknown interaction error
                if (error.code === 10062) {
                    // Send a new message instead
                    await interaction.channel.send({
                        content: `There was an issue with the interaction. Please try the offload command again.`,
                        ephemeral: true
                    }).catch(console.error);
                } else {
                    // For other errors, try to reply if possible
                    try {
                        if (interaction.deferred) {
                            await interaction.followUp({
                                content: `Error: ${error.message}`,
                                ephemeral: true
                            });
                        } else {
                            await interaction.reply({
                                content: `Error: ${error.message}`,
                                ephemeral: true
                            });
                        }
                    } catch (replyError) {
                        console.error('Failed to reply with error message:', replyError);
                        await interaction.channel.send({
                            content: `Error: ${error.message}`,
                            ephemeral: true
                        }).catch(console.error);
                    }
                }
            }
        }
        // Handle pagination buttons for stats embeds
        else if (interaction.customId.startsWith('stats_prev_') || interaction.customId.startsWith('stats_next_') || interaction.customId.startsWith('stats_page_')) {
            await interaction.deferUpdate();

            const parts = interaction.customId.split('_');
            const action = parts[1]; // prev, next, or page
            const currentPage = parseInt(parts[2]);

            // Get the stats embed updater
            const StatsEmbedUpdater = require('../utils/statsEmbedUpdater');
            const statsUpdater = client.statsEmbedUpdater;

            if (!statsUpdater) {
                return interaction.followUp({ content: 'Stats system not initialized.', ephemeral: true });
            }

            // Generate all pages
            const embedsData = await statsUpdater.generateStatsEmbeds();

            let newPage = currentPage;
            if (action === 'prev' && currentPage > 0) {
                newPage = currentPage - 1;
            } else if (action === 'next' && currentPage < embedsData.length - 1) {
                newPage = currentPage + 1;
            }

            // Update the message with the new page
            if (newPage < embedsData.length) {
                const newEmbedData = embedsData[newPage];
                await interaction.editReply({
                    embeds: [newEmbedData.embed],
                    components: newEmbedData.components || []
                });
            }
        }
        // Button handlers for /requests confirm/deny/review
        else if (action === 'confirm' || action === 'deny' || action === 'review') {
            const [_, actionType, requestAction, targetId, serverId, bmId, discordId] = interaction.customId.split('_');

            await interaction.deferReply({ ephemeral: true });

            // Fetch the original embed and thread
            const message = interaction.message;
            const embed = EmbedBuilder.from(message.embeds[0]);
            const thread = message.thread;

            // Update the embed status
            const statusFieldIndex = embed.data.fields.findIndex(field => field.name === 'Status');
            let newStatus = 'Pending'; // Default value

            if (actionType === 'confirm') {
                newStatus = 'Approved';
            } else if (actionType === 'deny') {
                newStatus = 'Denied';
            } else if (actionType === 'review') {
                newStatus = 'Under Review';
            }

            if (statusFieldIndex !== -1) {
                embed.data.fields[statusFieldIndex].value = newStatus;
            }

            // For review action, add or update the review field
            if (actionType === 'review') {
                const reviewFieldIndex = embed.data.fields.findIndex(field => field.name === 'Review Status');

                if (reviewFieldIndex !== -1) {
                    // Update existing review field
                    embed.data.fields[reviewFieldIndex].value = `<@${interaction.user.id}> is reviewing the request.`;
                } else {
                    // Add the review field
                    embed.addFields({
                        name: 'Review Status',
                        value: `<@${interaction.user.id}> is reviewing the request.`,
                        inline: false
                    });
                }
            } else {
                // For approve/deny actions, add reviewer info
                const reviewerFieldIndex = embed.data.fields.findIndex(field => field.name === 'Reviewed By');

                if (reviewerFieldIndex !== -1) {
                    // Update existing reviewer field
                    embed.data.fields[reviewerFieldIndex].value = `<@${interaction.user.id}>`;

                    // Update timestamp field if it exists
                    const timestampFieldIndex = embed.data.fields.findIndex(field => field.name === 'Review Timestamp');
                    if (timestampFieldIndex !== -1) {
                        embed.data.fields[timestampFieldIndex].value = new Date().toISOString();
                    }
                } else {
                    // Add the reviewer fields
                    embed.addFields(
                        { name: 'Reviewed By', value: `<@${interaction.user.id}>`, inline: true },
                        { name: 'Review Timestamp', value: new Date().toISOString(), inline: true }
                    );
                }
            }

            // Handle buttons based on action type
            const buttonRow = ActionRowBuilder.from(message.components[0]);

            if (actionType === 'confirm' || actionType === 'deny') {
                // Disable all buttons for confirm/deny actions
                const disabledButtons = buttonRow.components.map(button => {
                    return ButtonBuilder.from(button).setDisabled(true);
                });
                buttonRow.setComponents(disabledButtons);
            } else if (actionType === 'review') {
                // For review action, only disable the review button
                const updatedButtons = buttonRow.components.map(button => {
                    const btn = ButtonBuilder.from(button);
                    if (btn.data.custom_id.startsWith('review_')) {
                        btn.setDisabled(true);
                    }
                    return btn;
                });
                buttonRow.setComponents(updatedButtons);
            }

            // Default response message
            let responseMessage = `Request ${newStatus.toLowerCase()} by <@${interaction.user.id}>.`;

            if (actionType === 'confirm') {
                try {
                    // Initialize BanRequestUtils
                    const BanRequestUtils = require('../utils/banRequestUtils');
                    const banRequestUtils = new BanRequestUtils(client);

                    if (requestAction === 'ban') {
                        if (bmId === 'none') throw new Error('No BattleMetrics ID found for this user.');
                        const evidence = embed.data.fields.find(field => field.name === 'Evidence')?.value || '';
                        const reason = embed.data.fields.find(field => field.name === 'Reason')?.value || 'No reason provided';
                        const evidenceArray = evidence !== 'None provided' ? evidence.split(',').map(url => url.trim()) : [];

                        const banResult = await banRequestUtils.createBattleMetricsBan(bmId, reason, evidenceArray);
                        if (!banResult.success) {
                            throw new Error(`Failed to ban user: ${banResult.message}`);
                        }
                        responseMessage += `\nUser with BattleMetrics ID ${bmId} has been banned successfully.`;
                    } else if (requestAction === 'unban') {
                        if (bmId === 'none') throw new Error('No BattleMetrics ID found for this user.');

                        const unbanResult = await banRequestUtils.unbanPlayer(bmId);
                        if (!unbanResult.success) {
                            throw new Error(`Failed to unban user: ${unbanResult.message}`);
                        }
                        responseMessage += `\nUser with BattleMetrics ID ${bmId} has been unbanned successfully.`;
                    } else if (requestAction === 'spectate') {
                        // Spectate action (implementation depends on your game setup)
                        // For now, just log the action
                        console.log(`Spectate request confirmed for Steam ID ${targetId} on server ${serverId}`);
                        responseMessage += `\nSpectate request for Steam ID ${targetId} has been approved. Please proceed with spectating.`;
                    } else if (requestAction === 'blacklist') {
                        if (discordId === 'none') throw new Error('No Discord ID provided for blacklist.');
                        const guild = interaction.guild;
                        const member = await guild.members.fetch(discordId);
                        await member.ban({ reason: embed.data.fields.find(field => field.name === 'Reason')?.value || 'No reason provided' });
                        responseMessage += `\nUser <@${discordId}> has been banned from the Discord server.`;
                    }
                } catch (error) {
                    console.error(`Error processing ${requestAction} confirmation:`, error);
                    embed.data.fields[statusFieldIndex].value = 'Failed';
                    embed.addFields({ name: 'Error', value: error.message, inline: false });
                    responseMessage += `\nError: ${error.message}`;
                }
            }

            // Update the embed and notify the thread
            await message.edit({ embeds: [embed], components: [buttonRow] });
            if (thread) {
                await thread.send(responseMessage);
                if (actionType === 'confirm' || actionType === 'deny') {
                    // Only lock and archive threads for confirm/deny actions
                    await thread.setLocked(true);
                    await thread.setArchived(true);
                }
            }

            // Customize response based on action type
            if (actionType === 'review') {
                await interaction.editReply(`Request marked as under review successfully. You can still approve or deny it later.`);
            } else {
                await interaction.editReply(`Request ${newStatus.toLowerCase()} successfully.`);
            }
        }
    }

    // Handle select menu interactions
    if (interaction.isStringSelectMenu()) {
        // Handle server selection for /requests command
        if (interaction.customId.startsWith('request-server_')) {
            const [_, __, selectedAction, userId] = interaction.customId.split('_');
            const selectedServer = interaction.values[0];

            // Validate user ID to ensure only the original user can interact
            if (userId !== interaction.user.id) {
                return interaction.reply({ content: 'You are not authorized to make this selection.', ephemeral: true });
            }

            // Create the modal
            const modal = new ModalBuilder()
                .setCustomId(`request-modal_${selectedAction}_${selectedServer}`)
                .setTitle(`${selectedAction.charAt(0).toUpperCase() + selectedAction.slice(1)} Request`);

            const steamIdInput = new TextInputBuilder()
                .setCustomId('steam_id')
                .setLabel('Steam ID')
                .setStyle(TextInputStyle.Short)
                .setRequired(selectedAction !== 'blacklist');

            const evidenceInput = new TextInputBuilder()
                .setCustomId('evidence')
                .setLabel('Evidence (URLs, comma-separated)')
                .setStyle(TextInputStyle.Paragraph)
                .setRequired(false);

            const reasonInput = new TextInputBuilder()
                .setCustomId('reason')
                .setLabel('Reason')
                .setStyle(TextInputStyle.Paragraph)
                .setRequired(selectedAction === 'ban' || selectedAction === 'blacklist');

            const steamIdRow = new ActionRowBuilder().addComponents(steamIdInput);
            const evidenceRow = new ActionRowBuilder().addComponents(evidenceInput);
            const reasonRow = new ActionRowBuilder().addComponents(reasonInput);

            modal.addComponents(steamIdRow, evidenceRow, reasonRow);

            await interaction.showModal(modal);
        }
        // Handle permission level selection for onload
        else if (interaction.customId.startsWith('permission-level-select_')) {
            const [_, __, ___, userId, staffId] = interaction.customId.split('_');
            const StaffManager = require('../utils/staffManager');
            await StaffManager.handlePermissionLevelSelection(interaction, userId, staffId);
        }
        // Handle server selection for onload with permission level
        else if (interaction.customId.startsWith('server-select-onload_')) {
            const [_, __, ___, userId, staffId, permissionLevel] = interaction.customId.split('_');
            const StaffManager = require('../utils/staffManager');
            await StaffManager.handleServerSelectionOnload(interaction, userId, staffId, permissionLevel);
        }
        // Existing select menu handlers
        else if (interaction.customId.startsWith('server-select_')) {
            const userId = interaction.customId.replace('server-select_', '');
            const selectedServers = interaction.values;

            // Store the selected servers for the user
            client.userSelection[userId] = { servers: selectedServers };

            await interaction.update({
                content: `Selected servers: ${selectedServers.join(', ')}`,
                components: []
            });
        }
    }

    // Handle modal submissions
    if (interaction.isModalSubmit()) {
        if (interaction.customId.startsWith('banrequest-modal_')) {
            const steamId = interaction.customId.replace('banrequestmodal_', '');
            const BanRequestUtils = require('../utils/banRequestUtils');

            await BanRequestUtils.submitBanRequest(interaction, steamId);
        } else if (interaction.customId.startsWith('spectaterequest_modal_')) {
            const steamId = interaction.customId.replace('spectaterequest_modal_', '');
            const SpectateRequestUtils = require('../utils/spectateRequestUtils');

            await SpectateRequestUtils.submitSpectateRequest(interaction, steamId);
        } else if (interaction.customId.startsWith('request_modal_')) {
            await interaction.deferReply({ ephemeral: true });

            const [_, action, serverId] = interaction.customId.split('_');
            const steamId = interaction.fields.getTextInputValue('steam_id') || 'N/A';
            const evidence = interaction.fields.getTextInputValue('evidence') || '';
            const reason = interaction.fields.getTextInputValue('reason') || 'No reason provided';

            // Validate Steam ID for BattleMetrics actions
            if (action !== 'blacklist' && steamId !== 'N/A' && !steamId.match(/^[0-9]{17}$/)) {
                return interaction.editReply('Invalid Steam ID format. Please provide a valid Steam ID.');
            }

            // Check if the user has a database entry
            let userEntry = null;
            let targetUserId = null;
            if (action === 'blacklist') {
                // For blacklist, steamId is actually a Discord ID
                const discordIdMatch = steamId.match(/^<@!?(\d+)>$/) || [, steamId];
                targetUserId = discordIdMatch[1];
                userEntry = await Users.findOne({ discord_id: targetUserId });
            } else {
                userEntry = await Users.findOne({ steam_id: steamId });
            }

            // Fetch BattleMetrics ID and region if applicable
            let bmId = null;
            let regionInfo = { region: 'unknown', flag: null };
            if (action !== 'blacklist' && steamId !== 'N/A') {
                try {
                    console.log(`Searching BattleMetrics for Steam ID: ${steamId}`);
                    const response = await BattleMetricsClient.searchPlayerBySteamId(steamId);
                    console.log('BattleMetrics search response:', JSON.stringify(response, null, 2));

                    if (response.status === 200 && response.dict.data && response.dict.data.length > 0) {
                        bmId = response.dict.data[0].id;
                        console.log(`Found BattleMetrics ID: ${bmId}`);

                        const regionResponse = await BattleMetricsClient.getPlayerRegionInfo(bmId);
                        console.log('BattleMetrics region response:', JSON.stringify(regionResponse, null, 2));

                        if (regionResponse.status === 200 && regionResponse.regionInfo) {
                            regionInfo = regionResponse.regionInfo;
                        } else {
                            console.warn(`Failed to get region info for BM ID ${bmId}: ${regionResponse.message}`);
                        }
                    } else {
                        console.warn(`No BattleMetrics player found for Steam ID ${steamId}. Status: ${response.status}, Message: ${response.message}`);
                    }
                } catch (error) {
                    console.error(`Error fetching BattleMetrics data for Steam ID ${steamId}:`, error.message);
                    if (error.response) {
                        console.error('Response status:', error.response.status);
                        console.error('Response data:', error.response.data);
                    }
                }
            }

            // Fetch servers to look up server name
            let servers = [];
            try {
                servers = await BattleMetricsClient.getOrganizationServers();
            } catch (error) {
                console.error('Failed to fetch servers for embed:', error);
                servers = [
                    { id: '1666', name: 'AU Mrk' },
                    { id: '5795', name: 'EU Mrk' },
                    { id: '5760', name: 'US Mrk' }
                ]; // Fallback to known servers
            }

            if (!servers || servers.length === 0) {
                console.error('No servers available after fetching for embed. Using fallback servers.');
                servers = [
                    { id: '1666', name: 'AU Mrk' },
                    { id: '5795', name: 'EU Mrk' },
                    { id: '5760', name: 'US Mrk' }
                ];
            }

            // Create the embed with request details
            const requestEmbed = new EmbedBuilder()
                .setTitle(`${action.charAt(0).toUpperCase() + action.slice(1)} Request`)
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setTimestamp()
                .addFields(
                    { name: 'Requested By', value: `<@${interaction.user.id}>`, inline: true },
                    { name: 'Action', value: action, inline: true },
                    { name: 'Status', value: 'Pending', inline: true }
                );

            if (action !== 'blacklist') {
                requestEmbed.addFields(
                    { name: 'Steam ID', value: steamId, inline: true },
                    { name: 'BattleMetrics ID', value: bmId || 'Not found in BattleMetrics', inline: true },
                    { name: 'Region', value: regionInfo.region !== 'unknown' ? regionInfo.region.toUpperCase() : 'Unknown', inline: true }
                );
                if (regionInfo.flag) {
                    requestEmbed.addFields({ name: 'Country', value: regionInfo.flag, inline: true });
                }
                if (serverId !== 'none') {
                    const serverName = servers.find(s => s.id === serverId)?.name || 'Unknown Server';
                    requestEmbed.addFields({ name: 'Server', value: serverName, inline: true });
                }
            } else {
                requestEmbed.addFields({ name: 'Discord ID', value: steamId, inline: true });
            }

            requestEmbed.addFields(
                { name: 'Evidence', value: evidence || 'None provided', inline: false },
                { name: 'Reason', value: reason, inline: false }
            );

            // Add user model data if found
            if (userEntry) {
                requestEmbed.addFields(
                    { name: 'User in Database', value: 'Yes', inline: true },
                    { name: 'Discord Name', value: userEntry.discord_name || 'N/A', inline: true },
                    { name: 'Steam Name', value: userEntry.steam_name || 'N/A', inline: true },
                    { name: 'Last Login', value: userEntry.lastLogin || 'N/A', inline: true },
                    { name: 'User IP', value: userEntry.user_ip || 'N/A', inline: true },
                    { name: 'Staff Status', value: userEntry.staff ? 'Yes' : 'No', inline: true }
                );
            } else {
                requestEmbed.addFields({ name: 'User in Database', value: 'No', inline: true });
            }

            // Add Approve, Deny, and Review buttons
            const approveButton = new ButtonBuilder()
                .setCustomId(`confirm_${action}_${steamId}_${serverId}_${bmId || 'none'}_${targetUserId || 'none'}`)
                .setLabel('Approve')
                .setStyle(ButtonStyle.Success);

            const denyButton = new ButtonBuilder()
                .setCustomId(`deny_${action}_${steamId}_${serverId}_${bmId || 'none'}_${targetUserId || 'none'}`)
                .setLabel('Deny')
                .setStyle(ButtonStyle.Danger);

            const reviewButton = new ButtonBuilder()
                .setCustomId(`review_${action}_${steamId}_${serverId}_${bmId || 'none'}_${targetUserId || 'none'}`)
                .setLabel('Review')
                .setStyle(ButtonStyle.Primary);

            const buttonRow = new ActionRowBuilder().addComponents(approveButton, denyButton, reviewButton);

            // Send the embed and create a thread
            const message = await interaction.channel.send({ embeds: [requestEmbed], components: [buttonRow] });
            const thread = await message.startThread({
                name: `${action.charAt(0).toUpperCase() + action.slice(1)} Request - ${steamId}`,
                autoArchiveDuration: 1440, // 24 hours
                type: ChannelType.PublicThread
            });

            // Fetch user information using the provided Steam ID
            // Use the actual Steam ID from the form input instead of a hardcoded value
            const user = {
                steam_id: action !== 'blacklist' ? steamId : '76561990923292319',
                discord_pfp: interaction.user.avatarURL() || ''
            };
            const steamID = user.steam_id;

            console.log(`Using Steam ID for info check: ${steamID}`);

            if (!steamID || steamID === 'N/A') {
                console.log("User does not have a valid Steam ID for info check.");
                await thread.send("Could not perform info check: No valid Steam ID provided.");
                await interaction.editReply('Request submitted successfully! A thread has been created for discussion.');
                return;
            }

            let steamData = { playtime: "N/A", banStatus: "N/A" };
            let battleMetricsData = { bans: "No Bans Found" };
            let rustStats = { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
            let banData = { banStatus: "N/A", nexusProfile: "N/A" };

            try {
                steamData = await getSteamData(steamID);
            } catch (error) {
                console.error("Failed to fetch Steam data:", error);
            }

            try {
                rustStats = await getRustStats(steamID);
            } catch (error) {
                console.error("Failed to get Rust Stats", error);
            }

            try {
                banData = await checkGameBan(steamID);
            } catch (error) {
                console.error("Failed to fetch Ban data:", error);
            }

            try {
                battleMetricsData = await getBattleMetricsData(steamID);
            } catch (error) {
                console.error("Failed to fetch BattleMetrics data:", error);
            }

            // Process banned servers data for logging
            if (battleMetricsData.bannedServers && battleMetricsData.bannedServers.length > 0) {
                console.log(`Player has ${battleMetricsData.bannedServers.length} banned servers`);
            } else {
                console.log(`Player has no banned servers`);
            }

            let bans = await GetBans(steamID);
            let hoursAndServers = await GetHoursandServerInfo(steamID);
            let kdF7 = await GetKDandF7(steamID);

            const playerEmbed = new EmbedBuilder()
                .setTitle("Player Information")
                .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                .setThumbnail(user.discord_pfp || "")
                .addFields(
                    { name: "Steam Username", value: userEntry?.steam_name || "Unknown", inline: true },
                    { name: "Steam Profile", value: `[Click Here](https://steamcommunity.com/profiles/${steamID})`, inline: true },
                    { name: "Total Hours", value: steamData.playtime, inline: true },
                    { name: "Total Servers Played", value: battleMetricsData.serversPlayed, inline: true },
                    { name: "Account Age", value: battleMetricsData.accountAge, inline: true },
                    { name: "Kill/Death Ratio", value: `\`\`\`Kills: ${rustStats.kills} | Deaths: ${rustStats.deaths} | K/D: ${rustStats.kdRatio}\`\`\``, inline: false },
                    { name: "Steam Bans", value: steamData.banStatus, inline: true },
                    { name: "Game Bans", value: `${banData.banStatus} | [Check Profile](${banData.nexusProfile})`, inline: true },
                    { name: "BattleMetrics Links", value: `[Public Profile](${battleMetricsData.profileURL}) | [RCON Profile](${battleMetricsData.rconURL})`, inline: false },
                    { name: "Organization Hours", value: `${hoursAndServers.orgHours.toFixed(1)}` || "0", inline: true },
                    { name: "Aimtrain Hours", value: `${hoursAndServers.aimtrainHours.toFixed(1)}` || "0", inline: true },
                    { name: "Total Hours in Rust", value: `${hoursAndServers.allHours.toFixed(1)}` || "0", inline: true },
                    { name: "K/D Over 14 Days", value: kdF7.KDDay || `N/A`, inline: true },
                    { name: "F7 Reports Total", value: kdF7.F7Total || `N/A`, inline: true },
                    { name: "F7 Reports 24 Hours", value: kdF7.F7Day || `N/A`, inline: true }
                )
                .setTimestamp();

            if (bans.bans && bans.bans !== "No bans on record.") {
                playerEmbed.addFields({ name: "Recorded Bans", value: `\`\`\`${bans.bans}\`\`\``, inline: false });
            }

            if (bans.bannedServers && bans.bans !== "No banned servers found.") {
                const bannedEmbed = new EmbedBuilder()
                    .setTitle(`*Banned Servers*`)
                    .setDescription(`\`\`\`${bans.bannedServers}\`\`\``)
                    .setColor(client.config.BotSettings.EmbedColors || '#CCCCCC')
                    .setTimestamp();

                await thread.send({ embeds: [playerEmbed, bannedEmbed] });
            } else {
                await thread.send({ embeds: [playerEmbed] });
            }

            await interaction.editReply('Request submitted successfully! A thread has been created for discussion.');
        }
    }
};