/**
 * Handle permission-related interactions (select menus, buttons)
 */
module.exports = async (client, interaction) => {
    if (!interaction.isStringSelectMenu() && !interaction.isButton()) return;

    const customId = interaction.customId;

    try {
        // Handle permission selection for onboarding
        if (customId.startsWith('permission_select_onboard_')) {
            const parts = customId.split('_');
            const targetUserId = parts[3];
            const requesterId = parts[4];
            
            await client.permissionHandler.handleOnboardPermissionSelect(interaction, targetUserId, requesterId);
            return;
        }

        // Handle permission selection for modification
        if (customId.startsWith('permission_select_modify_')) {
            const parts = customId.split('_');
            const targetUserId = parts[3];
            const requesterId = parts[4];
            
            await client.permissionHandler.handleModifyPermissionSelect(interaction, targetUserId, requesterId);
            return;
        }

        // Handle server selection for onboarding
        if (customId.startsWith('server_select_onboard_')) {
            const parts = customId.split('_');
            const targetUserId = parts[3];
            const requesterId = parts[4];
            const permissionLevelName = parts[5];
            
            await client.permissionHandler.handleServerSelection(interaction, targetUserId, requesterId, permissionLevelName, 'onboard');
            return;
        }

        // Handle server selection for modification
        if (customId.startsWith('server_select_modify_')) {
            const parts = customId.split('_');
            const targetUserId = parts[3];
            const requesterId = parts[4];
            const permissionLevelName = parts[5];
            
            await client.permissionHandler.handleServerSelection(interaction, targetUserId, requesterId, permissionLevelName, 'modify');
            return;
        }

        // Handle offboarding confirmation
        if (customId.startsWith('confirm_offboard_')) {
            const parts = customId.split('_');
            const targetUserId = parts[2];
            const requesterId = parts[3];
            
            await client.permissionHandler.handleOffboardConfirm(interaction, targetUserId, requesterId);
            return;
        }

        // Handle offboarding cancellation
        if (customId.startsWith('cancel_offboard_')) {
            const parts = customId.split('_');
            const targetUserId = parts[2];
            const requesterId = parts[3];
            
            // Verify the interaction is from the correct user
            if (interaction.user.id !== requesterId) {
                return await interaction.reply({
                    content: '❌ You are not authorized to use this interaction.',
                    ephemeral: true
                });
            }

            await interaction.update({
                content: '❌ Offboarding cancelled.',
                embeds: [],
                components: []
            });
            return;
        }

    } catch (error) {
        console.error('Error handling permission interaction:', error);
        
        try {
            if (interaction.deferred || interaction.replied) {
                await interaction.editReply({
                    content: '❌ An error occurred while processing this interaction.',
                    components: []
                });
            } else {
                await interaction.reply({
                    content: '❌ An error occurred while processing this interaction.',
                    ephemeral: true
                });
            }
        } catch (replyError) {
            console.error('Error sending error reply:', replyError);
        }
    }
};
