# Staff Management Features

This document outlines the new staff management features including the updating stats embed and enhanced onboarding/offboarding system with permission levels.

## Features Overview

### 1. **Updating Stats Embed**
- Real-time staff statistics display in a designated channel
- Automatically updates every 5 minutes
- Shows comprehensive team performance metrics
- Includes top performers and recent activity

### 2. **RCON-Based Permission System**
- Multiple permission levels (Admin, Moderator, Helper, Trial)
- Select menu-based onboarding/offboarding
- Server-specific RCON command execution
- Customizable server commands for each permission level
- Steam ID-based permission assignment

## Stats Embed System

### Features
- **Team Overview**: Active staff count, tracked members
- **Activity Metrics**: Tickets, messages, bans, voice/in-game time
- **Top Performers**: Weekly rankings with activity scores
- **Recent Activity**: Latest bans, active staff today, system status
- **Auto-Updates**: Refreshes every 5 minutes automatically

### Setup Commands

#### `/statsembed setup <channel>`
Sets up the stats embed in a specific channel.
```
/statsembed setup channel:#staff-stats
```

#### `/statsembed update`
Forces an immediate update of the stats embed.

#### `/statsembed start/stop`
Controls automatic updates.

#### `/statsembed status`
Shows current status and configuration.

### Configuration
```yaml
StaffManagement:
  StatsEmbed:
    ChannelID: null # Set via /statsembed setup
    UpdateFrequency: 300000 # 5 minutes in milliseconds
```

## Permission System

### Permission Levels

#### Default Levels Created by `/managepermissions setup`:

1. **Administrator** (Priority: 100)
   - Full administrative access
   - All permissions enabled
   - Color: Red (#FF0000)

2. **Moderator** (Priority: 70)
   - Moderation permissions
   - Ban and ticket management
   - Color: Orange (#FF9800)

3. **Helper** (Priority: 30)
   - Basic staff permissions
   - Ticket support
   - Color: Green (#4CAF50)

4. **Trial Staff** (Priority: 10)
   - Limited permissions
   - Trial period access
   - Color: Blue (#2196F3)

### Permission Management Commands

#### `/managepermissions create <name> <displayname> <priority> [description]`
Creates a new permission level.
```
/managepermissions create senior "Senior Moderator" 80 "Experienced moderator with additional permissions"
```

#### `/managepermissions list`
Lists all configured permission levels.

#### `/managepermissions addrole <level> <role>`
Adds a Discord role to a permission level.
```
/managepermissions addrole moderator @Moderator
```

#### `/managepermissions addcommand <level> <title> <command>`
Adds a server command to a permission level.
```
/managepermissions addcommand moderator "Grant Moderator" "moderatorid {steam_id}"
```

#### `/managepermissions setup`
Creates default permission levels.

### Staff Permission Commands

#### `/staffpermissions onboard <staff> <steamid>`
Onboards a new staff member with permission selection.

**Process:**
1. Provide Steam ID for server permissions
2. Select permission level from dropdown
3. Choose servers to apply permissions
4. System executes RCON commands on selected servers
5. Creates staff member record

**Example:**
```
/staffpermissions onboard @NewStaff 76561198123456789
```

#### `/staffpermissions offboard <staff>`
Offboards a staff member with confirmation.

**Process:**
1. Confirmation dialog with warning
2. Executes RCON offload commands on all servers
3. Removes server permissions via RCON
4. Deactivates staff record

#### `/staffpermissions modify <staff>`
Modifies existing staff member permissions.

#### `/staffpermissions view <staff>`
Views current staff member permissions and roles.

### Select Menu System

The permission system uses interactive select menus for:

1. **Permission Level Selection**
   - Shows all available permission levels
   - Displays priority and description
   - Visual indicators (👑 for admin, 🛡️ for mod, 👤 for basic)

2. **Server Selection**
   - Multi-select for applying permissions to multiple servers
   - Shows which RCON commands will be executed
   - Displays command descriptions and expected results

3. **Confirmation System**
   - Button-based confirmations for destructive actions
   - Clear warnings about irreversible changes
   - User verification to prevent unauthorized actions

## Database Models

### Permission Level Schema
```javascript
{
  name: String,           // Internal name (lowercase)
  displayName: String,    // Display name
  priority: Number,       // Priority level (higher = more permissions)
  description: String,    // Description
  onloadCommands: [{      // RCON commands for onboarding
    title: String,
    command: String,      // Use {steam_id} placeholder
    description: String
  }],
  offloadCommands: [{     // RCON commands for offboarding
    title: String,
    command: String,      // Use {steam_id} placeholder
    description: String
  }],
  color: String          // Hex color for embeds
}
```

## Setup Instructions

### 1. Initial Setup

1. **Configure Permission Levels**:
   Edit the `config.yml` file to customize permission levels and RCON commands:
   ```yaml
   StaffManagement:
     PermissionLevels:
       admin:
         displayName: "Administrator"
         priority: 100
         onloadCommands:
           - title: "Grant Admin Access"
             command: "moderatorid {steam_id}"
   ```

2. **Setup Stats Embed**:
   ```
   /statsembed setup #staff-statistics
   ```

3. **Verify RCON Connections**:
   Ensure all servers in the config have working RCON connections for command execution.

### 2. Staff Member Management

1. **Configure BattleMetrics Emails** (for ban tracking):
   ```
   /managestaffemail set @StaffMember <EMAIL>
   ```

2. **Onboard New Staff**:
   ```
   /staffpermissions onboard @NewStaff 76561198123456789
   ```
   - Provide Steam ID for server permissions
   - Select permission level from menu
   - Choose servers to apply permissions
   - System executes RCON commands on selected servers

3. **Modify Existing Staff**:
   ```
   /staffpermissions modify @ExistingStaff
   ```

### 3. Monitoring and Maintenance

1. **Check System Status**:
   ```
   /syncstaffstats status
   /statsembed status
   ```

2. **Manual Syncs**:
   ```
   /syncstaffstats tickets
   /statsembed update
   ```

3. **View Staff Information**:
   ```
   /staffpermissions view @StaffMember
   /staffactivity @StaffMember
   ```

## Integration with Existing Systems

### Cross-Bot Statistics
- Stats embed pulls data from shared statistics database
- Integrates with ban tracking and ticket claim systems
- Shows real-time activity from both Manager Bot and Staff Manager

### Server Integration
- Permission commands execute via RCON connections
- Supports multiple servers with different configurations
- Placeholder system for Steam ID insertion

### Discord Integration
- Role management with permission verification
- Interactive components (select menus, buttons)
- Webhook notifications for important actions

## Troubleshooting

### Common Issues

1. **Stats Embed Not Updating**:
   - Check `/statsembed status`
   - Verify channel permissions
   - Check database connectivity

2. **Permission Assignment Failing**:
   - Check RCON connections to servers
   - Verify Steam ID format
   - Validate RCON command syntax
   - Check server console for errors

3. **Select Menus Not Working**:
   - Check interaction handler registration
   - Verify permission level configuration
   - Check console for errors

### Debug Commands
- `/syncstaffstats status` - Check tracking systems
- `/statsembed status` - Check embed updater
- `/managepermissions list` - Verify permission levels

## Security Considerations

- Permission commands require Administrator permissions
- User verification for all destructive actions
- Audit logging for RCON command execution
- Steam ID validation for server commands
- RCON connection security and error handling

This system provides comprehensive staff management with visual feedback, interactive controls, and robust RCON-based server permission handling.
