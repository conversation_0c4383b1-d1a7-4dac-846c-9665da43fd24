/**
 * Memory Monitor
 * 
 * A utility to monitor memory usage and log warnings when thresholds are exceeded.
 */

const fs = require('fs');

class MemoryMonitor {
  static interval = null;
  static lastLogTime = 0;
  static logCooldown = 10 * 60 * 1000; // 10 minutes
  static warningThreshold = 80; // 80% of heap
  static criticalThreshold = 90; // 90% of heap

  /**
   * Start monitoring memory usage
   * @param {number} checkInterval - Interval in ms to check memory usage
   */
  static startMonitoring(checkInterval = 180000) {
    if (this.interval) {
      return; // Already monitoring
    }

    console.log(`[MEMORY] Starting memory monitor with interval of ${checkInterval}ms`);
    
    // Initial check
    this.checkMemory();
    
    // Set up regular checks
    this.interval = setInterval(() => {
      this.checkMemory();
    }, checkInterval);
  }

  /**
   * Stop monitoring memory usage
   */
  static stopMonitoring() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      console.log('[MEMORY] Memory monitor stopped');
    }
  }

  /**
   * Check current memory usage
   */
  static checkMemory() {
    try {
      const memoryUsage = process.memoryUsage();
      const heapUsed = memoryUsage.heapUsed;
      const heapTotal = memoryUsage.heapTotal;
      const rss = memoryUsage.rss;
      
      // Calculate usage percentage
      const heapUsagePercent = (heapUsed / heapTotal) * 100;
      
      const now = Date.now();
      
      // Log memory usage periodically
      if (now - this.lastLogTime > this.logCooldown) {
        this.lastLogTime = now;
        
        // Log current memory usage
        console.log(`[MEMORY] Current usage: ${(heapUsed / 1024 / 1024).toFixed(2)}MB / ${(heapTotal / 1024 / 1024).toFixed(2)}MB (${heapUsagePercent.toFixed(1)}%)`);
        
        // Log to file
        const logMsg = `\n\n[${new Date().toLocaleString()}] [MEMORY] Current usage: ${(heapUsed / 1024 / 1024).toFixed(2)}MB / ${(heapTotal / 1024 / 1024).toFixed(2)}MB (${heapUsagePercent.toFixed(1)}%)`;
        fs.appendFile("./logs.manager", logMsg, (e) => {
          if (e) console.log(e);
        });
      }
      
      // Check thresholds and take action if needed
      if (heapUsagePercent > this.criticalThreshold) {
        console.log(`[MEMORY] CRITICAL: Memory usage at ${heapUsagePercent.toFixed(1)}% of heap`);
        
        // Run garbage collection if available
        if (global.gc) {
          console.log('[MEMORY] Running garbage collection due to critical memory usage');
          global.gc();
          
          // Log memory usage after garbage collection
          const afterGC = process.memoryUsage();
          const freedMemory = heapUsed - afterGC.heapUsed;
          console.log(`[MEMORY] Garbage collection freed ${(freedMemory / 1024 / 1024).toFixed(2)}MB`);
        }
      } else if (heapUsagePercent > this.warningThreshold) {
        console.log(`[MEMORY] WARNING: Memory usage at ${heapUsagePercent.toFixed(1)}% of heap`);
        
        // Run garbage collection if available
        if (global.gc) {
          console.log('[MEMORY] Running garbage collection due to high memory usage');
          global.gc();
        }
      }
    } catch (error) {
      console.error(`[MEMORY] Error checking memory: ${error.message}`);
    }
  }
}

module.exports = MemoryMonitor;
