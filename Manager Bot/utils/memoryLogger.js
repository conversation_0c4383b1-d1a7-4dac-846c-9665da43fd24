/**
 * Memory Logger
 * 
 * A simple utility to log memory usage of the bot.
 */

const fs = require('fs');
const path = require('path');

class MemoryLogger {
  constructor(options = {}) {
    this.options = {
      logInterval: options.logInterval || 5 * 60 * 1000, // 5 minutes
      logToConsole: options.logToConsole !== undefined ? options.logToConsole : true,
      logToFile: options.logToFile !== undefined ? options.logToFile : true,
      logFilePath: options.logFilePath || './logs/memory-usage.log',
      warningThreshold: options.warningThreshold || 80, // 80% of heap
      criticalThreshold: options.criticalThreshold || 90 // 90% of heap
    };

    this.interval = null;
    this.lastLogTime = 0;

    // Create logs directory if it doesn't exist
    if (this.options.logToFile) {
      const logsDir = path.dirname(this.options.logFilePath);
      if (!fs.existsSync(logsDir)) {
        try {
          fs.mkdirSync(logsDir, { recursive: true });
        } catch (error) {
          console.error(`Failed to create logs directory: ${error.message}`);
        }
      }
    }
  }

  /**
   * Start logging memory usage
   */
  start() {
    if (this.interval) {
      return; // Already running
    }

    console.log(`[MEMORY] Starting memory logger with interval of ${this.options.logInterval / 1000}s`);
    
    // Initial log
    this.logMemoryUsage();
    
    // Set up regular logging
    this.interval = setInterval(() => {
      this.logMemoryUsage();
    }, this.options.logInterval);
  }

  /**
   * Stop logging memory usage
   */
  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      console.log('[MEMORY] Memory logger stopped');
    }
  }

  /**
   * Log current memory usage
   */
  logMemoryUsage() {
    try {
      const memoryUsage = process.memoryUsage();
      
      // Format memory values
      const formatted = {
        rss: this.formatBytes(memoryUsage.rss),
        heapTotal: this.formatBytes(memoryUsage.heapTotal),
        heapUsed: this.formatBytes(memoryUsage.heapUsed),
        external: this.formatBytes(memoryUsage.external),
        arrayBuffers: this.formatBytes(memoryUsage.arrayBuffers || 0)
      };
      
      // Calculate heap usage percentage
      const heapUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
      
      // Create log message
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] Memory Usage: RSS: ${formatted.rss}, Heap: ${formatted.heapUsed}/${formatted.heapTotal} (${heapUsagePercent.toFixed(1)}%), External: ${formatted.external}, ArrayBuffers: ${formatted.arrayBuffers}`;
      
      // Log to console if enabled
      if (this.options.logToConsole) {
        if (heapUsagePercent > this.options.criticalThreshold) {
          console.log('\x1b[31m%s\x1b[0m', `[MEMORY] CRITICAL: ${logMessage}`); // Red
        } else if (heapUsagePercent > this.options.warningThreshold) {
          console.log('\x1b[33m%s\x1b[0m', `[MEMORY] WARNING: ${logMessage}`); // Yellow
        } else {
          console.log('\x1b[36m%s\x1b[0m', `[MEMORY] ${logMessage}`); // Cyan
        }
      }
      
      // Log to file if enabled
      if (this.options.logToFile) {
        fs.appendFile(this.options.logFilePath, logMessage + '\n', (err) => {
          if (err) {
            console.error(`Failed to write to memory log file: ${err.message}`);
          }
        });
      }
      
      // Also log to main log file
      fs.appendFile("./logs.manager", `\n[${new Date().toLocaleString()}] [MEMORY] Usage: ${formatted.heapUsed}/${formatted.heapTotal} (${heapUsagePercent.toFixed(1)}%)`, (err) => {
        if (err) {
          console.error(`Failed to write to main log file: ${err.message}`);
        }
      });
      
      // Return the memory usage data
      return {
        timestamp,
        raw: memoryUsage,
        formatted,
        heapUsagePercent
      };
    } catch (error) {
      console.error(`Error logging memory usage: ${error.message}`);
    }
  }

  /**
   * Format bytes to a human-readable string
   * @param {number} bytes - Bytes to format
   * @param {number} decimals - Number of decimal places
   * @returns {string} Formatted string
   */
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }
}

module.exports = MemoryLogger;
