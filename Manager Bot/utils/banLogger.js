const { EmbedBuilder, WebhookClient } = require('discord.js');
const fs = require('fs');
const yaml = require('js-yaml');
// Use the config adapter for backward compatibility with the new config structure
const config = require('./configAdapter');
const BMRequests = require('./bmRequests');
const BanMonitorConfig = require('../models/banMonitorConfig');

/**
 * Logs ban details to the public Discord channel via webhook.
 */
async function logToPublicChannel(ban, steamID, serverName, timeUnbanned, userAvatar) {
    try {
        // Get ban monitor configuration
        const banConfig = await getBanMonitorConfig();
        if (!banConfig || !banConfig.publicWebhooks || banConfig.publicWebhooks.length === 0) {
            console.log('No public webhooks configured for ban notifications');
            return;
        }

        // Create embed for public channel
        const publicEmbed = new EmbedBuilder()
            .setTitle(serverName)
            .setColor(banConfig.embedColor || config.EmbedColors)
            .setThumbnail(userAvatar)
            .addFields(
                { name: 'Player Information', value: `${ban.meta.player} - [${steamID}](https://steamcommunity.com/profiles/${steamID})` },
                { name: 'Ban Information', value: `Unban Date: ${timeUnbanned}\n\n\`${ban.attributes.reason}\``, inline: false }
            )
            .setTimestamp()
            .setFooter({ text: `${banConfig.serverName || config.SERVER_NAME} Bans` });

        // Send to all public webhooks
        for (const webhookUrl of banConfig.publicWebhooks) {
            if (!webhookUrl) continue;

            try {
                const webhook = new WebhookClient({ url: webhookUrl });
                await webhook.send({ embeds: [publicEmbed] });
            } catch (error) {
                console.error(`Error sending to public webhook: ${error.message}`);
            }
        }
    } catch (error) {
        console.error(`Error in logToPublicChannel: ${error.message}`);
    }
}

/**
 * Logs ban details to the staff Discord channel via webhook.
 */
async function logToStaffChannel(ban, steamID, serverName, timeUnbanned, userAvatar, banNote) {
    try {
        // Get ban monitor configuration
        const banConfig = await getBanMonitorConfig();
        if (!banConfig || !banConfig.staffWebhooks || banConfig.staffWebhooks.length === 0) {
            console.log('No staff webhooks configured for ban notifications');
            return;
        }

        // Create embed for staff channel
        const staffEmbed = new EmbedBuilder()
            .setTitle(serverName)
            .setColor(banConfig.embedColor || config.EmbedColors)
            .setThumbnail(userAvatar)
            .addFields(
                { name: 'Player Information', value: `${ban.meta.player} - [${steamID}](https://steamcommunity.com/profiles/${steamID})` },
                { name: 'Ban Information', value: `Unban Date: ${timeUnbanned}\n\n\`${ban.attributes.reason}\``, inline: false },
                {
                    name: 'BattleMetrics Links',
                    value: `[Ban Link](https://www.battlemetrics.com/rcon/bans/edit/${ban.id})\n` +
                           `[Profile](https://www.battlemetrics.com/rcon/players/${ban.relationships.player.data.id})`,
                    inline: true
                },
                { name: 'Ban Note', value: banNote || 'No additional notes', inline: true }
            )
            .setTimestamp()
            .setFooter({ text: `${banConfig.serverName || config.SERVER_NAME} Bans` });

        // Send to all staff webhooks
        for (const webhookUrl of banConfig.staffWebhooks) {
            if (!webhookUrl) continue;

            try {
                const webhook = new WebhookClient({ url: webhookUrl });
                await webhook.send({ embeds: [staffEmbed] });
            } catch (error) {
                console.error(`Error sending to staff webhook: ${error.message}`);
            }
        }
    } catch (error) {
        console.error(`Error in logToStaffChannel: ${error.message}`);
    }
}

/**
 * Retrieves the server name for the given ban.
 */
async function getServerName(ban) {
    try {
        const banConfig = await getBanMonitorConfig();

        if (ban.relationships?.server?.data?.id) {
            const serverResp = await BMRequests.getRequest(
                `https://api.battlemetrics.com/servers/${ban.relationships.server.data.id}`,
                config.bm_api_token
            );

            if (serverResp.status === 200) {
                return JSON.parse(serverResp.text).data.attributes.name;
            }
        }

        return banConfig.serverName || config.SERVER_NAME || 'Server Bans';
    } catch (error) {
        console.error(`Error in getServerName: ${error.message}`);
        return config.SERVER_NAME || 'Server Bans';
    }
}

/**
 * Gets or creates the ban monitor configuration
 */
async function getBanMonitorConfig() {
    try {
        let banConfig = await BanMonitorConfig.findOne();

        if (!banConfig) {
            // Create default configuration
            banConfig = new BanMonitorConfig({
                enabled: true,
                banListId: '',
                staffWebhooks: [],
                publicWebhooks: [],
                embedColor: config.EmbedColors,
                serverName: config.SERVER_NAME
            });
            await banConfig.save();
        }

        return banConfig;
    } catch (error) {
        console.error(`Error getting ban monitor config: ${error.message}`);
        return null;
    }
}

module.exports = {
    logToPublicChannel,
    logToStaffChannel,
    getServerName,
    getBanMonitorConfig
};
