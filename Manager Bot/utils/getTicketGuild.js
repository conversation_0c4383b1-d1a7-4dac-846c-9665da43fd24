/**
 * Utility function to get the correct guild for a ticket
 * This handles the case where tickets are created in a different server
 */

/**
 * Get the guild object for a ticket
 * @param {Client} client - Discord.js client
 * @param {Object} ticket - Ticket database object
 * @returns {Guild} - Discord.js Guild object
 */
function getTicketGuild(client, ticket) {
    // Try to get the guild from the guildID
    let guild = client.guilds.cache.get(ticket.guildID);

    // If the guild is not found and we have a sourceGuildID, try that instead
    if (!guild && ticket.sourceGuildID) {
        guild = client.guilds.cache.get(ticket.sourceGuildID);
    }

    return guild;
}

/**
 * Get the guild to create a new ticket in
 * @param {Client} client - Discord.js client
 * @param {string} sourceGuildId - The guild ID where the ticket was initiated
 * @returns {Guild} - Discord.js Guild object
 */
function getTicketCreationGuild(client, sourceGuildId) {
    // If TicketServerID is configured, use that server for tickets
    if (client.config && client.config.TicketSettings && client.config.TicketSettings.TicketServerID) {
        const ticketGuild = client.guilds.cache.get(client.config.TicketSettings.TicketServerID);
        if (ticketGuild) {
            console.log(`Using dedicated ticket server: ${ticketGuild.name}`);
            return ticketGuild;
        } else {
            console.warn(`⚠️ Configured TicketServerID (${client.config.TicketSettings.TicketServerID}) is not valid or bot doesn't have access`);
        }
    }

    // Fallback to the source guild
    return client.guilds.cache.get(sourceGuildId);
}

/**
 * Get the channel object for a ticket
 * @param {Client} client - Discord.js client
 * @param {Object} ticket - Ticket database object
 * @returns {Channel} - Discord.js Channel object or null if not found
 */
function getTicketChannel(client, ticket) {
    // Try to get the guild from the guildID
    const guild = client.guilds.cache.get(ticket.guildID);

    if (!guild) {
        return null;
    }

    return guild.channels.cache.get(ticket.channelID);
}

module.exports = { getTicketGuild, getTicketChannel, getTicketCreationGuild };
