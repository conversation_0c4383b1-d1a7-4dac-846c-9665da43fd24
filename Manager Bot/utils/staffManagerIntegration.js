const SharedStaffStats = require('../models/sharedStaffStats');
const { WebhookClient, EmbedBuilder } = require('discord.js');

/**
 * Utility class for integrating Manager Bo<PERSON> with Staff Manager
 * Handles cross-bot communication and shared statistics
 */
class StaffManagerIntegration {
    constructor(client) {
        this.client = client;
    }

    /**
     * Check if a user is a staff member
     */
    async isStaffMember(userId) {
        try {
            const member = await this.client.guilds.cache.get(this.client.config.BotSettings.GuildID)?.members.fetch(userId);
            if (!member) return false;

            const staffRoleId = this.client.config.BotSettings.StaffRole;
            return member.roles.cache.has(staffRoleId);
        } catch (error) {
            console.error('Error checking staff member status:', error);
            return false;
        }
    }

    /**
     * Notify Staff Manager when a ticket is claimed
     */
    async notifyTicketClaim(staffUserId, ticketData) {
        try {
            // Check if user is staff
            if (!await this.isStaffMember(staffUserId)) {
                return false;
            }

            console.log(`Notifying Staff Manager of ticket claim by ${staffUserId}`);

            // Update shared statistics
            await SharedStaffStats.updateTicketStats(staffUserId);

            // Send webhook notification if configured
            await this.sendTicketClaimWebhook(staffUserId, ticketData);

            return true;
        } catch (error) {
            console.error('Error notifying ticket claim:', error);
            return false;
        }
    }

    /**
     * Notify Staff Manager when a staff member sends a message
     */
    async notifyMessageActivity(staffUserId, messageCount = 1, channelType = 'general') {
        try {
            // Check if user is staff
            if (!await this.isStaffMember(staffUserId)) {
                return false;
            }

            // Update shared statistics
            await SharedStaffStats.updateMessageStats(staffUserId, messageCount);

            return true;
        } catch (error) {
            console.error('Error notifying message activity:', error);
            return false;
        }
    }

    /**
     * Notify Staff Manager when a ban is issued
     */
    async notifyBanActivity(staffUserId, banData) {
        try {
            // Check if user is staff
            if (!await this.isStaffMember(staffUserId)) {
                return false;
            }

            console.log(`Notifying Staff Manager of ban activity by ${staffUserId}`);

            // Update shared statistics
            await SharedStaffStats.updateBanStats(staffUserId);

            // Send webhook notification if configured
            await this.sendBanActivityWebhook(staffUserId, banData);

            return true;
        } catch (error) {
            console.error('Error notifying ban activity:', error);
            return false;
        }
    }

    /**
     * Try to identify which staff member issued a ban based on various criteria
     */
    async identifyBanIssuer(banData) {
        try {
            const banNote = banData.attributes?.note || '';
            const banReason = banData.attributes?.reason || '';
            
            // Get all staff members
            const guild = this.client.guilds.cache.get(this.client.config.BotSettings.GuildID);
            if (!guild) return null;

            const staffRole = guild.roles.cache.get(this.client.config.BotSettings.StaffRole);
            if (!staffRole) return null;

            const staffMembers = staffRole.members;

            // Method 1: Look for Discord mentions in ban note
            const mentionMatch = banNote.match(/<@!?(\d+)>/);
            if (mentionMatch) {
                const mentionedUserId = mentionMatch[1];
                if (staffMembers.has(mentionedUserId)) {
                    return mentionedUserId;
                }
            }

            // Method 2: Look for staff member names in ban note or reason
            for (const [userId, member] of staffMembers) {
                const displayName = member.displayName.toLowerCase();
                const username = member.user.username.toLowerCase();
                
                if (banNote.toLowerCase().includes(displayName) || 
                    banNote.toLowerCase().includes(username) ||
                    banReason.toLowerCase().includes(displayName) ||
                    banReason.toLowerCase().includes(username)) {
                    return userId;
                }
            }

            // Method 3: Look for common staff signatures or patterns
            const commonPatterns = [
                /banned by (\w+)/i,
                /issued by (\w+)/i,
                /-(\w+)$/,
                /\[(\w+)\]/
            ];

            for (const pattern of commonPatterns) {
                const match = banNote.match(pattern) || banReason.match(pattern);
                if (match) {
                    const name = match[1].toLowerCase();
                    for (const [userId, member] of staffMembers) {
                        if (member.displayName.toLowerCase().includes(name) || 
                            member.user.username.toLowerCase().includes(name)) {
                            return userId;
                        }
                    }
                }
            }

            return null;
        } catch (error) {
            console.error('Error identifying ban issuer:', error);
            return null;
        }
    }

    /**
     * Send webhook notification for ticket claim
     */
    async sendTicketClaimWebhook(staffUserId, ticketData) {
        try {
            const webhookUrl = this.client.config.Webhooks?.TicketClaimWebhook;
            if (!webhookUrl) return;

            const embed = new EmbedBuilder()
                .setTitle('🎫 Ticket Claimed')
                .setColor('#4CAF50')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffUserId}>`, inline: true },
                    { name: 'Ticket Type', value: ticketData.ticketType || 'Unknown', inline: true },
                    { name: 'Channel', value: `<#${ticketData.channelID}>`, inline: true }
                ])
                .setTimestamp();

            const webhook = new WebhookClient({ url: webhookUrl });
            await webhook.send({ embeds: [embed] });
        } catch (error) {
            console.error('Error sending ticket claim webhook:', error);
        }
    }

    /**
     * Send webhook notification for ban activity
     */
    async sendBanActivityWebhook(staffUserId, banData) {
        try {
            const webhookUrl = this.client.config.Webhooks?.BanStaffWebhook;
            if (!webhookUrl) return;

            const embed = new EmbedBuilder()
                .setTitle('🔨 Staff Ban Detected')
                .setColor('#FF6B6B')
                .addFields([
                    { name: 'Staff Member', value: `<@${staffUserId}>`, inline: true },
                    { name: 'Player', value: banData.playerName || 'Unknown', inline: true },
                    { name: 'Server', value: banData.serverName || 'Unknown', inline: true },
                    { name: 'Reason', value: banData.reason || 'No reason provided', inline: false }
                ])
                .setTimestamp();

            const webhook = new WebhookClient({ url: webhookUrl });
            await webhook.send({ embeds: [embed] });
        } catch (error) {
            console.error('Error sending ban activity webhook:', error);
        }
    }

    /**
     * Get staff statistics from shared database
     */
    async getStaffStats(staffUserId) {
        try {
            const stats = await SharedStaffStats.findOne({ staffDiscordId: staffUserId });
            return stats || null;
        } catch (error) {
            console.error('Error getting staff stats:', error);
            return null;
        }
    }

    /**
     * Get all staff statistics
     */
    async getAllStaffStats() {
        try {
            const stats = await SharedStaffStats.find({}).sort({ 'tickets.totalClaimed': -1 });
            return stats;
        } catch (error) {
            console.error('Error getting all staff stats:', error);
            return [];
        }
    }

    /**
     * Reset weekly statistics for all staff
     */
    async resetWeeklyStats() {
        try {
            console.log('Resetting weekly staff statistics...');
            
            await SharedStaffStats.updateMany(
                {},
                {
                    $set: {
                        'tickets.weeklyClaimed': 0,
                        'messages.weekly': 0,
                        'bans.weekly': 0,
                        'voice.weeklyMinutes': 0,
                        'ingame.weeklyMinutes': 0,
                        weeklyResetDate: new Date()
                    }
                }
            );

            console.log('Weekly statistics reset completed');
            return true;
        } catch (error) {
            console.error('Error resetting weekly stats:', error);
            return false;
        }
    }

    /**
     * Initialize staff member in shared database
     */
    async initializeStaffMember(staffUserId) {
        try {
            await SharedStaffStats.findOneAndUpdate(
                { staffDiscordId: staffUserId },
                {
                    $setOnInsert: {
                        tickets: { totalClaimed: 0, weeklyClaimed: 0, monthlyClaimed: 0 },
                        messages: { total: 0, weekly: 0, monthly: 0 },
                        bans: { total: 0, weekly: 0, monthly: 0 },
                        voice: { totalMinutes: 0, weeklyMinutes: 0, monthlyMinutes: 0 },
                        ingame: { totalMinutes: 0, weeklyMinutes: 0, monthlyMinutes: 0 }
                    }
                },
                { upsert: true }
            );

            return true;
        } catch (error) {
            console.error('Error initializing staff member:', error);
            return false;
        }
    }

    /**
     * Check if staff member exists in shared database
     */
    async staffMemberExists(staffUserId) {
        try {
            const stats = await SharedStaffStats.findOne({ staffDiscordId: staffUserId });
            return !!stats;
        } catch (error) {
            console.error('Error checking staff member existence:', error);
            return false;
        }
    }
}

module.exports = StaffManagerIntegration;
