/**
 * System Monitor
 * 
 * A utility class to monitor system resources and log metrics.
 * This is a simplified version that only tracks basic memory usage.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class SystemMonitor {
  /**
   * Create a new system monitor
   * @param {Object} options - Configuration options
   * @param {number} options.checkInterval - Interval in ms to check system metrics (default: 60000)
   * @param {number} options.logInterval - Interval in ms to log system metrics (default: 300000)
   * @param {boolean} options.logToConsole - Whether to log metrics to console (default: true)
   * @param {string} options.logPath - Path to log file (default: './logs/system-metrics.log')
   * @param {Function} options.onWarning - Callback when warning thresholds are exceeded
   * @param {Function} options.onCritical - Callback when critical thresholds are exceeded
   */
  constructor(options = {}) {
    this.options = {
      checkInterval: options.checkInterval || 60000, // 1 minute
      logInterval: options.logInterval || 300000, // 5 minutes
      logToConsole: options.logToConsole !== undefined ? options.logToConsole : true,
      logPath: options.logPath || './logs/system-metrics.log',
      onWarning: options.onWarning || (() => {}),
      onCritical: options.onCritical || (() => {})
    };

    this.checkIntervalId = null;
    this.logIntervalId = null;
    this.lastLogTime = 0;
    this.metrics = {
      memory: {
        heapUsed: 0,
        heapTotal: 0,
        rss: 0,
        external: 0
      },
      system: {
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuUsage: 0,
        uptime: 0
      }
    };

    // Create logs directory if it doesn't exist
    const logsDir = path.dirname(this.options.logPath);
    if (!fs.existsSync(logsDir)) {
      try {
        fs.mkdirSync(logsDir, { recursive: true });
      } catch (error) {
        console.error(`Failed to create logs directory: ${error.message}`);
      }
    }
  }

  /**
   * Start monitoring
   */
  start() {
    if (this.checkIntervalId) {
      return; // Already running
    }

    // Initial check
    this.checkMetrics();

    // Set up regular checks
    this.checkIntervalId = setInterval(() => {
      this.checkMetrics();
    }, this.options.checkInterval);

    // Set up regular logging
    this.logIntervalId = setInterval(() => {
      this.logMetrics();
    }, this.options.logInterval);

    if (this.options.logToConsole) {
      console.log(`[SYSTEM] System monitor started. Checking every ${this.options.checkInterval / 1000}s, logging every ${this.options.logInterval / 1000}s`);
    }
  }

  /**
   * Stop monitoring
   */
  stop() {
    if (this.checkIntervalId) {
      clearInterval(this.checkIntervalId);
      this.checkIntervalId = null;
    }

    if (this.logIntervalId) {
      clearInterval(this.logIntervalId);
      this.logIntervalId = null;
    }

    if (this.options.logToConsole) {
      console.log('[SYSTEM] System monitor stopped');
    }
  }

  /**
   * Check system metrics
   */
  checkMetrics() {
    try {
      // Update memory metrics
      const memoryUsage = process.memoryUsage();
      this.metrics.memory = {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        rss: memoryUsage.rss,
        external: memoryUsage.external
      };

      // Update system metrics
      this.metrics.system = {
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        uptime: process.uptime()
      };

      // Check thresholds
      this.checkThresholds();
    } catch (error) {
      console.error(`[SYSTEM] Error checking metrics: ${error.message}`);
    }
  }

  /**
   * Check if any thresholds are exceeded
   */
  checkThresholds() {
    const { memory, system } = this.metrics;
    
    // Calculate memory usage percentages
    const heapUsagePercent = (memory.heapUsed / memory.heapTotal) * 100;
    const systemMemoryUsagePercent = ((system.totalMemory - system.freeMemory) / system.totalMemory) * 100;

    // Warning thresholds
    if (heapUsagePercent > 80 || systemMemoryUsagePercent > 90) {
      this.options.onWarning(this.metrics);
    }

    // Critical thresholds
    if (heapUsagePercent > 90 || systemMemoryUsagePercent > 95) {
      this.options.onCritical(this.metrics);
    }
  }

  /**
   * Log current metrics
   */
  logMetrics() {
    const now = Date.now();
    
    // Skip if we've logged recently
    if (now - this.lastLogTime < this.options.logInterval / 2) {
      return;
    }
    
    this.lastLogTime = now;
    
    try {
      const { memory, system } = this.metrics;
      
      // Format metrics for logging
      const timestamp = new Date().toISOString();
      const heapUsedMB = (memory.heapUsed / 1024 / 1024).toFixed(2);
      const heapTotalMB = (memory.heapTotal / 1024 / 1024).toFixed(2);
      const rssMB = (memory.rss / 1024 / 1024).toFixed(2);
      const systemMemoryUsedMB = ((system.totalMemory - system.freeMemory) / 1024 / 1024).toFixed(2);
      const systemMemoryTotalMB = (system.totalMemory / 1024 / 1024).toFixed(2);
      
      const logMessage = `[${timestamp}] Memory: Heap ${heapUsedMB}MB/${heapTotalMB}MB (${((memory.heapUsed / memory.heapTotal) * 100).toFixed(1)}%), RSS: ${rssMB}MB, System: ${systemMemoryUsedMB}MB/${systemMemoryTotalMB}MB, Uptime: ${Math.floor(system.uptime / 60)} minutes`;
      
      // Log to console if enabled
      if (this.options.logToConsole) {
        console.log(`[SYSTEM] ${logMessage}`);
      }
      
      // Log to file
      fs.appendFile(this.options.logPath, logMessage + '\n', (err) => {
        if (err) {
          console.error(`[SYSTEM] Failed to write to log file: ${err.message}`);
        }
      });
    } catch (error) {
      console.error(`[SYSTEM] Error logging metrics: ${error.message}`);
    }
  }

  /**
   * Get current metrics
   * @returns {Object} Current system metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
}

module.exports = SystemMonitor;
