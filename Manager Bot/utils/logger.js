const { WebhookClient, EmbedBuilder } = require("discord.js");

// Use the config adapter for backward compatibility with the new config structure
const config = require('./configAdapter');
const webhookClient = config.DiscLogs ? new WebhookClient({ url: config.DiscLogs }) : null;

async function sendLog(guild, logType, description, color = "#FFFFFF") {
    if (!guild || !guild.id) return;

    // Get guild-specific color from config
    const guildColor = config.guildColors[guild.id] || color;

    const embed = new EmbedBuilder()
        .setTitle(`📜 ${logType} Log`)
        .setColor(guildColor)
        .setDescription(description)
        .setFooter({ text: `Guild: ${guild.name}` })
        .setTimestamp();

    try {
        await webhookClient.send({ embeds: [embed] });
        console.log(`✅ Sent ${logType} log for ${guild.name}`);
    } catch (error) {
        console.error(`❌ Failed to send ${logType} log:`, error);
    }
}

module.exports = { sendLog };
