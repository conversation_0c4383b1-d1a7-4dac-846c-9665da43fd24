const { EmbedBuilder } = require("discord.js");
const TrackedRole = require("../models/TrackedRole");
const StatusMessage = require("../models/StatusMessage");
const UserCountry = require("../models/UserCountry");

const countryFlags = {
    "US": "🇺🇸", "Canada": "🇨🇦", "UK": "🇬🇧", "Germany": "🇩🇪",
    "France": "🇫🇷", "Italy": "🇮🇹", "Spain": "🇪🇸", "Australia": "🇦🇺",
    "Japan": "🇯🇵", "China": "🇨🇳", "India": "🇮🇳", "Brazil": "🇧🇷"
};

// Track the last update time to avoid excessive updates
let lastUpdateTime = 0;

async function updateEmbed(client, channelID) {
    // Only update once every 5 minutes to reduce memory usage
    const now = Date.now();
    if (now - lastUpdateTime < 300000) { // 5 minutes
        return; // Skip this update if less than 5 minutes have passed
    }
    lastUpdateTime = now;

    const channel = client.channels.cache.get(channelID);
    if (!channel) return;

    try {
        let messageData = await StatusMessage.findOne({ channelID });
        let embedMessage = messageData
            ? await channel.messages.fetch(messageData.messageID).catch(() => null)
            : null;

        // Limit the number of tracked roles to reduce memory usage
        const trackedRoles = await TrackedRole.find().limit(10);
        if (trackedRoles.length === 0) {
            if (embedMessage) {
                await embedMessage.delete().catch(() => {});
                await StatusMessage.deleteOne({ channelID });
            }
            return;
        }

        // Pre-fetch all user countries in one query to reduce database calls
        const allMemberIds = [];
        const roleMembers = {};

        // First collect all member IDs - limit to 100 members per role to reduce memory usage
        for (const trackedRole of trackedRoles) {
            const role = channel.guild.roles.cache.get(trackedRole.roleID);
            if (!role || role.members.size === 0) continue;

            // Store only the first 100 members
            const limitedMembers = new Map();
            let count = 0;
            for (const [id, member] of role.members.entries()) {
                if (count >= 100) break;
                limitedMembers.set(id, member);
                count++;
            }

            roleMembers[trackedRole.roleID] = limitedMembers;
            limitedMembers.forEach(member => allMemberIds.push(member.id));
        }

        // Fetch all countries in one query - limit to 500 to prevent large queries
        const allCountries = await UserCountry.find({
            userID: { $in: allMemberIds.slice(0, 500) }
        });

        // Create a map for quick lookup
        const countryMap = {};
        allCountries.forEach(data => {
            countryMap[data.userID] = data.country;
        });

        let roleGroups = {};
        for (const trackedRole of trackedRoles) {
            const role = channel.guild.roles.cache.get(trackedRole.roleID);
            if (!role) continue;

            let members = roleMembers[trackedRole.roleID];
            if (!members || members.size === 0) continue;

            let usersInRole = [];
            for (const [memberID, member] of members) {
                // Get country from the pre-fetched map
                const country = countryMap[member.id];
                const countryFlag = country ? (countryFlags[country] || "🌍") : "❔";

                // Status handling - simplified to reduce processing
                let statusEmoji = "⚫ Offline"; // Default to Offline
                if (member.presence && member.presence.status) {
                    if (member.presence.status === "online") statusEmoji = "🟢 Online";
                    else if (member.presence.status === "idle") statusEmoji = "🟠 Idle";
                    else if (member.presence.status === "dnd") statusEmoji = "🔴 DND";
                }

                usersInRole.push(`${countryFlag} | ${statusEmoji} | <@${member.user.id}>`);
            }

            roleGroups[`< ${role.name} >`] = usersInRole;
        }

        // Limit to 2 embeds maximum to reduce memory usage
        let embeds = [];
        let currentEmbed = new EmbedBuilder()
            .setTitle("User Status")
            .setColor("#FFFFFF")
            .setTimestamp();

        let fieldCount = 0;
        const maxFields = 10; // Maximum fields per embed
        const maxUsersToShow = 15; // Reduced from 25 to 15 to prevent oversized embeds

        for (const [role, users] of Object.entries(roleGroups)) {
            // If we've reached the maximum number of embeds, stop processing
            if (embeds.length >= 1 && fieldCount >= maxFields) {
                break;
            }

            // Limit the number of users shown
            let displayUsers = users.slice(0, maxUsersToShow);
            if (users.length > maxUsersToShow) {
                displayUsers.push(`*...and ${users.length - maxUsersToShow} more*`);
            }

            let roleText = displayUsers.join("\n") || "*No users*";

            // Check if adding this field would exceed Discord's limits
            if (roleText.length > 1024) {
                roleText = roleText.substring(0, 1000) + "...";
            }

            currentEmbed.addFields({ name: role, value: roleText, inline: false });
            fieldCount++;

            if (fieldCount === maxFields) {
                embeds.push(currentEmbed);

                // Only create a second embed if we have more roles to process
                if (Object.entries(roleGroups).length > maxFields) {
                    currentEmbed = new EmbedBuilder()
                        .setTitle("User Status (Continued)")
                        .setColor("#FFFFFF")
                        .setTimestamp();
                    fieldCount = 0;
                }
            }
        }

        // Only add the current embed if it has fields and we haven't already added it
        if (fieldCount > 0 && currentEmbed.data.fields && currentEmbed.data.fields.length > 0) {
            embeds.push(currentEmbed);
        }

        try {
            if (!embedMessage) {
                let newMessage = await channel.send({ embeds }).catch(err => {
                    // If sending fails, try with a simpler embed
                    const errorEmbed = new EmbedBuilder()
                        .setTitle("User Status")
                        .setDescription("Too many users to display. Please check the server member list.")
                        .setColor("#FF0000");
                    return channel.send({ embeds: [errorEmbed] });
                });

                if (newMessage) {
                    await StatusMessage.create({ messageID: newMessage.id, channelID });
                }
            } else {
                await embedMessage.edit({ embeds }).catch(async err => {
                    // If editing fails, try with a simpler embed
                    const errorEmbed = new EmbedBuilder()
                        .setTitle("User Status")
                        .setDescription("Too many users to display. Please check the server member list.")
                        .setColor("#FF0000");
                    await embedMessage.edit({ embeds: [errorEmbed] });
                });
            }
        } catch (error) {
            // Silently ignore errors to prevent crashes
        }
    } catch (error) {
        // Silently ignore errors to prevent crashes
    }

    // Force garbage collection if available
    if (global.gc) {
        global.gc();
    }
}

module.exports = updateEmbed;
