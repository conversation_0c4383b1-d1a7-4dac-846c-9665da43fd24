/**
 * Memory Optimizer
 * 
 * A utility to optimize memory usage in the Discord bot.
 */

class MemoryOptimizer {
  /**
   * Initialize memory optimization
   * @param {Client} client - Discord.js client
   * @returns {Object} Memory optimizer instance
   */
  static init(client) {
    console.log('[MEMORY] Initializing memory optimizer');
    
    // Store original collection methods to optimize
    const originalSweep = Map.prototype.sweep;
    
    // Create optimizer instance
    const optimizer = {
      client,
      
      // Optimize collections periodically
      optimizeCollections() {
        try {
          console.log('[MEMORY] Optimizing collections');
          
          // Sweep old messages from cache
          if (client.channels && client.channels.cache) {
            let messageCount = 0;
            
            client.channels.cache.forEach(channel => {
              if (channel.messages && channel.messages.cache) {
                // Keep only messages from the last hour
                const oldCount = channel.messages.cache.size;
                channel.messages.cache.sweep(message => {
                  const oneHourAgo = Date.now() - 3600000;
                  return message.createdTimestamp < oneHourAgo;
                });
                messageCount += (oldCount - channel.messages.cache.size);
              }
            });
            
            if (messageCount > 0) {
              console.log(`[MEMORY] Cleared ${messageCount} old messages from cache`);
            }
          }
          
          // Run garbage collection if available
          if (global.gc) {
            global.gc();
          }
        } catch (error) {
          console.error(`[MEMORY] Error optimizing collections: ${error.message}`);
        }
      },
      
      // Start periodic optimization
      startPeriodicOptimization(interval = 900000) { // 15 minutes
        this.stopPeriodicOptimization();
        
        console.log(`[MEMORY] Starting periodic optimization every ${interval / 60000} minutes`);
        this.optimizationInterval = setInterval(() => {
          this.optimizeCollections();
        }, interval);
        
        // Run initial optimization
        this.optimizeCollections();
        
        return this;
      },
      
      // Stop periodic optimization
      stopPeriodicOptimization() {
        if (this.optimizationInterval) {
          clearInterval(this.optimizationInterval);
          this.optimizationInterval = null;
          console.log('[MEMORY] Stopped periodic optimization');
        }
        return this;
      }
    };
    
    // Start periodic optimization
    optimizer.startPeriodicOptimization();
    
    return optimizer;
  }
}

module.exports = MemoryOptimizer;
