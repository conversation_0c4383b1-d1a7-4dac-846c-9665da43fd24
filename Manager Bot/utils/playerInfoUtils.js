const axios = require('axios');
const cheerio = require('cheerio');
const { EmbedBuilder } = require('discord.js');
const config = require('./configAdapter');

/**
 * Utility class for retrieving detailed player information
 */
class PlayerInfoUtils {
    /**
     * Create a thread with detailed player information
     * @param {Object} client - Discord client
     * @param {Object} channel - Discord channel to create thread in
     * @param {Object} message - Discord message to create thread from
     * @param {string} steamId - Steam ID of the player
     * @param {string} playerName - Name of the player
     * @param {string} battlemetricsId - BattleMetrics ID of the player
     * @param {string} requestType - Type of request (ban, spectate, etc.)
     * @param {string} requestId - ID of the request
     * @returns {Promise<Object>} - Created thread
     */
    static async createPlayerInfoThread(client, channel, message, steamId, playerName, battlemetricsId, requestType, requestId) {
        try {
            // Create thread
            const thread = await message.startThread({
                name: `${requestType} Request: ${playerName || steamId}`,
                autoArchiveDuration: 1440 // 1 day
            });

            // Get player information
            let steamData = { playtime: "N/A", banStatus: "N/A" };
            let battleMetricsData = { bans: "No Bans Found", bannedServers: [] };
            let rustStats = { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
            let banData = { banStatus: "N/A", nexusProfile: "N/A" };

            try {
                steamData = await this.getSteamData(steamId);
            } catch (error) {
                console.error("Failed to fetch Steam data:", error);
            }

            try {
                rustStats = await this.getRustStats(steamId);
            } catch (error) {
                console.error("Failed to get Rust Stats", error);
            }

            try {
                banData = await this.checkGameBan(steamId);
            } catch (error) {
                console.error("Failed to fetch Ban data:", error);
            }

            try {
                battleMetricsData = await this.getBattleMetricsData(steamId);
            } catch (error) {
                console.error("Failed to fetch BattleMetrics data:", error);
            }

            let bans = await this.getBans(steamId);
            let hoursAndServers = await this.getHoursAndServerInfo(steamId);
            let kdF7 = await this.getKDandF7(steamId, battlemetricsId);

            // Create player info embed
            const playerEmbed = new EmbedBuilder()
                .setTitle("Player Information")
                .setColor(config.BotSettings.EmbedColors || "#00FF00")
                .addFields(
                    { name: "Steam Profile", value: `[Click Here](https://steamcommunity.com/profiles/${steamId})`, inline: true },
                    { name: "Total Hours", value: steamData.playtime, inline: true },
                    { name: "Total Servers Played", value: battleMetricsData.serversPlayed, inline: true },
                    { name: "Account Age", value: battleMetricsData.accountAge, inline: true },
                    { name: "Kill/Death Ratio", value: `\`\`\`Kills: ${rustStats.kills} | Deaths: ${rustStats.deaths} | K/D: ${rustStats.kdRatio}\`\`\``, inline: false },
                    { name: "Steam Bans", value: steamData.banStatus, inline: true },
                    { name: "Game Bans", value: `${banData.banStatus} | [Check Profile](${banData.nexusProfile})`, inline: true },
                    { name: "BattleMetrics Links", value: `[Public Profile](${battleMetricsData.profileURL}) | [RCON Profile](${battleMetricsData.rconURL})`, inline: false },
                    { name: "Organization Hours", value: `${hoursAndServers.orgHours.toFixed(1)}` || "0", inline: true },
                    { name: "Aimtrain Hours", value: `${hoursAndServers.aimtrainHours.toFixed(1)}` || "0", inline: true },
                    { name: "Total Hours in Rust", value: `${hoursAndServers.allHours.toFixed(1)}` || "0", inline: true },
                    { name: "K/D Over 14 Days", value: kdF7.KDDay || `N/A`, inline: true },
                    { name: "F7 Reports Total", value: kdF7.F7Total || `N/A`, inline: true },
                    { name: "F7 Reports 24 Hours", value: kdF7.F7Day || `N/A`, inline: true }
                )
                .setTimestamp();

            if (bans.bans && bans.bans !== "No bans on record.") {
                playerEmbed.addFields({ name: "Recorded Bans", value: `\`\`\`${bans.bans}\`\`\``, inline: false });
            }

            // Send player info embed
            await thread.send({ embeds: [playerEmbed] });

            // Send banned servers embed if available
            if (bans.bannedServers && bans.bannedServers !== "No banned servers found.") {
                const bannedEmbed = new EmbedBuilder()
                    .setTitle(`Banned Servers`)
                    .setDescription(`\`\`\`${bans.bannedServers}\`\`\``)
                    .setColor(config.BotSettings.EmbedColors || "#00FF00")
                    .setTimestamp();

                await thread.send({ embeds: [bannedEmbed] });
            }

            return thread;
        } catch (error) {
            console.error("Error creating player info thread:", error);
            return null;
        }
    }

    /**
     * Get Steam data for a player
     * @param {string} steamId - Steam ID of the player
     * @returns {Promise<Object>} - Steam data
     */
    static async getSteamData(steamId) {
        if (!config.APIKeys.steam_api_key) {
            console.error("Missing Steam API Key!");
            return { playtime: "N/A", banStatus: "N/A", gameBans: "N/A" };
        }

        const bansURL = `https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=${config.APIKeys.steam_api_key}&steamids=${steamId}`;
        const playtimeURI = `https://api.steampowered.com/IPlayerService/GetOwnedGames/v1/?key=${config.APIKeys.steam_api_key}&steamid=${steamId}&include_played_free_games=true`;
        const playerSummariesURL = `https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/?key=${config.APIKeys.steam_api_key}&steamids=${steamId}`;

        try {
            const [banResponse, playtimeResponse, summaryResponse] = await Promise.all([
                axios.get(bansURL),
                axios.get(playtimeURI),
                axios.get(playerSummariesURL)
            ]);

            // Check if the profile is private
            let profileStatus = "Public";
            let playerData = summaryResponse.data?.response?.players?.[0];

            if (playerData && playerData.communityvisibilitystate !== 3) {
                profileStatus = "Private";
                console.log(`Steam profile ${steamId} is private.`);
            }

            // Default playtime
            let playtime = "N/A";
            if (playtimeResponse.data?.response?.games && profileStatus === "Public") {
                let rustGame = playtimeResponse.data.response.games.find(game => game.appid === 252490);
                if (rustGame) {
                    let totalMinutes = rustGame.playtime_forever || 0;
                    playtime = (totalMinutes / 60).toFixed(1); // Convert to hours
                }
            }

            const banData = banResponse.data?.players?.[0] || {};
            let banStatus = banData.VACBanned ? `VAC Banned: ${banData.NumberOfVACBans} times` : "No Bans";
            let gameBans = banData.NumberOfGameBans > 0 ? `Game Bans: ${banData.NumberOfGameBans}` : "No Game Bans";

            return { playtime, banStatus, gameBans, profileStatus };
        } catch (error) {
            console.error("Failed to fetch Steam data:", error.message);
            return { playtime: "N/A", banStatus: "N/A", gameBans: "N/A", profileStatus: "Error" };
        }
    }

    /**
     * Check if a player has game bans
     * @param {string} steamId - Steam ID of the player
     * @returns {Promise<Object>} - Game ban data
     */
    static async checkGameBan(steamId) {
        const nexusURL = `https://www.nexusonline.co.uk/bans/profile/?id=${steamId}`;

        try {
            const response = await axios.get(nexusURL);
            const $ = cheerio.load(response.data);
            const banText = $(".header").text().trim();
            let banStatus = "No Game Bans Found";
            if (banText.includes("Player is currently game banned!")) {
                banStatus = "User is Game Banned!";
            }

            return { banStatus, nexusProfile: nexusURL };
        } catch (error) {
            console.error("Failed to fetch game ban status:", error.message);
            return { banStatus: "N/A (Failed to Check)", nexusProfile: nexusURL };
        }
    }

    /**
     * Get BattleMetrics data for a player
     * @param {string} steamId - Steam ID of the player
     * @returns {Promise<Object>} - BattleMetrics data
     */
    static async getBattleMetricsData(steamId) {
        if (!config.APIKeys.bm_api_token) {
            console.error("Missing BattleMetrics API Key!");
            return {
                bans: "No Bans Found",
                bannedServers: [],
                f7Total: "N/A",
                altAccounts: "No Alts Found",
                accountAge: "N/A",
                serversPlayed: "N/A",
                profileURL: "N/A",
                rconURL: "N/A"
            };
        }

        const headers = { Authorization: `Bearer ${config.APIKeys.bm_api_token}` };
        const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamId}&include=server,playerFlag,identifier,flagPlayer`;

        try {
            const response = await axios.get(bmURL, { headers });

            if (!response.data || !response.data.data.length) {
                return {
                    bans: "No Bans Found",
                    bannedServers: [],
                    f7Total: "N/A",
                    altAccounts: "No Alts Found",
                    accountAge: "N/A",
                    serversPlayed: "N/A",
                    profileURL: "N/A",
                    rconURL: "N/A",
                    BMID: "N/A"
                };
            }

            const playerData = response.data.data[0];

            let profileURL = `https://www.battlemetrics.com/players/${playerData.id}`;
            let rconURL = `https://www.battlemetrics.com/rcon/players/${playerData.id}`;
            let bans = playerData.attributes.banCount > 0 ? `🚨 ${playerData.attributes.banCount} Recorded Bans` : "No Bans Found";
            let serversPlayed = playerData.relationships?.servers?.data?.length ? `${playerData.relationships.servers.data.length}` : "N/A";
            let accountCreatedAt = playerData.attributes.createdAt || null;
            let accountAge = accountCreatedAt
                ? `<t:${Math.floor(new Date(accountCreatedAt).getTime() / 1000)}:F>`
                : "N/A";

            // Fetch F7 Reports
            let f7Total = "N/A";
            if (playerData.relationships?.playerFlags?.data?.length > 0) {
                f7Total = `${playerData.relationships.playerFlags.data.length}`;
            }

            // Fetch Banned Servers
            let bannedServers = [];
            if (playerData.relationships?.flags?.data?.length > 0) {
                bannedServers = playerData.relationships.flags.data
                    .map(flag => `[Server Ban](https://www.battlemetrics.com/players/${flag.id})`);
            }

            return {
                bans,
                bannedServers,
                f7Total,
                accountAge,
                serversPlayed,
                profileURL,
                rconURL,
                BMID: `${playerData.id}`
            };
        } catch (error) {
            console.error(`Failed to fetch BattleMetrics data for ${steamId}:`, error.message);
            return {
                bans: "No Bans Found",
                bannedServers: [],
                f7Total: "N/A",
                altAccounts: [],
                accountAge: "N/A",
                serversPlayed: "N/A",
                profileURL: "N/A",
                rconURL: "N/A",
                BMID: "N/A"
            };
        }
    }

    /**
     * Get Rust stats for a player
     * @param {string} steamId - Steam ID of the player
     * @returns {Promise<Object>} - Rust stats
     */
    static async getRustStats(steamId) {
        if (!config.APIKeys.steam_api_key) {
            console.error("Missing Steam API Key!");
            return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
        }

        const rustStatsURL = `https://api.steampowered.com/ISteamUserStats/GetUserStatsForGame/v0002/?appid=252490&key=${config.APIKeys.steam_api_key}&steamid=${steamId}`;

        try {
            const response = await axios.get(rustStatsURL);

            if (!response.data?.playerstats?.stats) {
                return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
            }

            const stats = response.data.playerstats.stats;

            // Extract kills and deaths
            const killsStat = stats.find(stat => stat.name === "kill_player");
            const deathsStat = stats.find(stat => stat.name === "deaths");

            const kills = killsStat ? killsStat.value : 0;
            const deaths = deathsStat ? deathsStat.value : 0;
            const kdRatio = deaths > 0 ? (kills / deaths).toFixed(2) : `${kills}.00`;

            return { kills, deaths, kdRatio };
        } catch (error) {
            console.error("Failed to fetch Rust stats:", error.message);
            return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
        }
    }

    /**
     * Get K/D and F7 reports for a player
     * @param {string} steamId - Steam ID of the player
     * @param {string} bmId - BattleMetrics ID of the player
     * @returns {Promise<Object>} - K/D and F7 data
     */
    static async getKDandF7(steamId, bmId) {
        if (!config.APIKeys.bm_api_token) {
            return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
        }

        let kills = 0, deaths = 0;
        let killsOneDay = 0, deathsOneDay = 0;
        let f7reports = 0, f7reportsOneDay = 0;
        let oneDay = Date.now() - (14 * 24 * 60 * 60 * 1000); // Time threshold for 14 days

        const headers = { Authorization: `Bearer ${config.APIKeys.bm_api_token}` };

        try {
            // If bmId is not provided, fetch it
            if (!bmId) {
                const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamId}&include=server,playerFlag,identifier,flagPlayer`;
                const BMData = await axios.get(bmURL, { headers });

                if (!BMData.data?.data?.length) {
                    return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
                }

                bmId = BMData.data.data[0].id;
            }

            const url = `https://api.battlemetrics.com/activity?tagTypeMode=and&filter[types][blacklist]=event:query&filter[players]=${bmId}&include=organization,user&page[size]=1000`;

            const response = await axios.get(url, { timeout: 10000 });

            if (response.status !== 200 || !response.data?.data) {
                return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
            }

            const events = response.data.data;

            for (const event of events) {
                if (!event || !event.attributes) continue;

                let timestamp = new Date(event.attributes.timestamp).getTime();

                if (event.attributes.messageType === "rustLog:playerDeath:PVP") {
                    if (event.attributes.data?.killer_id === bmId) {
                        kills++;
                        if (timestamp > oneDay) killsOneDay++;
                    } else if (event.attributes.data?.victim_id === bmId) {
                        deaths++;
                        if (timestamp > oneDay) deathsOneDay++;
                    }
                }

                if (event.attributes.messageType === "rustLog:playerReport") {
                    if (event.attributes.data?.forPlayerId === bmId) {
                        f7reports++;
                        if (timestamp > oneDay) f7reportsOneDay++;
                    }
                }
            }

            let kd = deaths > 0 ? (kills / deaths).toFixed(2) : kills.toFixed(2);
            let kdOneDay = deathsOneDay > 0 ? (killsOneDay / deathsOneDay).toFixed(2) : killsOneDay.toFixed(2);

            return {
                KDTotal: kd,
                KDDay: `${killsOneDay}/${deathsOneDay} (K/D: ${kdOneDay})`,
                killsTotal: kills,
                deathsTotal: deaths,
                kills24hr: killsOneDay,
                deaths24hr: deathsOneDay,
                F7Total: f7reports,
                F7Day: f7reportsOneDay
            };
        } catch (error) {
            console.error("Failed to fetch K/D and F7 reports:", error.message);
            return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
        }
    }

    /**
     * Get bans for a player
     * @param {string} steamId - Steam ID of the player
     * @returns {Promise<Object>} - Ban data
     */
    static async getBans(steamId) {
        if (!steamId) {
            console.error("Error: steamID is undefined, cannot fetch bans.");
            return { bans: "Error: steamID is missing.", bannedServers: [] };
        }

        try {
            if (!config.APIKeys.bm_api_token) {
                console.error("Error: Missing BattleMetrics API token.");
                return { bans: "Error: Missing API token.", bannedServers: [] };
            }

            const headers = { Authorization: `Bearer ${config.APIKeys.bm_api_token}` };

            // Fetch Player ID from BattleMetrics
            const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamId}&include=server,playerFlag,identifier,flagPlayer`;
            const BMData = await axios.get(bmURL, { headers });

            if (!BMData.data?.data?.length) {
                return { bans: "Error: Player not found on BattleMetrics.", bannedServers: [] };
            }

            const playerData = BMData.data.data[0];

            // Fetch bans for the player
            const bansURL = `https://api.battlemetrics.com/bans?filter[player]=${playerData.id}&include=server&page[size]=100`;
            const response = await axios.get(bansURL, { headers });

            if (response.status === 401) {
                console.error("Unauthorized: Check API token permissions.");
                return { bans: "Error: Unauthorized request. Check API token.", bannedServers: [] };
            }

            if (response.status !== 200) {
                console.error(`BattleMetrics API returned status: ${response.status}`);
                return { bans: "Error fetching ban data.", bannedServers: [] };
            }

            const bans = response.data?.data || [];
            if (bans.length === 0) return { bans: "No bans on record.", bannedServers: [] };

            let bannedServers = [];

            // Fetch Server Names instead of IDs along with Ban Reasons
            for (let ban of bans) {
                let serverName = "Unknown Server";

                // Check if the server data exists in the API response
                if (ban.relationships?.server?.data?.id) {
                    const serverData = response.data.included?.find(s => s.id === ban.relationships.server.data.id);
                    if (serverData) {
                        serverName = serverData.attributes?.name || "Unknown Server";
                    }
                }

                let reason = ban.attributes?.reason || "No reason provided";
                bannedServers.push(`${serverName} - ${reason}\n`);
            }

            const banReasons = response.data?.data.length
                ? response.data.data.map(ban => ban.attributes.reason).join("\n")
                : "No bans on record.";

            return {
                bans: banReasons,
                bannedServers: bannedServers.length > 0 ? bannedServers.join("\n") : "No banned servers found.",
            };
        } catch (error) {
            if (error.response && error.response.status === 401) {
                console.error("Unauthorized API request. Check API token.");
                return { bans: "Error: Unauthorized API request. Check API token.", bannedServers: [] };
            }

            console.error(`Error fetching bans:`, error.message);
            return { bans: "Failed to retrieve ban data.", bannedServers: [] };
        }
    }

    /**
     * Get hours and server info for a player
     * @param {string} steamId - Steam ID of the player
     * @param {number} retryCount - Number of retries
     * @returns {Promise<Object>} - Hours and server info
     */
    static async getHoursAndServerInfo(steamId, retryCount = 0) {
        if (!config.APIKeys.bm_api_token) {
            return {
                allHours: 0,
                orgHours: 0,
                aimtrainHours: 0,
                totalServers: 0,
                orgServers: []
            };
        }

        const headers = { Authorization: `Bearer ${config.APIKeys.bm_api_token}` };
        const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamId}&include=server,playerFlag,identifier,flagPlayer`;

        try {
            const BMData = await axios.get(bmURL, { headers });

            // Check if playerData exists
            const playerData = BMData.data?.data?.[0];
            if (!playerData) {
                console.error(`Player not found for steamID: ${steamId}`);
                return {
                    allHours: 0,
                    orgHours: 0,
                    aimtrainHours: 0,
                    totalServers: 0,
                    orgServers: []
                };
            }

            const url = `https://api.battlemetrics.com/players/${playerData.id}?include=server&fields[server]=name`;
            const response = await axios.get(url, { headers });

            if (response.status !== 200) {
                throw new Error(`Unexpected response status: ${response.status}`);
            }

            let totalHours = 0, aimtrainHours = 0, orgHours = 0, totalServers = 0;
            let orgServers = [];

            if (response.data?.included) {
                response.data.included.forEach(server => {
                    if (server.relationships?.game?.data?.id === "rust") {
                        totalServers++;
                        totalHours += server.meta?.timePlayed || 0;

                        if (server.attributes.name.toLowerCase().includes("aim") ||
                            server.attributes.name.toLowerCase().includes("training grounds") ||
                            server.attributes.name.toLowerCase().includes("ukn") ||
                            server.attributes.name.toLowerCase().includes("aimtrain")) {
                            aimtrainHours += server.meta?.timePlayed || 0;
                        } else if (server?.relationships?.organization?.data?.id === config.StaffManagement.BattlemetricsORGID) {
                            orgHours += server.meta?.timePlayed || 0;
                        }

                        if (server.relationships?.organization?.data?.id === config.StaffManagement.BattlemetricsORGID) {
                            orgServers.push({
                                serverName: server.attributes.name || "Unknown",
                                serverId: server.id || "N/A",
                                online: server.meta?.online || false,
                                timePlayed: server.meta?.timePlayed || 0,
                                lastSeen: server.meta?.lastSeen || "Unknown"
                            });
                        }
                    }
                });
            }

            return {
                allHours: totalHours / 3600 || 0,
                orgHours: orgHours / 3600 || 0,
                aimtrainHours: aimtrainHours / 3600 || 0,
                totalServers: totalServers || 0,
                orgServers
            };
        } catch (error) {
            console.error(`Error in getHoursAndServerInfo:`, error.message);

            if (error.response && error.response.status === 429 && retryCount < 3) {
                console.warn(`Rate limited! Retrying in 5 seconds (attempt ${retryCount + 1}/3)...`);
                await new Promise(resolve => setTimeout(resolve, 5000));
                return await this.getHoursAndServerInfo(steamId, retryCount + 1);
            }

            return {
                allHours: 0,
                orgHours: 0,
                aimtrainHours: 0,
                totalServers: 0,
                orgServers: []
            };
        }
    }
}

module.exports = PlayerInfoUtils;
