// Use the config adapter for backward compatibility with the new config structure
const config = require('./configAdapter');
const BanMonitorConfig = require('../models/banMonitorConfig');

/**
 * Initializes the ban monitor configuration from the config.yml file
 */
async function initBanMonitorConfig() {
    try {
        console.log('Initializing ban monitor configuration...');

        // Check if config already exists
        let banConfig = await BanMonitorConfig.findOne();

        if (!banConfig) {
            // Create new config
            banConfig = new BanMonitorConfig({
                enabled: true,
                banListId: config.bm_ban_list_id || '',
                checkInterval: 60000, // 60 seconds
                staffWebhooks: [],
                publicWebhooks: [],
                embedColor: config.EmbedColors || '#CCCCCC',
                serverName: config.SERVER_NAME || 'Server'
            });

            // Add webhooks from config if they exist
            if (config.banStaffWebhook) {
                banConfig.staffWebhooks.push(config.banStaffWebhook);
            }

            if (config.banPublicWebhook) {
                banConfig.publicWebhooks.push(config.banPublicWebhook);
            }

            await banConfig.save();
            console.log('Ban monitor configuration created successfully');
        } else {
            // Update existing config with values from config.yml if they're not already set
            let updated = false;

            if (!banConfig.banListId && config.bm_ban_list_id) {
                banConfig.banListId = config.bm_ban_list_id;
                updated = true;
            }

            if (config.banStaffWebhook && !banConfig.staffWebhooks.includes(config.banStaffWebhook)) {
                banConfig.staffWebhooks.push(config.banStaffWebhook);
                updated = true;
            }

            if (config.banPublicWebhook && !banConfig.publicWebhooks.includes(config.banPublicWebhook)) {
                banConfig.publicWebhooks.push(config.banPublicWebhook);
                updated = true;
            }

            if (updated) {
                await banConfig.save();
                console.log('Ban monitor configuration updated successfully');
            } else {
                console.log('Ban monitor configuration already exists and is up to date');
            }
        }
    } catch (error) {
        console.error(`Error initializing ban monitor configuration: ${error.message}`);
    }
}

module.exports = { initBanMonitorConfig };
