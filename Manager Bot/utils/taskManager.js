/**
 * Task Manager
 * 
 * A utility to manage scheduled tasks for the bot.
 */

const fs = require('fs');
const path = require('path');

class TaskManager {
  /**
   * Create a new task manager
   * @param {Client} client - Discord.js client
   */
  constructor(client) {
    this.client = client;
    this.tasks = new Map();
    this.intervals = new Map();
    this.tasksDir = path.join(__dirname, '../tasks');
  }

  /**
   * Load all tasks from the tasks directory
   */
  loadTasks() {
    // Create tasks directory if it doesn't exist
    if (!fs.existsSync(this.tasksDir)) {
      try {
        fs.mkdirSync(this.tasksDir, { recursive: true });
        console.log(`[TASKS] Created tasks directory at ${this.tasksDir}`);
        
        // Create a sample task file
        const sampleTaskPath = path.join(this.tasksDir, 'sampleTask.js');
        if (!fs.existsSync(sampleTaskPath)) {
          const sampleTask = `/**
 * Sample Task
 * 
 * This is a sample task that can be scheduled by the task manager.
 */

module.exports = {
  name: 'sampleTask',
  description: 'A sample task that logs a message',
  interval: 3600000, // Run every hour (in milliseconds)
  enabled: false, // Set to true to enable
  
  /**
   * Execute the task
   * @param {Client} client - Discord.js client
   */
  execute(client) {
    console.log('[SAMPLE TASK] This is a sample task running at ' + new Date().toLocaleString());
  }
};`;
          fs.writeFileSync(sampleTaskPath, sampleTask);
          console.log(`[TASKS] Created sample task file at ${sampleTaskPath}`);
        }
        
        return; // No tasks to load yet
      } catch (error) {
        console.error(`[TASKS] Failed to create tasks directory: ${error.message}`);
        return;
      }
    }

    // Read all task files
    try {
      const taskFiles = fs.readdirSync(this.tasksDir).filter(file => file.endsWith('.js'));
      console.log(`[TASKS] Found ${taskFiles.length} task files`);
      
      for (const file of taskFiles) {
        try {
          const taskPath = path.join(this.tasksDir, file);
          const task = require(taskPath);
          
          // Validate task
          if (!task.name || !task.execute || typeof task.execute !== 'function') {
            console.warn(`[TASKS] Invalid task in ${file}: missing name or execute function`);
            continue;
          }
          
          // Add task to collection
          this.tasks.set(task.name, task);
          console.log(`[TASKS] Loaded task: ${task.name}`);
        } catch (error) {
          console.error(`[TASKS] Failed to load task ${file}: ${error.message}`);
        }
      }
    } catch (error) {
      console.error(`[TASKS] Failed to read tasks directory: ${error.message}`);
    }
  }

  /**
   * Start all enabled tasks
   */
  startTasks() {
    // Clear any existing intervals
    this.stopTasks();
    
    // Start each enabled task
    for (const [name, task] of this.tasks.entries()) {
      if (task.enabled && task.interval) {
        try {
          console.log(`[TASKS] Starting task: ${name} (interval: ${task.interval}ms)`);
          
          // Execute immediately if specified
          if (task.executeOnStart) {
            try {
              task.execute(this.client);
            } catch (error) {
              console.error(`[TASKS] Error executing task ${name}: ${error.message}`);
            }
          }
          
          // Set up interval
          const interval = setInterval(() => {
            try {
              task.execute(this.client);
            } catch (error) {
              console.error(`[TASKS] Error executing task ${name}: ${error.message}`);
            }
          }, task.interval);
          
          this.intervals.set(name, interval);
        } catch (error) {
          console.error(`[TASKS] Failed to start task ${name}: ${error.message}`);
        }
      }
    }
    
    console.log(`[TASKS] Started ${this.intervals.size} tasks`);
  }

  /**
   * Stop all running tasks
   */
  stopTasks() {
    for (const [name, interval] of this.intervals.entries()) {
      clearInterval(interval);
      console.log(`[TASKS] Stopped task: ${name}`);
    }
    
    this.intervals.clear();
  }

  /**
   * Run a specific task immediately
   * @param {string} taskName - Name of the task to run
   * @returns {boolean} Whether the task was found and executed
   */
  runTask(taskName) {
    const task = this.tasks.get(taskName);
    
    if (!task) {
      console.warn(`[TASKS] Task not found: ${taskName}`);
      return false;
    }
    
    try {
      console.log(`[TASKS] Running task: ${taskName}`);
      task.execute(this.client);
      return true;
    } catch (error) {
      console.error(`[TASKS] Error executing task ${taskName}: ${error.message}`);
      return false;
    }
  }
}

module.exports = TaskManager;
