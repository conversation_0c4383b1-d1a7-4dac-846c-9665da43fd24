const axios = require('axios');

/**
 * Utility class for making requests to the BattleMetrics API with rate-limit handling.
 */
class BMRequests {
    /**
     * Handles GET requests to the BattleMetrics API with rate-limit handling.
     * @param {string} url - The API endpoint.
     * @param {string|null} token - The authentication token (optional).
     * @returns {Promise<Object>} - The response object.
     */
    static async getRequest(url, token = null) {
        const headers = token ? { "Authorization": `Bearer ${token}` } : {};

        while (true) {
            try {
                const response = await axios.get(url, { headers });

                if (response.headers['x-rate-limit-remaining']) {
                    const remaining = parseInt(response.headers['x-rate-limit-remaining'], 10);
                    if (remaining < 75 && token) {
                        console.log(`${remaining} requests left. URL: ${url}`);
                    }
                }

                return {
                    status: response.status,
                    text: JSON.stringify(response.data),
                    headers: response.headers
                };

            } catch (error) {
                if (error.response && error.response.status === 429) {
                    const retryAfter = parseInt(error.response.headers['retry-after']) || 30;
                    console.warn(`(429) Rate limited on ${url}. Retrying in ${retryAfter}s.`);
                    await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
                } else {
                    console.error(`Error fetching ${url}: ${error.message}`);
                    await new Promise(resolve => setTimeout(resolve, 30000)); // Retry after 30s on failure
                    return {
                        status: error.response?.status || 500,
                        text: JSON.stringify({ error: error.message }),
                        headers: {}
                    };
                }
            }
        }
    }

    /**
     * Handles POST requests to the BattleMetrics API with rate-limit handling.
     * @param {string} url - The API endpoint.
     * @param {Object} data - The JSON data to send.
     * @param {string|null} token - The authentication token (optional).
     * @returns {Promise<Object>} - The response object.
     */
    static async postRequest(url, data, token = null) {
        const headers = { "Content-Type": "application/json" };
        if (token) headers["Authorization"] = `Bearer ${token}`;

        while (true) {
            try {
                const response = await axios.post(url, data, { headers });

                return {
                    status: response.status,
                    text: JSON.stringify(response.data),
                    headers: response.headers
                };

            } catch (error) {
                if (error.response && error.response.status === 429) {
                    const retryAfter = parseInt(error.response.headers['retry-after']) || 30;
                    console.warn(`(429) Rate limited on ${url}. Retrying in ${retryAfter}s.`);
                    await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
                } else {
                    console.error(`Error posting to ${url}: ${error.message}`);
                    await new Promise(resolve => setTimeout(resolve, 30000)); // Retry after 30s on failure
                    return {
                        status: error.response?.status || 500,
                        text: JSON.stringify({ error: error.message }),
                        headers: {}
                    };
                }
            }
        }
    }
}

module.exports = BMRequests;
