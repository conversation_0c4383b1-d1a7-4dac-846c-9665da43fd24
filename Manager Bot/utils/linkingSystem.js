const fetch = require('node-fetch');

let config;
// Use the config adapter for backward compatibility with the new config structure
const config = require('./utils/configAdapter');`);
  // Create a minimal config object to prevent errors
  config = {
    LinkingSystem: {
      Type: 'disabled',
      WEBSITE_URL: 'https://example.com/',
      linkingWebsite: 'https://example.com/',
      SECRET_KEY: 'default'
    }
  };
}

/**
 * Fetches user information from the configured linking system
 * @param {string} discordId - Discord ID of the user
 * @returns {Promise<Object|null>} User information or null if not found
 */
async function fetchUserFromLinkingSystem(discordId) {
  const linkingSystem = config.LinkingSystem?.Type?.toLowerCase() || 'custom';

  // If linking system is disabled, return a mock user object to bypass linking checks
  if (linkingSystem === 'disabled') {
    console.log(`Linking system is disabled, returning mock user for ${discordId}`);
    return {
      discordId: discordId,
      discordUsername: "Unknown",
      steamId: "N/A",
      steamUsername: "N/A",
      source: 'disabled'
    };
  }

  switch (linkingSystem) {
    case 'steamcord':
      return fetchUserFromSteamcord(discordId);
    case 'platformsync':
      return fetchUserFromPlatformSync(discordId);
    case 'custom':
    default:
      return fetchUserFromCustomSystem(discordId);
  }
}

/**
 * Fetches user information from Steamcord API
 * @param {string} discordId - Discord ID of the user
 * @returns {Promise<Object|null>} User information or null if not found
 */
async function fetchUserFromSteamcord(discordId) {
  try {
    const url = `https://api.steamcord.io/players?discordId=${discordId}`;
    console.log(`Fetching from Steamcord: ${url}`);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.LinkingSystem?.SteamcordApiKey || config.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    console.log(`Response Status: ${response.status}`);
    if (!response.ok) {
      const errorBody = await response.text();
      console.log(`Error Body: ${errorBody}`);
      throw new Error(`Steamcord API error: ${response.status} - ${errorBody}`);
    }

    const players = await response.json();
    if (!Array.isArray(players) || players.length === 0) {
      console.log(`No player found for Discord ID: ${discordId}`);
      return null;
    }

    const player = players[0];
    return {
      playerId: player.playerId,
      discordId: player.discordAccounts[0].discordId,
      discordUsername: player.discordAccounts[0].username,
      discordAvatar: player.discordAccounts[0].avatar,
      isGuildMember: player.discordAccounts[0].isGuildMember,
      steamId: player.steamAccounts?.[0]?.steamId || "N/A",
      steamUsername: player.steamAccounts?.[0]?.username || "N/A",
      createdDate: player.createdDate,
      source: 'steamcord'
    };
  } catch (error) {
    console.error(`❌ Failed to fetch user from Steamcord API: ${error.message}`);
    return null;
  }
}

/**
 * Fetches user information from PlatformSync API
 * @param {string} discordId - Discord ID of the user
 * @returns {Promise<Object|null>} User information or null if not found
 */
async function fetchUserFromPlatformSync(discordId) {
  try {
    const url = `${config.LinkingSystem?.PlatformSyncUrl || 'https://api.platformsync.net'}/v1/users/discord/${discordId}`;
    console.log(`Fetching from PlatformSync: ${url}`);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.LinkingSystem?.PlatformSyncApiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.log(`Error Body: ${errorBody}`);
      throw new Error(`PlatformSync API error: ${response.status} - ${errorBody}`);
    }

    const userData = await response.json();
    if (!userData || !userData.data) {
      console.log(`No user found for Discord ID: ${discordId}`);
      return null;
    }

    return {
      discordId: userData.data.discordId,
      discordUsername: userData.data.discordUsername,
      discordAvatar: userData.data.discordAvatar,
      steamId: userData.data.steamId || "N/A",
      steamUsername: userData.data.steamUsername || "N/A",
      createdDate: userData.data.createdAt,
      source: 'platformsync'
    };
  } catch (error) {
    console.error(`❌ Failed to fetch user from PlatformSync API: ${error.message}`);
    return null;
  }
}

/**
 * Fetches user information from custom linking system
 * @param {string} discordId - Discord ID of the user
 * @returns {Promise<Object|null>} User information or null if not found
 */
async function fetchUserFromCustomSystem(discordId) {
  try {
    const url = `${config.LinkingSystem?.WEBSITE_URL || 'https://link.example.com/'}api?action=findByDiscord&id=${discordId}&secret=${config.LinkingSystem?.SECRET_KEY || config.SECRET_KEY}`;
    console.log(`Fetching from custom linking system: ${url}`);
    const response = await fetch(url);

    if (!response.ok) {
      const errorBody = await response.text();
      console.log(`Error Body: ${errorBody}`);
      throw new Error(`Custom linking system error: ${response.status} - ${errorBody}`);
    }

    const responseText = await response.text();

    // Check if the response indicates no user found
    if (responseText.toLowerCase().includes("no users") || responseText.toLowerCase().includes("not found")) {
      console.log(`No user found for Discord ID: ${discordId}`);
      return null;
    }

    try {
      // Try to parse as JSON first
      const userData = JSON.parse(responseText);
      return {
        discordId: discordId,
        discordUsername: userData.discord_name || userData.discord_username || "Unknown",
        steamId: userData.steam_id || "N/A",
        steamUsername: userData.steam_name || "N/A",
        source: 'custom'
      };
    } catch (e) {
      // If not JSON, try to extract Steam ID from text response
      // This is for backward compatibility with systems that just return the Steam ID
      if (responseText.match(/^[0-9]{17}$/)) {
        // Get user info from Discord
        const discordUser = await fetchDiscordUserInfo(discordId);
        return {
          discordId: discordId,
          discordUsername: discordUser?.username || "Unknown",
          discordAvatar: discordUser?.avatarURL || null,
          steamId: responseText,
          steamUsername: "Unknown", // We don't have this information
          source: 'custom'
        };
      }
      console.error(`❌ Failed to parse response from custom linking system: ${e.message}`);
      return null;
    }
  } catch (error) {
    console.error(`❌ Failed to fetch user from custom linking system: ${error.message}`);
    return null;
  }
}

/**
 * Fetches user information from Discord
 * @param {string} discordId - Discord ID of the user
 * @returns {Promise<Object|null>} User information or null if not found
 */
async function fetchDiscordUserInfo(discordId) {
  try {
    // This function would use the Discord.js client to fetch user info
    // This is a placeholder - the actual implementation would depend on how you access the Discord client
    // In a real implementation, you would use client.users.fetch(discordId)
    return null;
  } catch (error) {
    console.error(`❌ Failed to fetch Discord user info: ${error.message}`);
    return null;
  }
}

module.exports = {
  fetchUserFromLinkingSystem,
  fetchUserFromSteamcord,
  fetchUserFromPlatformSync,
  fetchUserFromCustomSystem
};
