/**
 * Utility functions for mapping roles between servers
 */

/**
 * Maps a role from the source guild to the target guild based on role name
 * @param {string} roleID - The role ID in the source guild
 * @param {Guild} sourceGuild - The source Discord guild
 * @param {Guild} targetGuild - The target Discord guild
 * @returns {Role|null} - The mapped role in the target guild, or null if not found
 */
function mapRoleByName(roleID, sourceGuild, targetGuild) {
    try {
        // Try to get the role from the source guild
        const sourceRole = sourceGuild.roles.cache.get(roleID);
        
        if (sourceRole) {
            // Look for a role with the same name in the target guild
            const targetRole = targetGuild.roles.cache.find(r => r.name === sourceRole.name);
            
            if (targetRole) {
                return targetRole;
            }
        }
        
        // If we couldn't find a matching role, try to use the original role ID
        // This allows for manually setting the same role IDs in both servers
        const directRole = targetGuild.roles.cache.get(roleID);
        if (directRole) {
            return directRole;
        }
    } catch (error) {
        console.error(`Error mapping role ${roleID}: ${error.message}`);
    }
    
    return null;
}

/**
 * Maps multiple roles from the source guild to the target guild
 * @param {Array<string>} roleIDs - Array of role IDs in the source guild
 * @param {Guild} sourceGuild - The source Discord guild
 * @param {Guild} targetGuild - The target Discord guild
 * @returns {Array<Role>} - Array of mapped roles in the target guild
 */
function mapRoles(roleIDs, sourceGuild, targetGuild) {
    if (!Array.isArray(roleIDs) || roleIDs.length === 0) {
        return [];
    }
    
    const mappedRoles = [];
    
    for (const roleID of roleIDs) {
        if (typeof roleID === "string" && roleID.trim() !== "") {
            const mappedRole = mapRoleByName(roleID, sourceGuild, targetGuild);
            if (mappedRole) {
                mappedRoles.push(mappedRole);
            }
        }
    }
    
    return mappedRoles;
}

/**
 * Creates permission overwrites for roles
 * @param {Array<string>} roleIDs - Array of role IDs in the source guild
 * @param {Guild} sourceGuild - The source Discord guild
 * @param {Guild} targetGuild - The target Discord guild
 * @param {boolean} useSeperateServer - Whether we're using a separate server
 * @returns {Array<Object>} - Array of permission overwrite objects
 */
function createRolePermissionOverwrites(roleIDs, sourceGuild, targetGuild, useSeperateServer) {
    const permissionOverwrites = [];
    
    if (!Array.isArray(roleIDs) || roleIDs.length === 0) {
        return permissionOverwrites;
    }
    
    if (useSeperateServer) {
        // For each role, try to map it to the target guild
        for (const roleID of roleIDs) {
            if (typeof roleID === "string" && roleID.trim() !== "") {
                const mappedRole = mapRoleByName(roleID, sourceGuild, targetGuild);
                
                if (mappedRole) {
                    permissionOverwrites.push({
                        id: mappedRole.id,
                        allow: ['SendMessages', 'ViewChannel', 'UseApplicationCommands']
                    });
                } else {
                    // If we couldn't map the role, still try to use the original ID
                    permissionOverwrites.push({
                        id: roleID,
                        allow: ['SendMessages', 'ViewChannel', 'UseApplicationCommands']
                    });
                }
            }
        }
    } else {
        // If we're not using a separate server, just add the roles directly
        roleIDs.forEach(roleID => {
            if (typeof roleID === "string" && roleID.trim() !== "") {
                permissionOverwrites.push({
                    id: roleID,
                    allow: ['SendMessages', 'ViewChannel', 'UseApplicationCommands']
                });
            }
        });
    }
    
    return permissionOverwrites;
}

module.exports = {
    mapRoleByName,
    mapRoles,
    createRolePermissionOverwrites
};
