const fs = require('fs');
const yaml = require('js-yaml');

/**
 * Config adapter to provide backward compatibility with the new config structure
 * This allows existing code to continue working with the new nested config format
 */
class ConfigAdapter {
    constructor() {
        this.config = null;
        this.loadConfig();
    }

    /**
     * Load the config file
     */
    loadConfig() {
        try {
            const configFile = fs.readFileSync('./config.yml', 'utf8');
            this.config = yaml.load(configFile);
            this.setupCompatibilityLayer();
        } catch (error) {
            console.error('Error loading config:', error);
            process.exit(1);
        }
    }

    /**
     * Set up compatibility layer to map new config structure to old flat structure
     */
    setupCompatibilityLayer() {
        // Map BotSettings properties
        if (this.config.BotSettings) {
            Object.keys(this.config.BotSettings).forEach(key => {
                this.config[key] = this.config.BotSettings[key];
            });
        }

        // Map Database properties
        if (this.config.Database) {
            Object.keys(this.config.Database).forEach(key => {
                this.config[key] = this.config.Database[key];
            });
        }

        // Map APIKeys properties
        if (this.config.APIKeys) {
            Object.keys(this.config.APIKeys).forEach(key => {
                this.config[key] = this.config.APIKeys[key];
            });
        }

        // Map Channels properties
        if (this.config.Channels) {
            Object.keys(this.config.Channels).forEach(key => {
                this.config[key] = this.config.Channels[key];
            });
        }

        // Map Webhooks properties
        if (this.config.Webhooks) {
            Object.keys(this.config.Webhooks).forEach(key => {
                this.config[key] = this.config.Webhooks[key];
            });
        }

        // Map LinkingSystem properties
        if (this.config.LinkingSystem) {
            Object.keys(this.config.LinkingSystem).forEach(key => {
                this.config[key] = this.config.LinkingSystem[key];
            });
        }

        // Map DeveloperIDs properties
        if (this.config.DeveloperIDs) {
            Object.keys(this.config.DeveloperIDs).forEach(key => {
                this.config[key] = this.config.DeveloperIDs[key];
            });
        }

        // Map GuildSettings properties
        if (this.config.GuildSettings) {
            // Map nested properties
            if (this.config.GuildSettings.guildColors) {
                this.config.guildColors = this.config.GuildSettings.guildColors;
            }
            if (this.config.GuildSettings.RoleCheck !== undefined) {
                this.config.RoleCheck = this.config.GuildSettings.RoleCheck;
            }
            if (this.config.GuildSettings.guildRoleMapping) {
                this.config.guildRoleMapping = this.config.GuildSettings.guildRoleMapping;
            }
            if (this.config.GuildSettings.guildWelcomeLogMapping) {
                this.config.guildWelcomeLogMapping = this.config.GuildSettings.guildWelcomeLogMapping;
            }
            if (this.config.GuildSettings.guildBlacklistMapping) {
                this.config.guildBlacklistMapping = this.config.GuildSettings.guildBlacklistMapping;
            }
            if (this.config.GuildSettings.SuggestionChannels) {
                this.config.SuggestionChannels = this.config.GuildSettings.SuggestionChannels;
            }
        }

        // Map Servers
        if (this.config.Servers) {
            this.config.Servers = this.config.Servers;
        }

        // Map BotActivitySettings
        if (this.config.BotActivitySettings) {
            this.config.BotActivitySettings = this.config.BotActivitySettings;
        }

        // Map TicketSystem properties
        if (this.config.TicketSystem) {
            // Map Panel settings
            if (this.config.TicketSystem.Panel && this.config.TicketSystem.Panel.Embed) {
                this.config.TicketPanelSettings = {
                    Embed: this.config.TicketSystem.Panel.Embed
                };
            }

            // Map Settings
            if (this.config.TicketSystem.Settings) {
                this.config.TicketSettings = this.config.TicketSystem.Settings;
            }

            // Map Transcripts
            if (this.config.TicketSystem.Transcripts) {
                this.config.TicketTranscriptSettings = this.config.TicketSystem.Transcripts;
            }

            // Map ClaimingSystem
            if (this.config.TicketSystem.ClaimingSystem) {
                this.config.ClaimingSystem = this.config.TicketSystem.ClaimingSystem;
            }

            // Map ArchiveSystem
            if (this.config.TicketSystem.ArchiveSystem) {
                this.config.ArchiveTickets = this.config.TicketSystem.ArchiveSystem;
            }

            // Map AlertSystem
            if (this.config.TicketSystem.AlertSystem) {
                this.config.TicketAlert = this.config.TicketSystem.AlertSystem;
            }

            // Map WorkingHours
            if (this.config.TicketSystem.WorkingHours) {
                this.config.WorkingHours = this.config.TicketSystem.WorkingHours;
            }

            // Map Logs
            if (this.config.TicketSystem.Logs) {
                Object.keys(this.config.TicketSystem.Logs).forEach(key => {
                    this.config[key] = this.config.TicketSystem.Logs[key];
                });
            }

            // Map CDN
            if (this.config.TicketSystem.CDN) {
                this.config.cdn = this.config.TicketSystem.CDN;
            }
        }

        // Map TicketAppearance
        if (this.config.TicketAppearance && this.config.TicketAppearance.OpenEmbed) {
            this.config.TicketOpenEmbed = this.config.TicketAppearance.OpenEmbed;
        }

        // Map TicketFeedback properties
        if (this.config.TicketFeedback) {
            // Map UserCloseDM
            if (this.config.TicketFeedback.UserCloseDM) {
                this.config.TicketUserCloseDM = this.config.TicketFeedback.UserCloseDM;
            }

            // Map ReviewSystem
            if (this.config.TicketFeedback.ReviewSystem) {
                this.config.TicketReviewSettings = this.config.TicketFeedback.ReviewSystem;
            }

            // Map ReviewRequirements
            if (this.config.TicketFeedback.ReviewRequirements) {
                this.config.TicketReviewRequirements = this.config.TicketFeedback.ReviewRequirements;
            }

            // Map ReviewChannel
            if (this.config.TicketFeedback.ReviewChannel) {
                this.config.ReviewChannel = this.config.TicketFeedback.ReviewChannel;
            }
        }

        // Map PaymentSystems properties
        if (this.config.PaymentSystems) {
            // Map PayPal
            if (this.config.PaymentSystems.PayPal) {
                this.config.PayPalSettings = this.config.PaymentSystems.PayPal;
            }

            // Map Stripe
            if (this.config.PaymentSystems.Stripe) {
                this.config.StripeSettings = this.config.PaymentSystems.Stripe;
            }

            // Map Crypto
            if (this.config.PaymentSystems.Crypto) {
                this.config.CryptoSettings = this.config.PaymentSystems.Crypto;
                
                // Map nested Crypto properties
                if (this.config.PaymentSystems.Crypto.Rates) {
                    this.config.CryptoRates = this.config.PaymentSystems.Crypto.Rates;
                }
                if (this.config.PaymentSystems.Crypto.Addresses) {
                    this.config.CryptoAddresses = this.config.PaymentSystems.Crypto.Addresses;
                }
            }
        }

        // Map SuggestionSystem properties
        if (this.config.SuggestionSystem) {
            // Map Settings
            if (this.config.SuggestionSystem.Settings) {
                this.config.SuggestionSettings = this.config.SuggestionSystem.Settings;
            }

            // Map Statuses
            if (this.config.SuggestionSystem.Statuses) {
                this.config.SuggestionStatuses = this.config.SuggestionSystem.Statuses;
            }

            // Map Colors
            if (this.config.SuggestionSystem.Colors) {
                this.config.SuggestionStatusesEmbedColors = this.config.SuggestionSystem.Colors;
            }

            // Map Buttons
            if (this.config.SuggestionSystem.Buttons) {
                if (this.config.SuggestionSystem.Buttons.Upvote) {
                    this.config.SuggestionUpvote = this.config.SuggestionSystem.Buttons.Upvote;
                }
                if (this.config.SuggestionSystem.Buttons.Downvote) {
                    this.config.SuggestionDownvote = this.config.SuggestionSystem.Buttons.Downvote;
                }
                if (this.config.SuggestionSystem.Buttons.ResetVote) {
                    this.config.SuggestionResetvote = this.config.SuggestionSystem.Buttons.ResetVote;
                }
                if (this.config.SuggestionSystem.Buttons.Accept) {
                    this.config.SuggestionAccept = this.config.SuggestionSystem.Buttons.Accept;
                }
                if (this.config.SuggestionSystem.Buttons.Deny) {
                    this.config.SuggestionDeny = this.config.SuggestionSystem.Buttons.Deny;
                }
            }
        }

        // Map ChannelStats properties
        if (this.config.ChannelStats) {
            if (this.config.ChannelStats.Tickets && this.config.ChannelStats.Tickets.Total) {
                this.config.TotalTickets = this.config.ChannelStats.Tickets.Total;
            }
            if (this.config.ChannelStats.Tickets && this.config.ChannelStats.Tickets.Open) {
                this.config.OpenTickets = this.config.ChannelStats.Tickets.Open;
            }
            if (this.config.ChannelStats.Rating && this.config.ChannelStats.Rating.Average) {
                this.config.AverageRating = this.config.ChannelStats.Rating.Average;
            }
            if (this.config.ChannelStats.Members && this.config.ChannelStats.Members.Total) {
                this.config.TotalMembers = this.config.ChannelStats.Members.Total;
            }
            if (this.config.ChannelStats.Requests && this.config.ChannelStats.Requests.Total) {
                this.config.TotalRequests = this.config.ChannelStats.Requests.Total;
            }
        }

        // Map Locale
        if (this.config.Locale) {
            this.config.Locale = this.config.Locale;
        }

        // Map HelpCommand
        if (this.config.HelpCommand) {
            // Flatten the structure
            const flatHelpCommand = {};
            
            if (this.config.HelpCommand.Appearance) {
                Object.keys(this.config.HelpCommand.Appearance).forEach(key => {
                    flatHelpCommand[key] = this.config.HelpCommand.Appearance[key];
                });
            }
            
            if (this.config.HelpCommand.Categories) {
                if (this.config.HelpCommand.Categories.General) {
                    flatHelpCommand.GeneralCategory = this.config.HelpCommand.Categories.General;
                }
                if (this.config.HelpCommand.Categories.Ticket) {
                    flatHelpCommand.TicketCategory = this.config.HelpCommand.Categories.Ticket;
                }
                if (this.config.HelpCommand.Categories.Utility) {
                    flatHelpCommand.UtilityCategory = this.config.HelpCommand.Categories.Utility;
                }
            }
            
            this.config.HelpCommand = flatHelpCommand;
        }

        // Map ButtonCustomization
        if (this.config.ButtonCustomization) {
            if (this.config.ButtonCustomization.Emojis) {
                this.config.ButtonEmojis = this.config.ButtonCustomization.Emojis;
            }
            if (this.config.ButtonCustomization.Colors) {
                this.config.ButtonColors = this.config.ButtonCustomization.Colors;
            }
        }

        // Map DatabaseMaintenance
        if (this.config.DatabaseMaintenance && this.config.DatabaseMaintenance.AutoCleanup) {
            this.config.cleanUpData = this.config.DatabaseMaintenance.AutoCleanup;
        }

        // Map ticket buttons (TicketButton1, TicketButton2, etc.)
        for (let i = 1; i <= 8; i++) {
            const buttonKey = `TicketButton${i}`;
            if (this.config[buttonKey]) {
                // Keep the existing button config
            }
        }
    }

    /**
     * Get the config with compatibility layer
     */
    getConfig() {
        return this.config;
    }
}

// Create a singleton instance
const configAdapter = new ConfigAdapter();

// Export the config with compatibility layer
module.exports = configAdapter.getConfig();
