const BattleMetricsClient = require('./battlemetricsClient');
const SpectateRequest = require('../models/spectateRequestModel');
const config = require('./configAdapter');

/**
 * Utility functions for spectate requests
 */
class SpectateRequestUtils {
    /**
     * Create a new spectate request
     * @param {string} steamId - The Steam ID of the player
     * @param {string} reason - The reason for the spectate request
     * @param {string} requestedBy - Discord ID of the user who requested the spectate
     * @returns {Promise<Object>} - Result of the operation
     */
    static async createSpectateRequest(steamId, reason, requestedBy) {
        try {
            // Check if there's already a pending spectate request for this player
            const existingRequest = await SpectateRequest.findOne({
                steamId,
                status: 'pending'
            });

            if (existingRequest) {
                return {
                    success: false,
                    message: 'There is already a pending spectate request for this player'
                };
            }

            // Search for the player on BattleMetrics
            const searchResult = await BattleMetricsClient.searchPlayerBySteamId(steamId);

            let battlemetricsId = null;
            let playerName = 'Unknown';
            let playerDetails = null;

            if (searchResult.status === 200 && searchResult.dict.data && searchResult.dict.data.length > 0) {
                const player = searchResult.dict.data[0];
                battlemetricsId = player.id;
                playerName = player.attributes.name || 'Unknown';

                // Get detailed player information
                const detailedInfo = await BattleMetricsClient.getDetailedPlayerInfo(battlemetricsId);
                if (detailedInfo.success) {
                    playerDetails = detailedInfo.data;
                }
            }

            // Create spectate request
            const spectateRequest = new SpectateRequest({
                steamId,
                battlemetricsId,
                playerName,
                reason,
                requestedBy,
                playerDetails
            });

            await spectateRequest.save();

            return {
                success: true,
                message: 'Spectate request submitted successfully',
                data: spectateRequest
            };
        } catch (error) {
            console.error('Error creating spectate request:', error);
            return {
                success: false,
                message: `Error creating spectate request: ${error.message}`
            };
        }
    }

    /**
     * Get all pending spectate requests
     * @returns {Promise<Object>} - Result of the operation
     */
    static async getPendingSpectateRequests() {
        try {
            const requests = await SpectateRequest.find({ status: 'pending' })
                .sort({ requestedAt: 1 });

            return {
                success: true,
                data: requests
            };
        } catch (error) {
            console.error('Error getting pending spectate requests:', error);
            return {
                success: false,
                message: `Error getting pending spectate requests: ${error.message}`
            };
        }
    }

    /**
     * Get a user's spectate requests
     * @param {string} userId - Discord ID of the user
     * @returns {Promise<Object>} - Result of the operation
     */
    static async getUserSpectateRequests(userId) {
        try {
            const requests = await SpectateRequest.find({ requestedBy: userId })
                .sort({ requestedAt: -1 });

            return {
                success: true,
                data: requests
            };
        } catch (error) {
            console.error('Error getting user spectate requests:', error);
            return {
                success: false,
                message: `Error getting spectate requests: ${error.message}`
            };
        }
    }

    /**
     * Review a spectate request
     * @param {string} requestId - The ID of the spectate request
     * @param {string} status - The new status ('approved' or 'rejected')
     * @param {string} reviewedBy - Discord ID of the staff member reviewing the request
     * @param {string} reviewNotes - Notes from the reviewer
     * @returns {Promise<Object>} - Result of the operation
     */
    static async reviewSpectateRequest(requestId, status, reviewedBy, reviewNotes) {
        try {
            const spectateRequest = await SpectateRequest.findById(requestId);

            if (!spectateRequest) {
                return {
                    success: false,
                    message: 'Spectate request not found'
                };
            }

            if (spectateRequest.status !== 'pending') {
                return {
                    success: false,
                    message: `This spectate request has already been ${spectateRequest.status}`
                };
            }

            spectateRequest.status = status;
            spectateRequest.reviewedBy = reviewedBy;
            spectateRequest.reviewedAt = new Date();
            spectateRequest.reviewNotes = reviewNotes || '';

            await spectateRequest.save();

            return {
                success: true,
                message: `Spectate request ${status}`,
                data: spectateRequest
            };
        } catch (error) {
            console.error('Error reviewing spectate request:', error);
            return {
                success: false,
                message: `Error reviewing spectate request: ${error.message}`
            };
        }
    }
}

module.exports = SpectateRequestUtils;
