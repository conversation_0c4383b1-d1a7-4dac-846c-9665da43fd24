const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EmbedBuilder, WebhookClient } = require('discord.js');
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('./utils/configAdapter')
const logHook = config.systemLogHook ? new WebhookClient({ url: config.systemLogHook }) : null;
const path = require('path');
const mainFile = path.join(__dirname, 'index.js');
const manager = new ShardingManager(mainFile, { totalShards: 'auto', token: config.Token, respawn: true, mode: 'worker' });
manager.spawn();

// ✅ Log when a shard is created
manager.on("shardCreate", (shard) => {
    const shardEmbed = new EmbedBuilder()
        .setTitle("Shard Created")
        .setDescription(`**Shard ID:** \`${shard.id}\`\n**Spawning...**`)
        .setColor("Green")
        .setTimestamp();

    logHook.send({ embeds: [shardEmbed] });

    // ✅ Log when a shard is ready
    manager.once("ready", () => {
        const readyEmbed = new EmbedBuilder()
            .setTitle("Shard Ready")
            .setDescription(`**Shard ID:** \`${shard.id}\` is now online! 🚀`)
            .setColor("Blue")
            .setTimestamp();

        logHook.send({ embeds: [readyEmbed] });
    });

    // ✅ Log if a shard disconnects
    manager.on("disconnect", (event) => {
        const disconnectEmbed = new EmbedBuilder()
            .setTitle("Shard Disconnected")
            .setDescription(`**Shard ID:** \`${shard.id}\` lost connection.`)
            .setColor("Orange")
            .setTimestamp();

        logHook.send({ embeds: [disconnectEmbed] });
    });

    // ✅ Log when a shard attempts to reconnect
    manager.on("reconnecting", () => {
        const reconnectEmbed = new EmbedBuilder()
            .setTitle("Shard Reconnecting")
            .setDescription(`**Shard ID:** \`${shard.id}\` is attempting to reconnect.`)
            .setColor("Yellow")
            .setTimestamp();

        logHook.send({ embeds: [reconnectEmbed] });
    });

    // ✅ Log if a shard encounters an error
    shard.on("error", (error) => {
        console.error(`❌ Shard ${shard.id} encountered an error:`, error);

        const errorEmbed = new EmbedBuilder()
            .setTitle("❌ Shard Error")
            .setDescription(`**Shard ID:** \`${shard.id}\`\n\`\`\`${error}\`\`\``)
            .setColor("Red")
            .setTimestamp();

        logHook.send({ embeds: [errorEmbed] });
    });
});