# Manager Bot

A comprehensive Discord bot for managing tickets, bans, and more.

## Setup Instructions

### Quick Setup

1. Run the setup script to configure the bot:
   ```
   node setup.js
   ```

2. Follow the prompts to configure your bot settings, database connection, API keys, and webhooks.

3. Start the bot:
   ```
   node index.js
   ```

### Manual Setup

If you prefer to set up the bot manually, you can edit the `config.yml` file directly. The file is organized into the following sections:

1. **Core Bot Settings** - Basic bot configuration
2. **Database Settings** - MongoDB connection
3. **API Keys & Tokens** - Various API keys for integrations
4. **Channel & Category IDs** - Discord channel and category IDs
5. **Webhook Configuration** - Discord webhooks for various notifications
6. **Linking System Settings** - Website integration settings
7. **Guild Specific Settings** - Server-specific configurations
8. **Bot Activity Settings** - Bot status messages
9. **Ticket System Configuration** - Ticket system settings
10. **Payment Systems** - PayPal, Stripe, and Crypto payment settings
11. **Suggestion System** - Suggestion system configuration
12. **Channel Stats Configuration** - Stats channel settings
13. **Locale Settings** - Customizable messages
14. **Button Customization** - Button appearance settings
15. **Database Maintenance** - Database cleanup settings

## Features

- **Ticket System** - Comprehensive ticket management system
- **Ban Monitoring** - Monitor and log BattleMetrics bans
- **Payment Integration** - PayPal, Stripe, and Cryptocurrency payment options
- **Suggestion System** - User suggestion system with voting
- **Statistics** - Track and display various statistics
- **Customizable** - Fully customizable messages and appearance

## Support

If you need help with the bot, please contact the developer.

## License

This bot is proprietary software. Unauthorized distribution is prohibited.
