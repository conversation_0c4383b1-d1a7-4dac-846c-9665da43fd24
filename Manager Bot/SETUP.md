# Discord Bot Setup Guide

This guide will help you set up the Discord bot with automatic restart capabilities and memory optimization.

## Running the Bot

### Option 1: Using the Start Script (Recommended)

The bot comes with an auto-restart script that will automatically restart the bot if it crashes.

1. Run the bot using the start script:
   ```
   node --expose-gc start-bot.js
   ```

   Or use the batch file on Windows:
   ```
   start-bot.bat
   ```

2. The bot will automatically restart if it crashes due to memory issues or other errors.

### Option 2: Using Systemd (Linux Only)

For Linux servers, you can use systemd to ensure the bot starts automatically when the server boots and restarts if it crashes.

1. Copy the `discord-bot.service` file to the systemd directory:
   ```
   sudo cp discord-bot.service /etc/systemd/system/
   ```

2. Edit the service file to match your installation path:
   ```
   sudo nano /etc/systemd/system/discord-bot.service
   ```

   Update the `WorkingDirectory` and `ExecStart` paths as needed.

3. Reload systemd to recognize the new service:
   ```
   sudo systemctl daemon-reload
   ```

4. Enable the service to start on boot:
   ```
   sudo systemctl enable discord-bot.service
   ```

5. Start the service:
   ```
   sudo systemctl start discord-bot.service
   ```

6. Check the status:
   ```
   sudo systemctl status discord-bot.service
   ```

## Memory Optimization

The bot includes several memory optimization features:

1. **Automatic Garbage Collection**: The bot will automatically run garbage collection periodically to free up memory.

2. **Memory Monitoring**: The bot monitors memory usage and logs it to files in the `logs` directory.

3. **Memory Leak Detection**: The bot detects potential memory leaks and takes heap snapshots when necessary.

4. **Cache Cleanup**: The bot regularly cleans up caches to prevent memory buildup.

5. **Optimized Timers**: Timers have been optimized to run less frequently to reduce memory usage.

## Troubleshooting

### Memory Issues

If the bot is using too much memory:

1. Check the memory logs in the `logs` directory.
2. Look for heap snapshots in the `heapdumps` directory.
3. Increase the restart delay in `start-bot.js` if the bot is restarting too frequently.

### Crash Logs

Crash logs are stored in:
- `logs/bot-restarts.log` - For crashes handled by the auto-restart script
- `logs.manager` - For general bot logs
- `logs/memory-leak-detection.log` - For memory leak detection logs
- `logs/system-metrics.log` - For system metrics logs

## Advanced Configuration

### Memory Limits

The bot is now configured to use 16GB of RAM by default, which is appropriate for a system with 64GB of RAM.

You can adjust the memory limits in these files:

1. `start-bot.js`:
```javascript
const botProcess = spawn('node', ['--max-old-space-size=16384', '--expose-gc', 'index.js'], {...});
```

2. `run-with-memory.bat` or `run-with-memory.sh`:
```
node --max-old-space-size=16384 --expose-gc index.js
```

3. `discord-bot.service`:
```
ExecStart=/usr/bin/node --max-old-space-size=16384 --expose-gc start-bot.js
```

Adjust the value (16384 = 16GB) based on your available RAM. For a system with 64GB of RAM, you could safely go up to 32GB (32768) if needed.

### Garbage Collection Frequency

You can adjust how often garbage collection runs by modifying the `GC_INTERVAL` in `utils/memoryOptimizer.js`.

### Log Rotation

Log files are automatically rotated when they reach 10MB in size. You can adjust this limit in the respective files:
- `utils/memoryMonitor.js`
- `utils/memoryLeakDetector.js`
- `utils/memoryOptimizer.js`
- `start-bot.js`
