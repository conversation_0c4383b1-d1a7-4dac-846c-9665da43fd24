const mongoose = require('mongoose');

/**
 * Schema for storing ban monitoring configuration
 */
const banMonitorConfigSchema = new mongoose.Schema({
    enabled: { type: Boolean, default: true },
    banListId: { type: String, required: true },
    checkInterval: { type: Number, default: 60000 }, // milliseconds
    lastChecked: { type: Date, default: Date.now },
    staffWebhooks: [{ type: String }],
    publicWebhooks: [{ type: String }],
    embedColor: { type: String, default: "#CCCCCC" },
    serverName: { type: String, default: "Server" }
});

module.exports = mongoose.model('BanMonitorConfig', banMonitorConfigSchema);
