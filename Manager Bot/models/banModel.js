const mongoose = require('mongoose');

/**
 * Schema for storing BattleMetrics ban IDs to prevent duplicate notifications
 */
const banSchema = new mongoose.Schema({
    banId: { type: String, required: true, unique: true },
    playerBMId: { type: String, required: true },
    steamId: { type: String, default: "Unknown" },
    reason: { type: String, default: "No reason provided" },
    serverName: { type: String, default: "Unknown Server" },
    expiresAt: { type: Date, default: null },
    createdAt: { type: Date, default: Date.now },
    processed: { type: Boolean, default: true }
});

module.exports = mongoose.model('BattleMetricsBan', banSchema);
