const mongoose = require("mongoose");

const SuggestionSchema = new mongoose.Schema({
    messageID: { type: String, required: true },
    userID: { type: String, required: true },
    guildID: { type: String, required: true },
    channelID: { type: String, required: true },
    suggestion: { type: String, required: true },
    status: { type: String, default: "Open" }, // Open / Closed
    upvotes: { type: Number, default: 0 }, // ✅ Track 👍 reactions
    downvotes: { type: Number, default: 0 } // ✅ Track 👎 reactions
});

module.exports = mongoose.model("Suggestion", SuggestionSchema);
