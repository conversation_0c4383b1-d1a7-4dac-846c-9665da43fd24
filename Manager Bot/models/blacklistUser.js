const mongoose = require("mongoose");

const blacklistSchema = new mongoose.Schema({
    userID: { type: String, required: true, unique: true },
    reason: { type: String, default: "No reason provided" },
    alreadyLogged: { type: Boolean, default: false },
    userWhoBlacklisted: { type: String, default: "N/A" },
    blacklistedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model("Blacklist", blacklistSchema);
