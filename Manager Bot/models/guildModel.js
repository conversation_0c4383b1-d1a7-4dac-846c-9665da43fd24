const mongoose = require('mongoose');

const schema = new mongoose.Schema ({
    guildID: String,
    ticketCategory: String,
    ticketClosedCategory: String,
    ticketsDisabled: Boolean,
    aiSupport: Boolean,
    totalTickets: Number,
    openTickets: Number,
    totalClaims: Number,
    totalMessages: Number,
    totalSuggestions: Number,
    totalSuggestionUpvotes: Number,
    totalSuggestionDownvotes: Number,
    totalReviews: Number,
    averageRating: Number,
    timesBotStarted: Number,
    averageCompletion: String,
    reviews: [],
    panelChannelID: String,
    disabledMessageID: String,
    aiDisabledMessageID: String,
    lastUsedId: String,
});

module.exports = mongoose.model('guild', schema);