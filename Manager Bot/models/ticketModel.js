const mongoose = require('mongoose');

const schema = new mongoose.Schema ({
    guildID: String,
    sourceGuildID: String, // The guild where the ticket was requested (may be different from guildID)
    channelID: String,
    open: Boolean,
    userID: String,
    username: String,
    avatar: String,
    ticketType: String,
    button: String,
    msgID: String,
    claimed: Boolean,
    claimUser: String,
    messages: Number,
    lastMessageSent: Date,
    lastMessageSentUser: String,
    status: String,
    closeUserID: String,
    archiveMsgID: String,
    questions: [
        {
            customId: String,
            required: Boolean,
            question: String,
            style: String,
            response: String,
        },
    ],
    ticketCreationDate: Date,
    closedAt: Date,
    identifier: String,
    closeReason: String,
    closeNotificationTime: Number,
    closeNotificationMsgID: String,
    closeNotificationUserID: String,
    transcriptID: String,
    webhookToken: String,
    webhookID: String,
}, {
    timestamps: true,
});

module.exports = mongoose.model('ticket', schema);