const mongoose = require('mongoose');

const userDataSchema = new mongoose.Schema({
    userId: {
        type: String,
        required: true,
        unique: true,
    },
    messages: {
        type: Number,
        default: 0,
    },
    warns: {
        type: Number,
        default: 0,
    },
    timeouts: {
        type: Number,
        default: 0,
    },
    kicks: {
        type: Number,
        default: 0,
    },
    bans: {
        type: Number,
        default: 0,
    },
    note: {
        type: String,
        default: "None",
    },
});

const UserData = mongoose.model('UserData', userDataSchema);

module.exports = UserData;