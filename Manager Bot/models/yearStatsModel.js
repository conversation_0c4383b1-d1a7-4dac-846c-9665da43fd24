const mongoose = require('mongoose');

const yearMetricsSchema = new mongoose.Schema({
  timestamp: { type: Date },
  totalTickets: { type: Number, default: 0 },
  ticketsClosed: { type: Number, default: 0 },
  totalMessages: { type: Number, default: 0 },
  totalReviews: { type: Number, default: 0 },
  totalClaims: { type: Number, default: 0 },
  totalSuggestions: { type: Number, default: 0 },
  totalSuggestionUpvotes: { type: Number, default: 0 },
  totalSuggestionDownvotes: { type: Number, default: 0 },
  usersJoined: { type: Number, default: 0 },
  usersLeft: { type: Number, default: 0 },
  newBans: { type: Number, default: 0 },
  newRoles: { type: Number, default: 0 },
  authedUsers: { type: Number, default: 0 },
  totalRequests: { type: Number, default: 0 },

  totalMemory: { type: String, default: "" },
  freeMemory: { type: String, default: "" },
  usedMemory: { type: String, default: "" },
  activeMemory: { type: String, default: "" },
  availableMemory: { type: String, default: "" },

  uptime: { type: String, default: "" },
});

const yearlyStatsSchema = new mongoose.Schema({
  guildID: { type: String, default: `1188010953640521748` },
  yearStartDate: { type: Date, required: true, unique: true },
  yearlyMetrics: [yearMetricsSchema],
}, {
  timestamps: true, // %%__NONCE__%%
});
  

module.exports = mongoose.model('yearlyStat', yearlyStatsSchema);