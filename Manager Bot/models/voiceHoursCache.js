const mongoose = require('mongoose');

/**
 * Schema for caching voice hours data
 */
const voiceHoursCacheSchema = new mongoose.Schema({
    _id: { type: String, required: true }, // Discord ID as primary key
    join_time: { type: Number, required: true }, // Timestamp when user joined voice channel
    channel_id: { type: String, default: null } // Voice channel ID
});

module.exports = mongoose.model('VoiceHoursCache', voiceHoursCacheSchema);
