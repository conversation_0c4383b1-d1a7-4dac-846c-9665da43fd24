const mongoose = require('mongoose');

const UsersSchema = new mongoose.Schema({
    discord_id: { type: String, required: true, default: "N/A" },
    steam_id: { type: String, required: true, default: "N/A" },
    user_ip: { type: String, required: true, default: "N/A" },
    access_token: { type: String, required: true, default: "N/A" },
    refresh_token: { type: String, required: true, default: "N/A" },
    nitro: { type: Boolean, required: true, default: false },
    steam_name: { type: String, required: true, default: "N/A" },
    discord_name: { type: String, required: true, default: "N/A" },
    discord_pfp: { type: String, required: true, default: "N/A" },
    steam_pfp: { type: String, required: true, default: "N/A" },
    user_locale: { type: String, required: true, default: "N/A" },
    timestamp: { type: String, required: true, default: Math.floor(Date.now() / 1000).toString() },
    secure_token: { type: String, required: true },
    staff: { type: Boolean, required: true, default: false },
    
    // Other 
    sessions: [{
      token: { type: String, required: false },
      ip: { type: String, required: false },
      device: { type: String, required: false },
      created: { type: String, required: false },
      expires: { type: String, required: false }
    }],
  
    logs: [{
      action: { type: String, required: false },
      ip: { type: String, required: false },
      device: { type: String, required: false },
      created: { type: String, required: false }
    }],
  
    lastLogin: { type: String, required: false },
    lastUpdate: { type: String, required: false }
});

module.exports = mongoose.model('Users', UsersSchema);
