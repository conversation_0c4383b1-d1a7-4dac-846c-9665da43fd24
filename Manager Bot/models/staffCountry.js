const mongoose = require('mongoose');

/**
 * Schema for storing staff country information
 */
const staffCountrySchema = new mongoose.Schema({
    userID: { type: String, required: true, unique: true },
    country: { type: String, required: true, default: 'Unknown' },
    countryCode: { type: String, required: false },
    flag: { type: String, required: false }
});

module.exports = mongoose.model('StaffCountry', staffCountrySchema);
