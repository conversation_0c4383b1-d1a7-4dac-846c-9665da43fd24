const mongoose = require("mongoose");

const blacklistRequestSchema = new mongoose.Schema({
    userID: { type: String, required: true }, // Discord ID of the user being requested for blacklist
    reason: { type: String, required: true }, // Reason for blacklist
    evidence: { type: String, required: true }, // Link to evidence (screenshot, video, etc.)
    requestedBy: { type: String, required: true }, // Moderator who submitted the request
    status: { type: String, enum: ["pending", "accepted", "declined"], default: "pending" }, // Current request status
    timestamp: { type: Date, default: Date.now } // When the request was made
});

module.exports = mongoose.model("BlacklistRequest", blacklistRequestSchema);
