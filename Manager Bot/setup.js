const fs = require('fs');
const yaml = require('js-yaml');
const readline = require('readline');
const { promisify } = require('util');
const { exec } = require('child_process');
const execAsync = promisify(exec);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline.question
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

/**
 * Main setup function
 */
async function setup() {
  console.log('\n==========================================================');
  console.log('           MANAGER BOT CONFIGURATION SETUP');
  console.log('==========================================================\n');
  console.log('This script will help you set up your Manager Bot configuration.');
  console.log('You can press Enter to keep the default values (shown in brackets).\n');
  
  try {
    // Load the current config
    const configPath = './config.yml';
    const configExists = fs.existsSync(configPath);
    
    let config;
    if (configExists) {
      const configFile = fs.readFileSync(configPath, 'utf8');
      config = yaml.load(configFile);
      console.log('Existing configuration loaded successfully.\n');
    } else {
      console.log('No existing configuration found. Creating a new one.\n');
      config = {};
    }
    
    // Initialize config structure if needed
    if (!config.BotSettings) config.BotSettings = {};
    if (!config.Database) config.Database = {};
    if (!config.APIKeys) config.APIKeys = {};
    if (!config.Webhooks) config.Webhooks = {};
    if (!config.LinkingSystem) config.LinkingSystem = {};
    
    // Setup sections
    await setupBotSettings(config);
    await setupDatabase(config);
    await setupAPIKeys(config);
    await setupWebhooks(config);
    await setupLinkingSystem(config);
    
    // Save the config
    const newConfigYaml = yaml.dump(config, {
      lineWidth: -1,
      noRefs: true,
      quotingType: '"'
    });
    
    fs.writeFileSync(configPath, newConfigYaml, 'utf8');
    
    console.log('\n==========================================================');
    console.log('           CONFIGURATION SAVED SUCCESSFULLY');
    console.log('==========================================================\n');
    console.log('Your configuration has been saved to config.yml');
    console.log('You can now start the bot with: node index.js\n');
    
    // Ask if the user wants to install dependencies
    const installDeps = await question('Would you like to install dependencies now? (y/n) [y]: ');
    if (installDeps.toLowerCase() !== 'n') {
      console.log('\nInstalling dependencies...');
      try {
        await execAsync('npm install');
        console.log('Dependencies installed successfully!');
      } catch (error) {
        console.error('Error installing dependencies:', error.message);
      }
    }
    
  } catch (error) {
    console.error('Error during setup:', error);
  } finally {
    rl.close();
  }
}

/**
 * Setup bot settings
 */
async function setupBotSettings(config) {
  console.log('\n=== BOT SETTINGS ===\n');
  
  config.BotSettings.Token = await question(`Discord Bot Token ${config.BotSettings.Token ? '[Current: ' + maskToken(config.BotSettings.Token) + ']' : ''}: `);
  if (!config.BotSettings.Token) {
    console.log('Warning: Bot token is required for the bot to function.');
  }
  
  const botName = await question(`Bot Name ${config.BotSettings.BotName ? '[Current: ' + config.BotSettings.BotName + ']' : '[Manager Bot]'}: `);
  config.BotSettings.BotName = botName || config.BotSettings.BotName || 'Manager Bot';
  
  const prefix = await question(`Command Prefix ${config.BotSettings.prefix ? '[Current: ' + config.BotSettings.prefix + ']' : '[,]'}: `);
  config.BotSettings.prefix = prefix || config.BotSettings.prefix || ',';
  
  const guildID = await question(`Main Guild ID ${config.BotSettings.GuildID ? '[Current: ' + config.BotSettings.GuildID + ']' : ''}: `);
  config.BotSettings.GuildID = guildID || config.BotSettings.GuildID || '';
  
  const staffGuild = await question(`Staff Guild ID (can be same as Main Guild) ${config.BotSettings.StaffGuild ? '[Current: ' + config.BotSettings.StaffGuild + ']' : ''}: `);
  config.BotSettings.StaffGuild = staffGuild || config.BotSettings.StaffGuild || config.BotSettings.GuildID;
  
  const embedColor = await question(`Default Embed Color ${config.BotSettings.EmbedColors ? '[Current: ' + config.BotSettings.EmbedColors + ']' : '[#CCCCCC]'}: `);
  config.BotSettings.EmbedColors = embedColor || config.BotSettings.EmbedColors || '#CCCCCC';
  
  const staffRole = await question(`Staff Role ID ${config.BotSettings.StaffRole ? '[Current: ' + config.BotSettings.StaffRole + ']' : ''}: `);
  config.BotSettings.StaffRole = staffRole || config.BotSettings.StaffRole || '';
  
  const serverName = await question(`Server Name ${config.BotSettings.SERVER_NAME ? '[Current: ' + config.BotSettings.SERVER_NAME + ']' : ''}: `);
  config.BotSettings.SERVER_NAME = serverName || config.BotSettings.SERVER_NAME || '';
}

/**
 * Setup database settings
 */
async function setupDatabase(config) {
  console.log('\n=== DATABASE SETTINGS ===\n');
  
  const mongoURI = await question(`MongoDB URI ${config.Database.MongoURI ? '[Current: ' + maskMongoURI(config.Database.MongoURI) + ']' : ''}: `);
  config.Database.MongoURI = mongoURI || config.Database.MongoURI || '';
  
  if (!config.Database.MongoURI) {
    console.log('Warning: MongoDB URI is required for the bot to function.');
  }
}

/**
 * Setup API keys
 */
async function setupAPIKeys(config) {
  console.log('\n=== API KEYS ===\n');
  
  const steamApiKey = await question(`Steam API Key ${config.APIKeys.steam_api_key ? '[Current: ' + maskApiKey(config.APIKeys.steam_api_key) + ']' : ''}: `);
  config.APIKeys.steam_api_key = steamApiKey || config.APIKeys.steam_api_key || '';
  
  const bmApiToken = await question(`BattleMetrics API Token ${config.APIKeys.bm_api_token ? '[Current: ' + maskApiKey(config.APIKeys.bm_api_token) + ']' : ''}: `);
  config.APIKeys.bm_api_token = bmApiToken || config.APIKeys.bm_api_token || '';
  
  const bmOrgId = await question(`BattleMetrics Organization ID ${config.APIKeys.bm_org_id ? '[Current: ' + config.APIKeys.bm_org_id + ']' : ''}: `);
  config.APIKeys.bm_org_id = bmOrgId || config.APIKeys.bm_org_id || '';
  
  const bmBanListId = await question(`BattleMetrics Ban List ID ${config.APIKeys.bm_ban_list_id ? '[Current: ' + config.APIKeys.bm_ban_list_id + ']' : ''}: `);
  config.APIKeys.bm_ban_list_id = bmBanListId || config.APIKeys.bm_ban_list_id || '';
  
  const secretKey = await question(`Secret Key for API Authentication ${config.APIKeys.SECRET_KEY ? '[Current: ' + maskApiKey(config.APIKeys.SECRET_KEY) + ']' : ''}: `);
  config.APIKeys.SECRET_KEY = secretKey || config.APIKeys.SECRET_KEY || '';
}

/**
 * Setup webhooks
 */
async function setupWebhooks(config) {
  console.log('\n=== WEBHOOKS ===\n');
  
  console.log('Command Logs Webhook:');
  const logsWebhook = await question(`  Webhook URL ${config.Webhooks.LogsWebhook ? '[Current: ' + maskWebhook(config.Webhooks.LogsWebhook) + ']' : ''}: `);
  config.Webhooks.LogsWebhook = logsWebhook || config.Webhooks.LogsWebhook || '';
  
  console.log('\nError Logs Webhook:');
  const errorLogsHook = await question(`  Webhook URL ${config.Webhooks.ErrorLogsHook ? '[Current: ' + maskWebhook(config.Webhooks.ErrorLogsHook) + ']' : ''}: `);
  config.Webhooks.ErrorLogsHook = errorLogsHook || config.Webhooks.ErrorLogsHook || '';
  
  console.log('\nSystem Logs Webhook:');
  const systemLogHook = await question(`  Webhook URL ${config.Webhooks.systemLogHook ? '[Current: ' + maskWebhook(config.Webhooks.systemLogHook) + ']' : ''}: `);
  config.Webhooks.systemLogHook = systemLogHook || config.Webhooks.systemLogHook || '';
  
  console.log('\nAdmin Logs Webhook:');
  const adminLogHook = await question(`  Webhook URL ${config.Webhooks.adminLogHook ? '[Current: ' + maskWebhook(config.Webhooks.adminLogHook) + ']' : ''}: `);
  config.Webhooks.adminLogHook = adminLogHook || config.Webhooks.adminLogHook || '';
  
  console.log('\nStaff Logs Webhook:');
  const staffLogHook = await question(`  Webhook URL ${config.Webhooks.staffLogHook ? '[Current: ' + maskWebhook(config.Webhooks.staffLogHook) + ']' : ''}: `);
  config.Webhooks.staffLogHook = staffLogHook || config.Webhooks.staffLogHook || '';
  
  console.log('\nTicket Webhook:');
  const ticketWebhookURL = await question(`  Webhook URL ${config.Webhooks.ticketWebhookURL ? '[Current: ' + maskWebhook(config.Webhooks.ticketWebhookURL) + ']' : ''}: `);
  config.Webhooks.ticketWebhookURL = ticketWebhookURL || config.Webhooks.ticketWebhookURL || '';
  
  console.log('\nBan Staff Webhook:');
  const banStaffWebhook = await question(`  Webhook URL ${config.Webhooks.banStaffWebhook ? '[Current: ' + maskWebhook(config.Webhooks.banStaffWebhook) + ']' : ''}: `);
  config.Webhooks.banStaffWebhook = banStaffWebhook || config.Webhooks.banStaffWebhook || '';
  
  console.log('\nBan Public Webhook:');
  const banPublicWebhook = await question(`  Webhook URL ${config.Webhooks.banPublicWebhook ? '[Current: ' + maskWebhook(config.Webhooks.banPublicWebhook) + ']' : ''}: `);
  config.Webhooks.banPublicWebhook = banPublicWebhook || config.Webhooks.banPublicWebhook || '';
}

/**
 * Setup linking system
 */
async function setupLinkingSystem(config) {
  console.log('\n=== LINKING SYSTEM ===\n');
  
  const websiteURL = await question(`Website URL ${config.LinkingSystem.WEBSITE_URL ? '[Current: ' + config.LinkingSystem.WEBSITE_URL + ']' : ''}: `);
  config.LinkingSystem.WEBSITE_URL = websiteURL || config.LinkingSystem.WEBSITE_URL || '';
  
  const linkingWebsite = await question(`Linking Website URL ${config.LinkingSystem.linkingWebsite ? '[Current: ' + config.LinkingSystem.linkingWebsite + ']' : ''}: `);
  config.LinkingSystem.linkingWebsite = linkingWebsite || config.LinkingSystem.linkingWebsite || '';
  
  const logoURL = await question(`Logo URL ${config.LinkingSystem.LOGO_URL ? '[Current: ' + config.LinkingSystem.LOGO_URL + ']' : ''}: `);
  config.LinkingSystem.LOGO_URL = logoURL || config.LinkingSystem.LOGO_URL || '';
}

/**
 * Mask token for display
 */
function maskToken(token) {
  if (!token) return '';
  if (token.length <= 8) return '********';
  return token.substring(0, 4) + '...' + token.substring(token.length - 4);
}

/**
 * Mask API key for display
 */
function maskApiKey(key) {
  if (!key) return '';
  if (key.length <= 8) return '********';
  return key.substring(0, 4) + '...' + key.substring(key.length - 4);
}

/**
 * Mask MongoDB URI for display
 */
function maskMongoURI(uri) {
  if (!uri) return '';
  try {
    // Try to mask username and password in the URI
    const regex = /(mongodb(\+srv)?:\/\/)([^:]+):([^@]+)@/;
    return uri.replace(regex, '$1****:****@');
  } catch (error) {
    return '********';
  }
}

/**
 * Mask webhook URL for display
 */
function maskWebhook(webhook) {
  if (!webhook) return '';
  try {
    // Try to mask the webhook token
    const parts = webhook.split('/');
    if (parts.length >= 7) {
      const token = parts[6];
      parts[6] = token.substring(0, 4) + '...' + token.substring(token.length - 4);
      return parts.join('/');
    }
    return '********';
  } catch (error) {
    return '********';
  }
}

// Run the setup
setup().catch(console.error);
