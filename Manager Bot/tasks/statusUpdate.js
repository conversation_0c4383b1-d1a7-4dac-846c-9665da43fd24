/**
 * Status Update Task
 * 
 * Updates the bot's status message periodically.
 */

module.exports = {
  name: 'statusUpdate',
  description: 'Updates the bot status periodically',
  interval: 300000, // Run every 5 minutes (in milliseconds)
  enabled: true, // Enabled by default
  executeOnStart: true, // Run immediately when bot starts
  
  /**
   * Execute the task
   * @param {Client} client - Discord.js client
   */
  execute(client) {
    try {
      console.log('[STATUS] Updating bot status at ' + new Date().toLocaleString());
      
      // Set a random status message
      const statusMessages = [
        'Helping with tickets',
        'Managing support',
        'Ticket system online',
        'Need help? Open a ticket!'
      ];
      
      const randomStatus = statusMessages[Math.floor(Math.random() * statusMessages.length)];
      
      client.user.setActivity(randomStatus, { type: 'PLAYING' });
    } catch (error) {
      console.error(`[STATUS] Error updating status: ${error.message}`);
    }
  }
};
