const { Collection, Client, Discord, Intents, AttachmentBuilder, ActionRowBuilder, EmbedBuilder, ButtonBuilder, ApplicationCommandType, ContextMenuCommandBuilder, SlashCommandBuilder, WebhookClient } = require('discord.js');
const fs = require('fs');
const yaml = require("js-yaml")
const fetch = require("node-fetch");
// Use the config adapter for backward compatibility with the new config structure
const config = require('./utils/configAdapter')
const client = require("./index.js")
const color = require('ansi-colors');
const axios = require('axios')
const glob = require("glob");
const path = require('path');
const jwt = require('jsonwebtoken');
const webhookClient = config.LogsWebhook ? new WebhookClient({ url: config.LogsWebhook }) : null;
const adminLogs = config.adminLogHook ? new WebhookClient({ url: config.adminLogHook }) : null;
const Users = require('./models/Users')
let discordTranscripts;
if(config.TicketTranscriptSettings.TranscriptType === "HTML") discordTranscripts = require('discord-html-transcripts')

client.commands = new Collection();
client.slashCommands = new Collection();
client.prefixCommands = new Collection();
client.cooldownMap = new Map();

client.cooldowns = new Collection();
client.userSelection = new Map();

const ticketModel = require("./models/ticketModel.js");
const { updateYearlyStats } = require("./events/dailyStatsUpdater.js")
const apiStats = require("./models/statisticsModel")

const stripe = require('stripe')(config.StripeSettings.StripeSecretKey, {
  apiVersion: '2024-06-20',
});

client.stripe = stripe;

const paypal = require("paypal-rest-sdk");
paypal.configure({
  'mode': 'live',
  'client_id': config.PayPalSettings.PayPalClientID,
  'client_secret': config.PayPalSettings.PayPalSecretKey
});
client.paypal = paypal;

function logToFile(message) {
  fs.appendFile("./logs.manager", `\n\n${message}`, (err) => {
      if (err) console.error(err);
  });
}


const CryptoConvert = require("crypto-convert")
if(!config.CryptoRates) console.log('\x1b[31m%s\x1b[0m', `[ERROR] Your config.yml file is outdated! CryptoRates is missing from the config in the crypto section`)
if(config.CryptoSettings.Enabled) {
const cryptoConvert = new CryptoConvert({
	cryptoInterval: 5000,
	fiatInterval: (60 * 1e3 * 60),
	calculateAverage: true,
	bitfinex: config.CryptoRates.bitfinex,
	coinbase: config.CryptoRates.coinbase,
	kraken: config.CryptoRates.kraken,
  binance: config.CryptoRates.binance,
	HTTPAgent: null
});
client.cryptoConvert = cryptoConvert
}

//Slash Commands
const { REST } = require('@discordjs/rest');
const { Routes } = require('discord-api-types/v10');

if(config.GuildID) {
const slashCommands = [];
const contextMenuCommands = [];
const userInstallCommands = [];
const commandFolders = fs.readdirSync('./slashCommands');
console.log(`[COMMANDS] Commands have been loaded!`)
for (const folder of commandFolders) {
  const commandFiles = fs.readdirSync(`./slashCommands/${folder}`).filter(file => file.endsWith('.js'));
  for (const file of commandFiles) {

  const command = require(`./slashCommands/${folder}/${file}`);
  if (command.enabled) {
    if (command.data instanceof SlashCommandBuilder) {
      slashCommands.push(command.data.toJSON());
      console.log(`${command.data.name} has been loaded`)
    } else if (command.data instanceof ContextMenuCommandBuilder) {
      contextMenuCommands.push(command.data.toJSON());
      console.log(`${command.data.name} has been loaded`)
    } else {
      userInstallCommands.push(command.data);
      console.log(`${command.data.name} has been loaded`)
    }
    client.slashCommands.set(command.data.name, command);
  }
}
}

const dashboardPath = path.join(__dirname, 'src', 'dashboard.js');

glob('./src/**/*.js', function (err, files) {
  if (err) return console.error(err);

  const loadedAddons = [];

  files.forEach(async file => {
    if (file.endsWith('.js')) {
      const folderName = file.match(/\/src\/([^/]+)/)[1];

      if (fs.existsSync(dashboardPath) && !loadedAddons.includes('dashboard')) {
        require(dashboardPath);

        // Add 'dashboard' to the loadedAddons array
        loadedAddons.push('dashboard');
        console.log(`[API] dashboard.js has been loaded!`);
      }

      try {
        if (fs.existsSync(file)) {
          let addon = require(file);

          if (addon && addon.data && addon.data.toJSON) {
            await slashCommands.push(addon.data.toJSON());
            await client.slashCommands.set(addon.data.name, addon);
          } else if (addon && addon.run && typeof addon.run === 'function') {
            await addon.run(client);
          }
        }
      } catch (addonError) {
        console.error(`${color.red(`[ERROR] ${folderName}: ${addonError.message}`)}`);
        console.error(addonError.stack);
      }
    }
  });
});

const allCommands = [...slashCommands, ...contextMenuCommands, ...userInstallCommands];
client.on('ready', async () => {

  const rest = new REST({
      version: '10'
  }).setToken(config.Token);
  (async () => {
      try {
              await rest.put(
                  Routes.applicationCommands(client.user.id), {
                      body: allCommands
                  },
              );
      } catch (error) {
          if (error) {
            let logMsg = `\n\n[${new Date().toLocaleString()}] [ERROR] ${error.stack}`;
            await fs.appendFile("./logs.ecommerify", logMsg, (e) => {
              if(e) console.log(e);
            });
            console.log(error)
            await console.log('\x1b[31m%s\x1b[0m', `[ERROR] Slash commands are unavailable because application.commands scope wasn't selected when inviting the bot. Please use the link below to re-invite your bot.`)
            await console.log('\x1b[31m%s\x1b[0m', `https://discord.com/api/oauth2/authorize?client_id=${client.user.id}&permissions=8&scope=bot%20applications.commands`)
          }
      }
  })();
});
}
const prefixCommandFiles = fs.readdirSync(path.join(__dirname, "prefixedCommands")).filter(file => file.endsWith(".js"));
for (const file of prefixCommandFiles) {
  const command = require(`./prefixedCommands/${file}`);
  client.prefixCommands.set(command.name, command);
  console.log(`✅ Loaded Prefix Command: ${command.name}`);
}
//

// function loadEvents(dir) {
//   fs.readdir(dir, (err, files) => {
//     if (err) return console.error(err);
//     console.log(`[EVENTS] Events have been loaded!`);
//     files.forEach(file => {
//       const filePath = path.join(dir, file);
//       fs.stat(filePath, (err, stats) => {
//         if (err) return console.error(err);

//         if (stats.isDirectory()) {
//           loadEvents(filePath);
//         } else if (file.endsWith('.js') && file !== 'dailyStatsUpdater.js') {
//           const evt = require(filePath);
//           let evtName = file.split('.')[0];
//           client.on(evtName, evt.bind(null, client));
//         }
//       });
//     });
//   });
// }


// Command and event handler etc..
fs.readdir('./events/', (err, files) => {
  if (err) return console.error
  console.log(`-------------------------------------------------------------------`)
  console.log(`[EVENTS] Events have been loaded!`)

  files.forEach(async (file) => {
    if(!file.endsWith('.js') || file === 'dailyStatsUpdater.js') return;
    console.log(`${file} has been loaded`)

    const evt = require(`./events/${file}`);
    let evtName = file.split('.')[0];
    client.on(evtName, evt.bind(null, client));
  });
});


client.on("interactionCreate", async (interaction) => {
  if (!interaction.isChatInputCommand()) return;

  const timestamp = new Date().toLocaleString();
  const userTag = `${interaction.user.username}#${interaction.user.discriminator}`;
  const userId = interaction.user.id;

  // ✅ Log Interaction (Button Clicks, Select Menus, etc.)
  if (interaction.customId) {
      let logMsg = `[${timestamp}] [INTERACTION] ${interaction.customId}`;
      logToFile(logMsg);

      const interactionEmbed = new EmbedBuilder()
          .setColor("Blue")
          .setTitle("Interaction Used")
          .addFields(
              { name: "Interaction ID", value: `\`${interaction.customId}\`` },
              { name: "User", value: `${userTag} (${userId})`, inline: true },
              { name: "Time", value: `${timestamp}`, inline: true }
          )
          .setFooter({ text: "Manager", iconURL: interaction.user.displayAvatarURL() });

      webhookClient.send({ embeds: [interactionEmbed] });
      adminLogs.send({ embeds: [interactionEmbed] });
  }

  const command = client.slashCommands.get(interaction.commandName);
  if (!command) return;

  // ✅ Log Slash Command Usage
  let logMsg = `[${timestamp}] [SLASH COMMAND] /${interaction.commandName} used by ${userTag}`;
  logToFile(logMsg);

  const commandEmbed = new EmbedBuilder()
      .setColor("Green")
      .setTitle("Slash Command Used")
      .addFields(
          { name: "Command", value: `\`/${interaction.commandName}\``, inline: true },
          { name: "User", value: `${userTag} (${userId})`, inline: true },
          { name: "Time", value: `${timestamp}`, inline: true }
      )
      .setFooter({ text: "Manager", iconURL: interaction.user.displayAvatarURL() });

  webhookClient.send({ embeds: [commandEmbed] });

  // ✅ Console Logging (If Enabled)
  if (config.BotSettings.LogCommands) {
      console.log(
          color.yellow(`[SLASH COMMAND] ${color.cyan(userTag)} used ${color.cyan(`/${interaction.commandName}`)}`)
      );
  }

  // ✅ Execute the command
  try {
      await command.execute(interaction, client);
  } catch (error) {
      console.error(error);
      const errorEmbed = new EmbedBuilder()
          .setColor("Red")
          .setTitle("Command Execution Failed")
          .addFields(
              { name: "Command", value: `\`/${interaction.commandName}\``, inline: true },
              { name: "User", value: `${userTag} (${userId})`, inline: true },
              { name: "Error", value: `\`\`\`${error.message}\`\`\`` }
          )
          .setTimestamp()
          .setFooter({ text: "Manager", iconURL: interaction.user.displayAvatarURL() });

      webhookClient.send({ embeds: [errorEmbed] });
      adminLogs.send({ embeds: [errorEmbed] })
  }
});



// Check config for errors
exports.checkConfig = function(client){
  let foundErrors = [];
  let guild = client.guilds.cache.get(config.GuildID)

  var reg=/^#([0-9a-f]{3}){1,2}$/i;
  if(reg.test(config.EmbedColors) === false)  {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] EmbedColors is not a valid HEX Color!`)
    foundErrors.push("EmbedColors is not a valid HEX Color!");
  }


  // Check if user has removed any buttons from the config
for (let i = 1; i <= 8; i++) {
  const button = config[`TicketButton${i}`];

  if (!button) {
    console.log('\x1b[31m%s\x1b[0m', `[ERROR] You have removed TicketButton${i} from the config which means that the bot won't function properly, You can set Enabled to false if you want to disable it instead.`)
    foundErrors.push(`TicketButton${i} removed from the config!`);
    process.exit();
  }
}


  // Check for invalid colors in all ticket buttons
  if(!["Blurple", "Gray", "Green", "Red"].includes(config.TicketButton1.ButtonColor)) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton1.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("TicketButton1.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.TicketButton2.ButtonColor) && config.TicketButton2.Enabled)  {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton2.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("TicketButton2.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.TicketButton3.ButtonColor) && config.TicketButton3.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton3.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("TicketButton3.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.TicketButton4.ButtonColor) && config.TicketButton4.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton4.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("TicketButton4.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.TicketButton5.ButtonColor) && config.TicketButton5.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton5.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("TicketButton5.ButtonColor is not a valid color!");
  }


  // Check for invalid colors in all suggestion buttons
  if(!["Blurple", "Gray", "Green", "Red"].includes(config.SuggestionUpvote.ButtonColor) && config.SuggestionSettings.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] SuggestionUpvote.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("SuggestionUpvote.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.SuggestionDownvote.ButtonColor) && config.SuggestionSettings.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] SuggestionDownvote.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("SuggestionDownvote.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.SuggestionResetvote.ButtonColor) && config.SuggestionSettings.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] SuggestionResetvote.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("SuggestionResetvote.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.SuggestionAccept.ButtonColor) && config.SuggestionSettings.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] SuggestionAccept.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("SuggestionAccept.ButtonColor is not a valid color!");
  }

  if(!["Blurple", "Gray", "Green", "Red"].includes(config.SuggestionDeny.ButtonColor) && config.SuggestionSettings.Enabled) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] SuggestionDeny.ButtonColor is not a valid color! Valid colors: Blurple, Gray, Green, Red (CASE SENSITIVE)`)
    foundErrors.push("SuggestionDeny.ButtonColor is not a valid color!");
  }

// Check for invalid category channels in all ticket buttons
for (let i = 1; i <= 8; i++) {
  const ticketButton = config[`TicketButton${i}`];

  if (i !== 1 && !ticketButton.Enabled) continue;

  if (guild.channels.cache.get(ticketButton.TicketCategoryID)?.type !== 4) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton${i}.TicketCategoryID is not a valid category!`);
    foundErrors.push(`TicketButton${i}.TicketCategoryID is not a valid category!`);
  }
}




// Check for invalid closed category channels in all ticket buttons
for (let i = 1; i <= 8; i++) {
  const ticketButton = config[`TicketButton${i}`];

  if (i !== 1 && !ticketButton.Enabled) continue;

  if (config.TicketSettings.ArchiveTickets && guild.channels.cache.get(ticketButton.ClosedCategoryID)?.type !== 4) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton${i}.ClosedCategoryID is not a valid category!`);
    foundErrors.push(`TicketButton${i}.ClosedCategoryID is not a valid category!`);
  }
}


// Check for category descriptions longer than 100 characters
for (let i = 1; i <= 8; i++) {
  const ticketButton = config[`TicketButton${i}`];

  if (i !== 1 && !ticketButton.Enabled) continue;

  if (ticketButton.Description.length > 100) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton${i}.Description can't be longer than 100 characters!`);
    foundErrors.push(`TicketButton${i}.Description can't be longer than 100 characters!`);
  }
}


// Check for invalid support roles in all ticket buttons
for (let i = 1; i <= 8; i++) {
  const button = config[`TicketButton${i}`];

  if (i !== 1 && !button.Enabled) continue;

  button.SupportRoles.forEach(roleid => {
    const role = guild.roles.cache.get(roleid);

    if (!role) {
      console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton${i}.SupportRoles is not a valid role! (${roleid})`);
      foundErrors.push(`TicketButton${i}.SupportRoles is not a valid role!`);
    }
  });
}

// Check for invalid emojis in all ticket buttons
const emojiRegex = require('emoji-regex');
const discordEmojiRegex = /<a?:[a-zA-Z0-9_]+:(\d+)>/;

for (let i = 1; i <= 8; i++) {
  const ticketButton = config[`TicketButton${i}`];

  if (i !== 1 && !ticketButton.Enabled) continue;

  if (ticketButton.ButtonEmoji) {
    const emojiPattern = emojiRegex();
    const emojiMatch = emojiPattern.exec(ticketButton.ButtonEmoji);
    const discordEmojiMatch = ticketButton.ButtonEmoji.match(discordEmojiRegex);

    if (!emojiMatch && !discordEmojiMatch) {
      console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton${i}.ButtonEmoji contains an invalid emoji! (${ticketButton.ButtonEmoji})`);
      foundErrors.push(`TicketButton${i}.ButtonEmoji contains an invalid emoji!`);
    }
  }
}

// Check for more than 5 questions in all ticket buttons
for (let i = 1; i <= 8; i++) {
  const ticketButton = config[`TicketButton${i}`];

  if (i !== 1 && !ticketButton.Enabled) continue;

  if (Array.isArray(ticketButton.Questions) && ticketButton.Questions.length > 5) {
    console.log('\x1b[31m%s\x1b[0m', `[WARNING] TicketButton${i} has more than 5 questions! (Each category can only have a max of 5 questions, due to a Discord limitation)`);
    foundErrors.push(`TicketButton${i} has more than 5 questions!`);
  }
}



if(foundErrors.length > 0) {
let logMsg = `\n\n[${new Date().toLocaleString()}] [CONFIG ERROR(S)] \n${foundErrors.join("\n ").trim()}`;
fs.appendFile("./logs.ecommerify", logMsg, (e) => {
  if(e) console.log(e);
});
}
}

exports.checkDashboard = async function () {
  const folderPath = path.join(__dirname, 'src', 'Dashboard');

  try {
    const files = await new Promise((resolve, reject) => {
      fs.readdir(folderPath, (error, files) => {
        if (error) {
          reject(error);
        } else {
          resolve(files);
        }
      });
    });

    return true;

  } catch (error) {
    if (error.code === 'ENOENT') {
      return false;
    } else {
      throw error;
    }
  }
};

exports.saveTranscript = async function(interaction){
  let dashboardExists = await exports.checkDashboard();
  let attachment;
  let timestamp = "null"
if(interaction) {
  if(config.TicketTranscriptSettings.TranscriptType === "HTML") {
      attachment = await discordTranscripts.createTranscript(interaction.channel, {
        limit: -1,
        minify: false,
        saveImages: config.TicketTranscriptSettings.SaveImages,
        returnType: 'buffer',
        poweredBy: false,
        fileName: `${interaction.channel.name}.html`
      });

      if(config.TicketTranscriptSettings.SaveInFolder && dashboardExists) {
        timestamp = Date.now();
        fs.writeFileSync(`./src/Dashboard/transcripts/transcript-${interaction.channel.id}-${timestamp}.html`, attachment);

        const ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
        ticketDB.transcriptID = `${timestamp}`;
        await ticketDB.save();
      }

      attachment = new AttachmentBuilder(Buffer.from(attachment), { name: `${interaction.channel.name}-transcript.html` });
  } else if(config.TicketTranscriptSettings.TranscriptType === "TXT") {
      await interaction.channel.messages.fetch({ limit: 100 }).then(async fetched => {
          let a = fetched.filter(m => m.author.bot !== true).map(m => `${new Date(m.createdTimestamp).toLocaleString()} - ${m.author.username}#${m.author.discriminator}: ${m.attachments.size > 0 ? m.attachments.first().proxyURL : m.content}`).reverse().join('\n');
          if (a.length < 1) a = "Nothing"
          if(config.TicketTranscriptSettings.SaveInFolder) fs.writeFileSync(`./transcripts/${interaction.channel.name}-transcript-${interaction.channel.id}.txt`, Buffer.from(a));
          attachment = new AttachmentBuilder(Buffer.from(a), { name: `${interaction.channel.name}-transcript.txt` });
  })
}
}

return { attachment, timestamp };
}

exports.saveTranscriptAlertCmd = async function(channel){
  let dashboardExists = await exports.checkDashboard();
  let attachment;
  let timestamp = "null"
  if(channel) {
    if(config.TicketTranscriptSettings.TranscriptType === "HTML") {
        attachment = await discordTranscripts.createTranscript(channel, {
            limit: -1,
            minify: false,
            saveImages: config.TicketTranscriptSettings.SaveImages,
            returnType: 'buffer',
            poweredBy: false,
            fileName: `${channel.name}.html`
        });

        if(config.TicketTranscriptSettings.SaveInFolder && dashboardExists) {
          timestamp = Date.now();
          fs.writeFileSync(`./src/Dashboard/transcripts/transcript-${channel.id}-${timestamp}.html`, attachment);

          const ticketDB = await ticketModel.findOne({ channelID: channel.id });
          ticketDB.transcriptID = `${timestamp}`;
          await ticketDB.save();
        }

        attachment = new AttachmentBuilder(Buffer.from(attachment), { name: `${channel.name}-transcript.html` });
    } else if(config.TicketTranscriptSettings.TranscriptType === "TXT") {
        await channel.messages.fetch({ limit: 100 }).then(async fetched => {
            let a = fetched.filter(m => m.author.bot !== true).map(m => `${new Date(m.createdTimestamp).toLocaleString()} - ${m.author.username}#${m.author.discriminator}: ${m.attachments.size > 0 ? m.attachments.first().proxyURL : m.content}`).reverse().join('\n');
            if (a.length < 1) a = "Nothing"
            if(config.TicketTranscriptSettings.SaveInFolder) fs.writeFileSync(`./transcripts/${channel.name}-transcript-${channel.id}.txt`, attachment);
            attachment = new AttachmentBuilder(Buffer.from(a), { name: `${channel.name}-transcript.txt` });
    })
  }
  }
  return { attachment, timestamp };
}

const stripeModel = require('./models/stripeInvoicesModel.js');
const paypalModel = require('./models/paypalInvoicesModel.js');

// Check for new payments
    // Stripe payment detection
    exports.checkStripePayments = async function () {
      let guild = client.guilds.cache.get(config.GuildID);

      try {
        const filtered = await stripeModel.find({ status: 'open' });

        if (!filtered.length) return;

        for (const eachPayment of filtered) {
          let channel = guild.channels.cache.get(eachPayment.channelID);
          let user = guild.members.cache.get(eachPayment.userID);
          let seller = guild.members.cache.get(eachPayment.sellerID);
          let session;

          if (user) {
            session = await client.stripe.invoices.retrieve(eachPayment.invoiceID);

            if (!session || !channel) {
              await stripeModel.deleteMany({ invoiceID: eachPayment.invoiceID });
            }

            if (session.status === 'paid') {
              await stripeModel.updateOne({ invoiceID: session.id }, { $set: { status: 'paid' } });
              await stripeModel.updateOne({ invoiceID: session.id }, { $set: { status: 'deleted' } });
            }
          }

          if (channel && user && session && session.status === 'paid') {
            await channel.messages.fetch(eachPayment.messageID).then(async msg => {
              const row = new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                  .setStyle('Link')
                  .setURL(`https://stripe.com`)
                  .setLabel(config.Locale.PayPalPayInvoice)
                  .setDisabled(true)
              );

              let customerRole = guild.roles.cache.get(config.StripeSettings.RoleToGive);
              if(customerRole) user.roles.add(customerRole)

              const embed = msg.embeds[0];
              embed.fields[0] = { name: `• ${config.Locale.suggestionInformation}`, value: `> **${config.Locale.PayPalSeller}** ${seller}\n> **${config.Locale.PayPalUser}** ${user}\n> **${config.Locale.PayPalPrice}** ${config.StripeSettings.CurrencySymbol}${eachPayment.price} (${config.StripeSettings.Currency})\n> **${config.Locale.suggestionStatus}** 🟢 PAID (<t:${Math.round(Date.now() / 1000)}:R>)` };
              const embedColor = EmbedBuilder.from(embed);
              embedColor.setColor("Green");
              await msg.edit({ embeds: [embedColor], components: [row] });
            });
          }
        }
      } catch (error) {
        console.error('Error in checkStripePayments:', error);
      }
    };


    exports.checkPayPalPayments = async function () {
      const guild = client.guilds.cache.get(config.GuildID);

      try {
        const filtered = await paypalModel.find({ status: 'DRAFT' });

        if (!filtered.length) return;

        for (const eachPayment of filtered) {
          const channel = guild.channels.cache.get(eachPayment.channelID);
          const user = guild.members.cache.get(eachPayment.userID);
          const seller = guild.members.cache.get(eachPayment.sellerID);

          if (user) {
            client.paypal.invoice.get(eachPayment.invoiceID, async function (error, invoice) {
              if (error) {
                if (error.response.error === "invalid_client") {
                  console.log('\x1b[31m%s\x1b[0m', `[ERROR] The PayPal API Credentials you specified in the config are invalid! Make sure you use the "LIVE" mode!`);
                } else {
                  console.error(error);
                }
              } else {
                if (!channel || !invoice) {
                  await paypalModel.deleteMany({ invoiceID: invoice.id });
                }

                if (invoice.status === 'PAID') {
                  await paypalModel.updateOne({ invoiceID: invoice.id }, { $set: { status: 'paid' } });
                }

                if (invoice && channel && user && invoice.status === 'PAID') {
                  channel.messages.fetch(eachPayment.messageID)
                    .catch(e => { })
                    .then(async msg => {
                      const row = new ActionRowBuilder().addComponents(
                        new ButtonBuilder()
                          .setStyle('Link')
                          .setURL(`https://paypal.com`)
                          .setLabel(config.Locale.PayPalPayInvoice)
                          .setDisabled(true)
                      );

                      const embed = msg.embeds[0];
                      embed.fields[0] = {
                        name: `• ${config.Locale.suggestionInformation}`,
                        value: `> **${config.Locale.PayPalSeller}** <@!${seller.id}>\n> **${config.Locale.PayPalUser}** <@!${user.id}>\n> **${config.Locale.PayPalPrice}** ${config.PayPalSettings.CurrencySymbol}${eachPayment.price} (${config.PayPalSettings.Currency})\n> **${config.Locale.suggestionStatus}** 🟢 PAID (<t:${Math.round(Date.now() / 1000)}:R>)`
                      };

                      const embedColor = EmbedBuilder.from(embed)
                        .setColor("Green");

                        let customerRole = guild.roles.cache.get(config.PayPalSettings.RoleToGive);
                        if(customerRole) user.roles.add(customerRole)

                      await msg.edit({ embeds: [embedColor], components: [row] });
                    });
                }
              }
            });
          }
        }
      } catch (error) {
        console.error('Error in checkPayPalPayments:', error);
      }
    };

    let requestCounter = 0

exports.countRequests = async function () {
  try {
    // Increment the request counter
    requestCounter++;

    // Find or create the API statistics record
    let APIstatistics = await apiStats.findOne();
    if (!APIstatistics) {
      APIstatistics = new apiStats(); // Correct the instantiation
    }

    // Update total requests and reset the counter
    APIstatistics.totalRequests += requestCounter;
    await APIstatistics.save();
    requestCounter = 0;

    // Specify the metrics to update
    const metricsToUpdate = ['totalRequests'];

    // Update yearly statistics
    await updateYearlyStats(config.GuildID, metricsToUpdate);

    return APIstatistics;
  } catch (error) {
    console.error('Error counting requests:', error);
    throw error; // Optionally rethrow to handle upstream
  }
}

exports.getUserInfo = async function(userId) {
  try {
    const discordUser = await client.users.fetch(userId);

    const avatarURL = discordUser.avatar ? discordUser.avatarURL() : `https://cdn.discordapp.com/attachments/1323863612145664012/1333375130746748982/Comp1_15-ezgif.com-video-to-gif-converter.gif?ex=67a13b9c&is=679fea1c&hm=74a8c14178d02b8d4ce14cc3a54d0ada2150b113ff87966eaa6909269f0e4dbf&`;

    return {
      username: discordUser.username,
      avatarURL: avatarURL,
    };
  } catch (error) {
    if (error.message === 'Unknown Member') {
      return null;
    } else {
      return {
        username: 'Unknown',
        avatarURL: `https://cdn.discordapp.com/attachments/1323863612145664012/1333375130746748982/Comp1_15-ezgif.com-video-to-gif-converter.gif?ex=67a13b9c&is=679fea1c&hm=74a8c14178d02b8d4ce14cc3a54d0ada2150b113ff87966eaa6909269f0e4dbf&`,
      };
    }
  }
}

exports.getUserRoles = async function(usedId, guildId) {
  try {
    const guild = await client.guilds.fetch(guildId);
    const member = await guild.members.fetch(userId);

    const userRoles = member.roles.cache.map(role => role.id);

    return userRoles;
  } catch (error) {
    console.error('Error fetching user roles:', error);
    return [];
  }
}



exports.checkIfUserHasSupportRoles = async function(interaction, message) {
  let supportRole = false;
  let context = interaction || message;

  const ticketDB = await ticketModel.findOne({ channelID: context.channel.id });
  const ticketButton = ticketDB.button;

  for (let i = 1; i <= 8; i++) {
    const buttonConfig = config[`TicketButton${i}`];

    if (ticketButton === `TicketButton${i}`) {
      for (const roleId of buttonConfig.SupportRoles) {
        const role = context.guild.roles.cache.get(roleId);

        if (role && context.member.roles.cache.has(role.id)) {
          supportRole = true;
          break;
        }
      }
      break;
    }
  }

  return supportRole;
};

exports.paginateEmbed = async function(interaction, emojis, embeds, timeout){

  if (embeds.length <= 0) return interaction.editReply({ content: `There's no embeds to paginate!`, ephemeral: true })
  if (embeds.length == 1) return interaction.editReply({ embeds: [embeds[0]], ephemeral: true});

  let current = 0
  const row = (state) => [
      new ActionRowBuilder().addComponents(
          new ButtonBuilder()
              .setEmoji(emojis[0])
              .setDisabled(state)
              .setStyle("Secondary")
              .setCustomId("apiButton1"),
          new ButtonBuilder()
              .setLabel(emojis[1])
              .setDisabled(state)
              .setStyle("Secondary")
              .setCustomId("apiButton2"),
          new ButtonBuilder()
              .setLabel(emojis[2])
              .setDisabled(state)
              .setStyle("Secondary")
              .setCustomId("apiButton3"),
          new ButtonBuilder()
              .setEmoji(emojis[3])
              .setDisabled(state)
              .setStyle("Secondary")
              .setCustomId("apiButton4")
      )
  ]

  const currentPage = await interaction.editReply({ embeds: [embeds[current].setFooter({ text: `Page ${current + 1} of ${embeds.length}` })], components: row(false), fetchReply: true, ephemeral: true}).catch((e) => { console.log(e) })

  const collector = currentPage.createMessageComponentCollector({ filter: (m) => m.user.id === interaction.member.id, componentType: ComponentType.Button, time: ms(timeout), });

  collector.on("collect", async (collected) => {
      if (collected.customId === "apiButton1") current = 0
      else if (collected.customId === "apiButton2") current--
      else if (collected.customId === "apiButton3") current++
      else if (collected.customId === "apiButton4") current = embeds.length - 1

      if (current < 0) current = embeds.length - 1
      if (current >= embeds.length) current = 0

      interaction.editReply({ embeds: [embeds[current].setFooter({ text: `Page ${current + 1} of ${embeds.length}` })] }).catch((e) => { console.log(e) });

      collected.deferUpdate();
  })

  collector.on("end", async () => {
    currentPage.edit({embeds: [embeds[current].setColor("Red")], components: row(true) }).catch(() => { });
  });
}


  client.login(config.Token).catch(error => {
    if (error.message.includes("Used disallowed intents")) {
      console.log('\x1b[31m%s\x1b[0m', `Used disallowed intents (READ HOW TO FIX): \n\nYou did not enable Privileged Gateway Intents in the Discord Developer Portal!\nTo fix this, you have to enable all the privileged gateway intents in your discord developer portal, you can do this by opening the discord developer portal, go to your application, click on bot on the left side, scroll down and enable Presence Intent, Server Members Intent, and Message Content Intent`);
      process.exit();
    } else if (error.message.includes("An invalid token was provided")) {
      console.log('\x1b[31m%s\x1b[0m', `[ERROR] The bot token specified in the config is incorrect!`)
      process.exit()
    } else {
      console.log('\x1b[31m%s\x1b[0m', `[ERROR] An error occured while attempting to login to the bot`)
      console.log(error)
      process.exit()
    }
  })

