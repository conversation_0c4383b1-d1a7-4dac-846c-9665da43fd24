

[5/1/2025, 9:05:55 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk <PERSON>\Manager Bot\events\interactionCreate.js:510
await processTicketCreation(interaction, buttonConfig, customIdentifier, buttonConfigKey, responses);
^^^^^

SyntaxError: await is only valid in async functions and the top level bodies of modules
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk <PERSON>\Manager Bot\utils.js:204:17

[5/1/2025, 9:05:56 PM] [READY] Bot is now ready!

[5/1/2025, 9:05:56 PM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'enabled')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:446:32)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/1/2025, 9:07:23 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:510
await processTicketCreation(interaction, buttonConfig, customIdentifier, buttonConfigKey, responses);
^^^^^

SyntaxError: await is only valid in async functions and the top level bodies of modules
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/1/2025, 9:07:23 PM] [READY] Bot is now ready!

[5/1/2025, 9:07:38 PM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading '1250601096783593573')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\messageCreate.js:97:57)
    at Client.emit (node:events:518:28)
    at MessageCreateAction.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\actions\MessageCreate.js:32:14)
    at module.exports [as MESSAGE_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\MESSAGE_CREATE.js:4:32)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1190:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1007:14)

[5/1/2025, 9:09:47 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:510
await processTicketCreation(interaction, buttonConfig, customIdentifier, buttonConfigKey, responses);
^^^^^

SyntaxError: await is only valid in async functions and the top level bodies of modules
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/1/2025, 9:09:47 PM] [READY] Bot is now ready!

[5/1/2025, 9:14:10 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1828
})
 ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/1/2025, 9:14:11 PM] [READY] Bot is now ready!

[5/1/2025, 9:15:45 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1831
})
 ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/1/2025, 9:15:45 PM] [READY] Bot is now ready!

[5/1/2025, 9:16:11 PM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:20:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/1/2025, 9:16:12 PM] [READY] Bot is now ready!

[5/1/2025, 9:22:39 PM] [READY] Bot is now ready!

[5/1/2025, 9:22:57 PM] [READY] Bot is now ready!

[5/1/2025, 9:24:37 PM] [READY] Bot is now ready!

[5/1/2025, 9:24:56 PM] [READY] Bot is now ready!

[5/1/2025, 9:25:45 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:207
}
^

SyntaxError: Missing catch or finally after try
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/1/2025, 9:25:46 PM] [READY] Bot is now ready!

[5/1/2025, 9:26:52 PM] [READY] Bot is now ready!

[5/1/2025, 9:27:37 PM] [READY] Bot is now ready!

[5/1/2025, 9:28:16 PM] [READY] Bot is now ready!

[5/1/2025, 9:28:36 PM] [READY] Bot is now ready!

[5/1/2025, 9:28:56 PM] [READY] Bot is now ready!

[5/1/2025, 9:29:20 PM] [READY] Bot is now ready!

[5/1/2025, 9:29:26 PM] [SLASH COMMAND] /banmonitor used by skelee.dev#0

[5/1/2025, 9:29:41 PM] [SLASH COMMAND] /banmonitor used by skelee.dev#0

[5/1/2025, 9:29:49 PM] [SLASH COMMAND] /banmonitor used by skelee.dev#0

[5/1/2025, 9:54:03 PM] [READY] Bot is now ready!

[5/1/2025, 10:04:46 PM] [READY] Bot is now ready!

[5/1/2025, 10:04:57 PM] [READY] Bot is now ready!

[5/1/2025, 10:05:06 PM] [READY] Bot is now ready!

[5/1/2025, 10:05:32 PM] [READY] Bot is now ready!

[5/1/2025, 10:05:52 PM] [READY] Bot is now ready!

[5/1/2025, 10:06:09 PM] [READY] Bot is now ready!

[5/1/2025, 10:06:39 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/1/2025, 10:06:41 PM] [READY] Bot is now ready!

[5/1/2025, 10:11:34 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/1/2025, 10:11:35 PM] [READY] Bot is now ready!

[5/1/2025, 10:14:50 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/1/2025, 10:14:50 PM] [READY] Bot is now ready!

[5/1/2025, 10:15:06 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/1/2025, 10:15:07 PM] [READY] Bot is now ready!

[5/1/2025, 10:15:29 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/1/2025, 10:15:30 PM] [READY] Bot is now ready!

[5/1/2025, 10:16:08 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/1/2025, 10:16:08 PM] [READY] Bot is now ready!

[5/1/2025, 10:16:52 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/1/2025, 10:16:52 PM] [READY] Bot is now ready!

[5/1/2025, 10:17:28 PM] [READY] Bot is now ready!

[5/1/2025, 10:18:56 PM] [READY] Bot is now ready!

[5/1/2025, 10:20:23 PM] [READY] Bot is now ready!

[5/1/2025, 10:20:55 PM] [READY] Bot is now ready!

[5/1/2025, 10:21:27 PM] [SLASH COMMAND] /staff-stats used by skelee.dev#0

[5/1/2025, 10:22:44 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 10:23:50 PM] [READY] Bot is now ready!

[5/1/2025, 10:24:01 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 10:24:28 PM] [SLASH COMMAND] /remove-staff used by skelee.dev#0

[5/1/2025, 10:24:37 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 10:25:13 PM] [READY] Bot is now ready!

[5/1/2025, 10:28:03 PM] [SLASH COMMAND] /onload-staff used by skelee.dev#0

[5/1/2025, 10:28:19 PM] [SLASH COMMAND] /offload-staff used by skelee.dev#0

[5/1/2025, 10:46:02 PM] [READY] Bot is now ready!

[5/1/2025, 10:46:18 PM] [READY] Bot is now ready!

[5/1/2025, 10:46:34 PM] [READY] Bot is now ready!

[5/1/2025, 10:46:59 PM] [READY] Bot is now ready!

[5/1/2025, 10:47:40 PM] [READY] Bot is now ready!

[5/1/2025, 10:47:53 PM] [READY] Bot is now ready!

[5/1/2025, 10:48:34 PM] [READY] Bot is now ready!

[5/1/2025, 10:54:53 PM] [READY] Bot is now ready!

[5/1/2025, 10:55:10 PM] [READY] Bot is now ready!

[5/1/2025, 10:55:44 PM] [READY] Bot is now ready!

[5/1/2025, 10:56:14 PM] [READY] Bot is now ready!

[5/1/2025, 10:56:36 PM] [READY] Bot is now ready!

[5/1/2025, 10:56:59 PM] [READY] Bot is now ready!

[5/1/2025, 10:57:21 PM] [READY] Bot is now ready!

[5/1/2025, 10:57:38 PM] [READY] Bot is now ready!

[5/1/2025, 10:58:59 PM] [SLASH COMMAND] /remove-staff used by skelee.dev#0

[5/1/2025, 11:01:27 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 11:03:45 PM] [SLASH COMMAND] /panel used by skelee.dev#0

[5/1/2025, 11:03:45 PM] [unhandledRejection] [v1.5.0]
DiscordAPIError[50035]: Invalid Form Body
embeds[1].description[BASE_TYPE_REQUIRED]: This field is required
    at handleErrors (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SequentialHandler.runRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1149:23)
    at async SequentialHandler.queueRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:980:14)
    at async _REST.request (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async TextChannel.send (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\structures\interfaces\TextBasedChannel.js:195:15)

[5/1/2025, 11:05:26 PM] [SLASH COMMAND] /panel used by skelee.dev#0

[5/1/2025, 11:05:26 PM] [unhandledRejection] [v1.5.0]
DiscordAPIError[50035]: Invalid Form Body
embeds[1].description[BASE_TYPE_REQUIRED]: This field is required
    at handleErrors (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SequentialHandler.runRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1149:23)
    at async SequentialHandler.queueRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:980:14)
    at async _REST.request (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async TextChannel.send (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\structures\interfaces\TextBasedChannel.js:195:15)

[5/1/2025, 11:05:39 PM] [READY] Bot is now ready!

[5/1/2025, 11:05:41 PM] [SLASH COMMAND] /panel used by skelee.dev#0

[5/1/2025, 11:06:54 PM] [READY] Bot is now ready!

[5/1/2025, 11:07:02 PM] [SLASH COMMAND] /panel used by skelee.dev#0

[5/1/2025, 11:19:50 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 11:20:12 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 11:20:30 PM] [SLASH COMMAND] /addstaff used by botjason#0

[5/1/2025, 11:28:42 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 11:29:02 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/1/2025, 11:31:54 PM] [SLASH COMMAND] /onload-staff used by skelee.dev#0

[5/2/2025, 12:12:12 AM] [SLASH COMMAND] /staff-stats used by skelee.dev#0

[5/2/2025, 12:16:12 AM] [READY] Bot is now ready!

[5/2/2025, 12:17:47 AM] [READY] Bot is now ready!

[5/2/2025, 12:18:12 AM] [READY] Bot is now ready!

[5/2/2025, 12:19:27 AM] [READY] Bot is now ready!

[5/2/2025, 12:20:54 AM] [READY] Bot is now ready!

[5/2/2025, 12:21:33 AM] [ERROR] [v1.5.0]
Error: Received one or more errors
    at _ObjectValidator.handleIgnoreStrategy (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:2136:70)
    at _ObjectValidator.handleStrategy (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:1983:47)
    at _ObjectValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:2089:17)
    at _ObjectValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:1122:164
    at Array.map (<anonymous>)
    at StringSelectMenuBuilder.addOptions (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:1121:28)
    at StringSelectMenuBuilder.addOptions (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\structures\StringSelectMenuBuilder.js:49:18)
    at showTicketMenu (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:940:13)
    at handleYesButton (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:696:15)

[5/2/2025, 12:22:25 AM] [READY] Bot is now ready!

[5/2/2025, 12:26:29 AM] [READY] Bot is now ready!

[5/2/2025, 12:26:53 AM] [READY] Bot is now ready!

[5/2/2025, 12:27:41 AM] [READY] Bot is now ready!

[5/2/2025, 12:28:05 AM] [READY] Bot is now ready!

[5/2/2025, 12:28:25 AM] [READY] Bot is now ready!

[5/2/2025, 12:35:29 AM] [READY] Bot is now ready!

[5/2/2025, 12:35:45 AM] [READY] Bot is now ready!

[5/2/2025, 12:36:09 AM] [READY] Bot is now ready!

[5/2/2025, 12:36:37 AM] [READY] Bot is now ready!

[5/2/2025, 12:37:29 AM] [READY] Bot is now ready!

[5/2/2025, 12:38:55 AM] [READY] Bot is now ready!

[5/2/2025, 12:39:17 AM] [READY] Bot is now ready!

[5/2/2025, 12:39:37 AM] [READY] Bot is now ready!

[5/2/2025, 12:40:21 AM] [READY] Bot is now ready!

[5/2/2025, 12:41:11 AM] [READY] Bot is now ready!

[5/2/2025, 12:41:32 AM] [READY] Bot is now ready!

[5/2/2025, 12:41:54 AM] [READY] Bot is now ready!

[5/2/2025, 12:46:30 AM] [READY] Bot is now ready!

[5/2/2025, 12:47:53 AM] [READY] Bot is now ready!

[5/2/2025, 12:48:15 AM] [READY] Bot is now ready!

[5/2/2025, 12:48:37 AM] [READY] Bot is now ready!

[5/2/2025, 12:49:02 AM] [READY] Bot is now ready!

[5/2/2025, 12:49:39 AM] [READY] Bot is now ready!

[5/2/2025, 12:50:06 AM] [READY] Bot is now ready!

[5/2/2025, 12:53:01 AM] [SLASH COMMAND] /bugreport used by skelee.dev#0

[5/2/2025, 12:53:11 AM] [SLASH COMMAND] /bugreport used by skelee.dev#0

[5/2/2025, 12:54:07 AM] [READY] Bot is now ready!

[5/2/2025, 12:54:27 AM] [READY] Bot is now ready!

[5/2/2025, 12:54:48 AM] [READY] Bot is now ready!

[5/2/2025, 12:55:10 AM] [READY] Bot is now ready!

[5/2/2025, 12:55:32 AM] [READY] Bot is now ready!

[5/2/2025, 12:55:54 AM] [READY] Bot is now ready!

[5/2/2025, 12:56:14 AM] [READY] Bot is now ready!

[5/2/2025, 12:56:44 AM] [READY] Bot is now ready!

[5/2/2025, 12:57:04 AM] [READY] Bot is now ready!

[5/2/2025, 12:57:26 AM] [READY] Bot is now ready!

[5/2/2025, 12:57:46 AM] [READY] Bot is now ready!

[5/2/2025, 12:58:06 AM] [READY] Bot is now ready!

[5/2/2025, 12:58:31 AM] [READY] Bot is now ready!

[5/2/2025, 12:58:54 AM] [READY] Bot is now ready!

[5/2/2025, 12:59:16 AM] [READY] Bot is now ready!

[5/2/2025, 12:59:37 AM] [READY] Bot is now ready!

[5/2/2025, 1:00:23 AM] [READY] Bot is now ready!

[5/2/2025, 1:01:01 AM] [READY] Bot is now ready!

[5/2/2025, 1:01:05 AM] [unhandledRejection] [v1.5.0]
Error: Received one or more errors
    at _UnionValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:1965:23)
    at _UnionValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at EmbedBuilder.setTitle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:379:20)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:191:18
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:01:59 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:20:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:02:00 AM] [READY] Bot is now ready!

[5/2/2025, 1:04:40 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:20:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:04:41 AM] [READY] Bot is now ready!

[5/2/2025, 1:05:35 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:20:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:05:36 AM] [READY] Bot is now ready!

[5/2/2025, 1:05:59 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:20:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:05:59 AM] [READY] Bot is now ready!

[5/2/2025, 1:06:27 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:20:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:06:28 AM] [READY] Bot is now ready!

[5/2/2025, 1:08:27 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:20:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:08:28 AM] [READY] Bot is now ready!

[5/2/2025, 1:09:43 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../config'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/2/2025, 1:09:43 AM] [READY] Bot is now ready!

[5/2/2025, 1:10:32 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../config'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/2/2025, 1:10:32 AM] [READY] Bot is now ready!

[5/2/2025, 1:10:54 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../config'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/2/2025, 1:10:55 AM] [READY] Bot is now ready!

[5/2/2025, 1:24:37 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../config'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/2/2025, 1:24:38 AM] [READY] Bot is now ready!

[5/2/2025, 1:25:22 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../config'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/2/2025, 1:25:22 AM] [READY] Bot is now ready!

[5/2/2025, 1:25:46 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../config'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\linkingSystem.js:2:16)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/2/2025, 1:25:47 AM] [READY] Bot is now ready!

[5/2/2025, 1:26:09 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:21:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:26:10 AM] [READY] Bot is now ready!

[5/2/2025, 1:26:45 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'map')
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:21:37)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:26:46 AM] [READY] Bot is now ready!

[5/2/2025, 1:27:24 AM] [SLASH COMMAND] /panel used by skelee.dev#0

[5/2/2025, 1:29:01 AM] [READY] Bot is now ready!

[5/2/2025, 1:29:10 AM] [ERROR] [v1.5.0]
Error: Received one or more errors
    at _UnionValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:1965:23)
    at _UnionValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at EmbedBuilder.setColor (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:310:20)
    at EmbedBuilder.setColor (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\structures\EmbedBuilder.js:23:18)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:607:6)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:29:22 AM] [READY] Bot is now ready!

[5/2/2025, 1:30:15 AM] [READY] Bot is now ready!

[5/2/2025, 1:31:02 AM] [READY] Bot is now ready!

[5/2/2025, 1:32:19 AM] [READY] Bot is now ready!

[5/2/2025, 1:32:50 AM] [READY] Bot is now ready!

[5/2/2025, 1:33:15 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'SelectMenu')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:40:51)
    at Client.emit (node:events:530:35)
    at InteractionCreateAction.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\actions\InteractionCreate.js:97:12)
    at module.exports [as INTERACTION_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\INTERACTION_CREATE.js:4:36)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1190:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1007:14)

[5/2/2025, 1:34:17 AM] [READY] Bot is now ready!

[5/2/2025, 1:34:38 AM] [READY] Bot is now ready!

[5/2/2025, 1:34:57 AM] [READY] Bot is now ready!

[5/2/2025, 1:35:20 AM] [READY] Bot is now ready!

[5/2/2025, 1:35:39 AM] [READY] Bot is now ready!

[5/2/2025, 1:35:58 AM] [READY] Bot is now ready!

[5/2/2025, 1:36:34 AM] [READY] Bot is now ready!

[5/2/2025, 1:36:40 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'SelectMenu')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:790:33)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:37:25 AM] [READY] Bot is now ready!

[5/2/2025, 1:37:51 AM] [ERROR] [v1.5.0]
Error: Expected a string primitive
    at _StringValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:2615:70)
    at _StringValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at StringSelectMenuBuilder.setPlaceholder (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:767:50)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:814:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:38:14 AM] [ERROR] [v1.5.0]
Error: Expected a string primitive
    at _StringValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:2615:70)
    at _StringValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at StringSelectMenuBuilder.setPlaceholder (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:767:50)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:814:16)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:38:39 AM] [READY] Bot is now ready!

[5/2/2025, 1:38:49 AM] [READY] Bot is now ready!

[5/2/2025, 1:38:56 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'totalTickets')
    at processTicketCreation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:120:49)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:543:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:39:07 AM] [READY] Bot is now ready!

[5/2/2025, 1:39:18 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'totalTickets')
    at processTicketCreation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:172:49)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:595:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:39:31 AM] [READY] Bot is now ready!

[5/2/2025, 1:39:38 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'totalTickets')
    at processTicketCreation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:175:49)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:598:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:40:12 AM] [READY] Bot is now ready!

[5/2/2025, 1:40:52 AM] [READY] Bot is now ready!

[5/2/2025, 1:41:51 AM] [READY] Bot is now ready!

[5/2/2025, 1:42:17 AM] [READY] Bot is now ready!

[5/2/2025, 1:42:38 AM] [READY] Bot is now ready!

[5/2/2025, 1:43:31 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'totalTickets')
    at processTicketCreation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:175:49)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:598:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:44:24 AM] [READY] Bot is now ready!

[5/2/2025, 1:44:55 AM] [READY] Bot is now ready!

[5/2/2025, 1:45:52 AM] [READY] Bot is now ready!

[5/2/2025, 1:47:53 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1009
  }
  ^

SyntaxError: Missing catch or finally after try
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:47:54 AM] [READY] Bot is now ready!

[5/2/2025, 1:48:13 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1008
      } catch (error) {
        ^^^^^

SyntaxError: Unexpected token 'catch'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 1:48:14 AM] [READY] Bot is now ready!

[5/2/2025, 1:49:05 AM] [READY] Bot is now ready!

[5/2/2025, 1:49:14 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'totalTickets')
    at processTicketCreation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:175:49)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:624:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:49:36 AM] [READY] Bot is now ready!

[5/2/2025, 1:51:02 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'totalTickets')
    at processTicketCreation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:175:49)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:624:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:51:02 AM] [READY] Bot is now ready!

[5/2/2025, 1:51:16 AM] [READY] Bot is now ready!

[5/2/2025, 1:51:36 AM] [READY] Bot is now ready!

[5/2/2025, 1:51:37 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'Enabled')
    at processTicketCreation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:188:38)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:624:7)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 1:56:02 AM] [READY] Bot is now ready!

[5/2/2025, 2:00:37 AM] [READY] Bot is now ready!

[5/2/2025, 2:04:48 AM] [READY] Bot is now ready!

[5/2/2025, 2:09:24 AM] [READY] Bot is now ready!

[5/2/2025, 2:10:25 AM] [SLASH COMMAND] /reply used by skelee.dev#0

[5/2/2025, 2:12:23 AM] [READY] Bot is now ready!

[5/2/2025, 2:13:03 AM] [READY] Bot is now ready!

[5/2/2025, 2:13:12 AM] [READY] Bot is now ready!

[5/2/2025, 2:13:13 AM] [SLASH COMMAND] /reply used by skelee.dev#0

[5/2/2025, 2:30:15 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/2/2025, 2:30:32 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/2/2025, 2:30:40 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/2/2025, 3:09:40 AM] [unhandledRejection] [v1.5.0]
Error [InteractionAlreadyReplied]: The reply to this interaction has already been sent or deferred.
    at ModalSubmitInteraction.reply (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:163:46)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:180:19
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 3:09:46 AM] [unhandledRejection] [v1.5.0]
Error: Expected a string primitive
    at _StringValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:2615:70)
    at _StringValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at ButtonBuilder.setLabel (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:732:44)
    at ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:130:8)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:206:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 3:10:15 AM] [SLASH COMMAND] /delete used by skelee.dev#0

[5/2/2025, 3:13:54 AM] [unhandledRejection] [v1.5.0]
Error: Expected a string primitive
    at _StringValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:2615:70)
    at _StringValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at ButtonBuilder.setLabel (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:732:44)
    at ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:130:8)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:206:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 3:15:22 AM] [READY] Bot is now ready!

[5/2/2025, 3:17:25 AM] [READY] Bot is now ready!

[5/2/2025, 3:22:20 AM] [READY] Bot is now ready!

[5/2/2025, 3:22:39 AM] [READY] Bot is now ready!

[5/2/2025, 3:22:44 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'replace')
    at ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:145:59)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:206:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 3:23:13 AM] [READY] Bot is now ready!

[5/2/2025, 3:23:16 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'replace')
    at ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:145:59)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:206:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 3:23:51 AM] [READY] Bot is now ready!

[5/2/2025, 3:25:18 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:17
const config = require('../utils/configAdapter');`);
      ^

SyntaxError: Identifier 'config' has already been declared
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 3:25:18 AM] [READY] Bot is now ready!

[5/2/2025, 3:25:39 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:17
const config = require('../utils/configAdapter');`);
      ^

SyntaxError: Identifier 'config' has already been declared
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 3:25:40 AM] [READY] Bot is now ready!

[5/2/2025, 3:25:57 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:17
const config = require('../utils/configAdapter');`);
      ^

SyntaxError: Identifier 'config' has already been declared
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 3:25:58 AM] [READY] Bot is now ready!

[5/2/2025, 3:26:29 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:17
const config = require('../utils/configAdapter');`);
      ^

SyntaxError: Identifier 'config' has already been declared
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 3:26:30 AM] [READY] Bot is now ready!

[5/2/2025, 3:26:41 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:17
const config = require('../utils/configAdapter');`);
      ^

SyntaxError: Identifier 'config' has already been declared
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 3:26:42 AM] [READY] Bot is now ready!

[5/2/2025, 3:27:40 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:17
const config = require('../utils/configAdapter');`);
      ^

SyntaxError: Identifier 'config' has already been declared
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/2/2025, 3:27:40 AM] [READY] Bot is now ready!

[5/2/2025, 3:28:02 AM] [READY] Bot is now ready!

[5/2/2025, 3:28:18 AM] [READY] Bot is now ready!

[5/2/2025, 3:28:45 AM] [READY] Bot is now ready!

[5/2/2025, 3:29:19 AM] [READY] Bot is now ready!

[5/2/2025, 3:29:34 AM] [READY] Bot is now ready!

[5/2/2025, 3:29:37 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'replace')
    at ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:145:59)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:206:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 3:30:39 AM] [READY] Bot is now ready!

[5/2/2025, 3:30:42 AM] [unhandledRejection] [v1.5.0]
Error: Received one or more errors
    at _UnionValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:1965:23)
    at _UnionValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at EmbedBuilder.setTitle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:379:20)
    at ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:149:8)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:206:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/2/2025, 4:20:15 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'send')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\guildMemberUpdate.js:60:65)
    at Client.emit (node:events:518:28)
    at GuildMemberUpdateAction.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\actions\GuildMemberUpdate.js:30:74)
    at module.exports [as GUILD_MEMBER_UPDATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_MEMBER_UPDATE.js:4:36)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1190:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1007:14)

[5/2/2025, 11:42:02 AM] [READY] Bot is now ready!

[5/2/2025, 11:42:10 AM] [READY] Bot is now ready!

[5/2/2025, 11:43:10 AM] [SLASH COMMAND] /remove-staff used by skelee.dev#0

[5/2/2025, 11:43:24 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/2/2025, 11:47:43 AM] [READY] Bot is now ready!

[5/2/2025, 11:50:21 AM] [READY] Bot is now ready!

[5/2/2025, 11:53:34 AM] [READY] Bot is now ready!

[5/2/2025, 11:56:39 AM] [READY] Bot is now ready!

[5/2/2025, 11:58:40 AM] [READY] Bot is now ready!

[5/2/2025, 12:01:24 PM] [READY] Bot is now ready!

[5/2/2025, 12:04:53 PM] [READY] Bot is now ready!

[5/2/2025, 12:07:16 PM] [READY] Bot is now ready!

[5/2/2025, 12:07:48 PM] [READY] Bot is now ready!

[5/2/2025, 12:10:25 PM] [READY] Bot is now ready!

[5/2/2025, 12:10:31 PM] [READY] Bot is now ready!

[5/2/2025, 12:12:25 PM] [READY] Bot is now ready!

[5/2/2025, 12:12:35 PM] [READY] Bot is now ready!

[5/2/2025, 12:16:29 PM] [READY] Bot is now ready!

[5/2/2025, 12:18:10 PM] [READY] Bot is now ready!

[5/2/2025, 12:18:53 PM] [READY] Bot is now ready!

[5/2/2025, 12:22:50 PM] [READY] Bot is now ready!

[5/2/2025, 12:23:08 PM] [READY] Bot is now ready!

[5/2/2025, 12:27:32 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/2/2025, 12:27:39 PM] [unhandledRejection] [v1.5.0]
TypeError: evt.bind is not a function
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:206:28
    at Array.forEach (<anonymous>)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:200:9
    at FSReqCallback.oncomplete (node:fs:188:23)

[5/2/2025, 12:27:52 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:27:57 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:02 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:07 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:12 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:17 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:22 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:27 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:32 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:37 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:42 PM] [unhandledRejection] [v1.5.0]
MongooseError: Operation `statusmessages.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/2/2025, 12:28:53 PM] [READY] Bot is now ready!

[5/2/2025, 12:29:05 PM] [READY] Bot is now ready!

[5/2/2025, 12:29:33 PM] [READY] Bot is now ready!

[5/2/2025, 12:33:21 PM] [READY] Bot is now ready!

[5/2/2025, 12:34:34 PM] [READY] Bot is now ready!

[5/2/2025, 12:36:44 PM] [READY] Bot is now ready!

[5/2/2025, 12:41:51 PM] [READY] Bot is now ready!

[5/2/2025, 12:41:58 PM] [READY] Bot is now ready!

[5/2/2025, 12:45:38 PM] [SLASH COMMAND] /remove-staff used by skelee.dev#0

[5/2/2025, 12:45:44 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/2/2025, 12:46:01 PM] [SLASH COMMAND] /onload-staff used by skelee.dev#0

[5/2/2025, 12:48:58 PM] [SLASH COMMAND] /remove-staff used by skelee.dev#0

[5/2/2025, 12:49:06 PM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/2/2025, 12:51:05 PM] [READY] Bot is now ready!

[5/2/2025, 12:52:52 PM] [READY] Bot is now ready!

[5/2/2025, 12:53:44 PM] [READY] Bot is now ready!

[5/2/2025, 12:55:36 PM] [SLASH COMMAND] /staff-stats used by skelee.dev#0

[5/2/2025, 12:55:49 PM] [SLASH COMMAND] /staff-stats used by skelee.dev#0

[5/2/2025, 1:11:48 PM] [SLASH COMMAND] /staff-stats used by skelee.dev#0

[5/2/2025, 1:16:33 PM] [READY] Bot is now ready!

[5/2/2025, 1:16:47 PM] [READY] Bot is now ready!

[5/2/2025, 1:16:59 PM] [READY] Bot is now ready!

[5/2/2025, 1:17:48 PM] [READY] Bot is now ready!

[5/2/2025, 1:18:37 PM] [READY] Bot is now ready!

[5/2/2025, 1:19:18 PM] [READY] Bot is now ready!

[5/2/2025, 1:19:59 PM] [READY] Bot is now ready!

[5/2/2025, 1:20:08 PM] [READY] Bot is now ready!

[5/2/2025, 1:21:11 PM] [READY] Bot is now ready!

[5/2/2025, 1:22:09 PM] [READY] Bot is now ready!

[5/2/2025, 1:23:07 PM] [READY] Bot is now ready!

[5/2/2025, 1:24:11 PM] [READY] Bot is now ready!

[5/2/2025, 1:24:45 PM] [READY] Bot is now ready!

[5/2/2025, 1:25:11 PM] [READY] Bot is now ready!

[5/2/2025, 1:25:38 PM] [READY] Bot is now ready!

[5/2/2025, 1:26:18 PM] [READY] Bot is now ready!

[5/2/2025, 1:26:39 PM] [READY] Bot is now ready!

[5/2/2025, 1:28:01 PM] [SLASH COMMAND] /add-staff used by skelee.dev#0

[5/2/2025, 1:28:38 PM] [READY] Bot is now ready!

[5/2/2025, 1:28:42 PM] [SLASH COMMAND] /add-staff used by skelee.dev#0

[5/2/2025, 1:28:52 PM] [SLASH COMMAND] /add-staff used by skelee.dev#0

[5/2/2025, 1:29:17 PM] [READY] Bot is now ready!

[5/2/2025, 1:29:32 PM] [READY] Bot is now ready!

[5/2/2025, 1:29:35 PM] [SLASH COMMAND] /add-staff used by skelee.dev#0

[5/2/2025, 1:29:45 PM] [SLASH COMMAND] /add-staff used by skelee.dev#0

[5/2/2025, 1:34:07 PM] [SLASH COMMAND] /spectaterequest used by skelee.dev#0

[5/2/2025, 1:34:24 PM] [SLASH COMMAND] /spectaterequest used by skelee.dev#0

[5/2/2025, 1:46:05 PM] [READY] Bot is now ready!

[5/2/2025, 1:46:09 PM] [SLASH COMMAND] /blacklistrequest used by skelee.dev#0

[5/2/2025, 1:46:31 PM] [READY] Bot is now ready!

[5/2/2025, 1:46:39 PM] [READY] Bot is now ready!

[5/2/2025, 1:46:47 PM] [SLASH COMMAND] /blacklistrequest used by skelee.dev#0

[5/2/2025, 1:47:06 PM] [READY] Bot is now ready!

[5/2/2025, 1:47:39 PM] [READY] Bot is now ready!

[5/2/2025, 1:47:43 PM] [SLASH COMMAND] /spectaterequest used by skelee.dev#0

[5/2/2025, 1:48:08 PM] [SLASH COMMAND] /spectaterequest used by skelee.dev#0

[5/2/2025, 1:48:44 PM] [SLASH COMMAND] /spectaterequest used by skelee.dev#0

[5/2/2025, 1:48:55 PM] [SLASH COMMAND] /watchlist used by skelee.dev#0

[5/2/2025, 1:49:16 PM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/2/2025, 1:52:22 PM] [READY] Bot is now ready!

[5/2/2025, 1:55:19 PM] [READY] Bot is now ready!

[5/2/2025, 1:56:24 PM] [READY] Bot is now ready!

[5/2/2025, 1:56:54 PM] [READY] Bot is now ready!

[5/2/2025, 1:57:08 PM] [READY] Bot is now ready!

[5/2/2025, 1:57:34 PM] [READY] Bot is now ready!

[5/2/2025, 1:58:16 PM] [READY] Bot is now ready!

[5/2/2025, 1:58:51 PM] [READY] Bot is now ready!

[5/2/2025, 1:59:06 PM] [READY] Bot is now ready!

[5/2/2025, 1:59:41 PM] [READY] Bot is now ready!

[5/2/2025, 2:00:11 PM] [READY] Bot is now ready!

[5/2/2025, 2:00:40 PM] [READY] Bot is now ready!

[5/2/2025, 2:01:57 PM] [READY] Bot is now ready!

[5/2/2025, 2:07:31 PM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/2/2025, 2:08:25 PM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/2/2025, 2:14:20 PM] [SLASH COMMAND] /add-staff used by skelee.dev#0

[5/2/2025, 2:14:20 PM] [READY] Bot is now ready!

[5/2/2025, 2:14:37 PM] [READY] Bot is now ready!

[5/2/2025, 2:15:07 PM] [READY] Bot is now ready!

[5/2/2025, 2:15:34 PM] [READY] Bot is now ready!

[5/2/2025, 2:16:08 PM] [READY] Bot is now ready!

[5/2/2025, 2:16:41 PM] [READY] Bot is now ready!

[5/2/2025, 2:17:06 PM] [READY] Bot is now ready!

[5/2/2025, 2:18:01 PM] [READY] Bot is now ready!

[5/2/2025, 2:32:49 PM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/2/2025, 10:09:46 PM] [READY] Bot is now ready!

[5/2/2025, 10:12:05 PM] [READY] Bot is now ready!

[5/2/2025, 10:17:01 PM] [READY] Bot is now ready!

[5/2/2025, 10:17:26 PM] [READY] Bot is now ready!

[5/2/2025, 10:17:41 PM] [READY] Bot is now ready!

[5/2/2025, 10:18:10 PM] [READY] Bot is now ready!

[5/2/2025, 10:18:26 PM] [READY] Bot is now ready!

[5/2/2025, 10:18:54 PM] [READY] Bot is now ready!

[5/2/2025, 10:19:10 PM] [READY] Bot is now ready!

[5/2/2025, 10:19:24 PM] [READY] Bot is now ready!

[5/3/2025, 3:21:55 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/3/2025, 3:22:05 AM] [READY] Bot is now ready!

[5/3/2025, 3:22:06 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/3/2025, 3:22:19 AM] [READY] Bot is now ready!

[5/3/2025, 3:22:31 AM] [READY] Bot is now ready!

[5/3/2025, 3:22:33 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/3/2025, 3:22:44 AM] [READY] Bot is now ready!

[5/3/2025, 3:22:56 AM] [READY] Bot is now ready!

[5/3/2025, 3:23:01 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/3/2025, 3:23:14 AM] [SLASH COMMAND] /addstaff used by skelee.dev#0

[5/3/2025, 3:24:00 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:27:56 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:28:04 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:29:44 AM] [READY] Bot is now ready!

[5/3/2025, 3:29:48 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:34:10 AM] [READY] Bot is now ready!

[5/3/2025, 3:34:28 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:34:47 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:37:46 AM] [READY] Bot is now ready!

[5/3/2025, 3:38:07 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:42:50 AM] [READY] Bot is now ready!

[5/3/2025, 3:43:09 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:47:55 AM] [READY] Bot is now ready!

[5/3/2025, 3:48:29 AM] [SLASH COMMAND] /banrequest used by skelee.dev#0

[5/3/2025, 3:50:34 AM] [READY] Bot is now ready!

[5/3/2025, 3:51:06 AM] [READY] Bot is now ready!

[5/3/2025, 3:51:16 AM] [READY] Bot is now ready!

[5/3/2025, 3:51:46 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1528
};
^

SyntaxError: Unexpected token '}'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/3/2025, 3:51:46 AM] [READY] Bot is now ready!

[5/3/2025, 3:51:52 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1529
};
^

SyntaxError: Unexpected token '}'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/3/2025, 3:51:53 AM] [READY] Bot is now ready!

[5/3/2025, 3:52:03 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1529
};
^

SyntaxError: Unexpected token '}'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/3/2025, 3:52:03 AM] [READY] Bot is now ready!

[5/3/2025, 3:52:10 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module './utils/banRequestUtils'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:29:25)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/3/2025, 3:52:10 AM] [READY] Bot is now ready!

[5/3/2025, 3:57:23 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module './utils/banRequestUtils'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:27:25)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/3/2025, 3:57:24 AM] [READY] Bot is now ready!

[5/3/2025, 3:57:45 AM] [READY] Bot is now ready!

[5/3/2025, 3:58:53 AM] [ERROR] [v1.5.0]
TypeError: BanRequestUtils.getBanRequestById is not a function
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1349:46)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/3/2025, 4:00:25 AM] [READY] Bot is now ready!

[5/3/2025, 10:05:00 AM] [SLASH COMMAND] /blacklistinfo used by botjason#0

[5/3/2025, 10:05:22 AM] [SLASH COMMAND] /staff-stats used by botjason#0

[5/3/2025, 12:52:19 PM] [READY] Bot is now ready!

[5/3/2025, 12:55:41 PM] [READY] Bot is now ready!

[5/3/2025, 12:56:35 PM] [READY] Bot is now ready!

[5/3/2025, 12:57:11 PM] [READY] Bot is now ready!

[5/3/2025, 12:57:53 PM] [READY] Bot is now ready!

[5/3/2025, 12:58:18 PM] [READY] Bot is now ready!

[5/3/2025, 12:58:37 PM] [READY] Bot is now ready!

[5/3/2025, 1:00:30 PM] [READY] Bot is now ready!

[5/3/2025, 1:00:49 PM] [READY] Bot is now ready!

[5/3/2025, 1:01:03 PM] [READY] Bot is now ready!

[5/3/2025, 1:01:19 PM] [READY] Bot is now ready!

[5/3/2025, 1:01:36 PM] [READY] Bot is now ready!

[5/3/2025, 1:02:02 PM] [READY] Bot is now ready!

[5/3/2025, 1:02:18 PM] [READY] Bot is now ready!

[5/3/2025, 1:02:33 PM] [READY] Bot is now ready!

[5/3/2025, 1:02:46 PM] [READY] Bot is now ready!

[5/3/2025, 1:03:01 PM] [READY] Bot is now ready!

[5/3/2025, 1:03:24 PM] [READY] Bot is now ready!

[5/3/2025, 1:03:45 PM] [READY] Bot is now ready!

[5/3/2025, 1:04:05 PM] [READY] Bot is now ready!

[5/3/2025, 1:04:23 PM] [READY] Bot is now ready!

[5/3/2025, 1:04:47 PM] [READY] Bot is now ready!

[5/3/2025, 1:05:02 PM] [READY] Bot is now ready!

[5/3/2025, 1:07:54 PM] [READY] Bot is now ready!

[5/3/2025, 1:13:14 PM] [READY] Bot is now ready!

[5/3/2025, 1:13:41 PM] [READY] Bot is now ready!

[5/3/2025, 1:14:15 PM] [READY] Bot is now ready!

[5/3/2025, 1:14:50 PM] [READY] Bot is now ready!

[5/3/2025, 1:15:23 PM] [READY] Bot is now ready!

[5/3/2025, 1:15:50 PM] [READY] Bot is now ready!

[5/3/2025, 1:17:15 PM] [READY] Bot is now ready!

[5/3/2025, 1:17:51 PM] [READY] Bot is now ready!

[5/3/2025, 1:18:30 PM] [READY] Bot is now ready!

[5/3/2025, 1:19:07 PM] [READY] Bot is now ready!

[5/3/2025, 1:19:40 PM] [READY] Bot is now ready!

[5/3/2025, 1:20:20 PM] [READY] Bot is now ready!

[5/3/2025, 1:22:12 PM] [READY] Bot is now ready!

[5/3/2025, 1:34:16 PM] [READY] Bot is now ready!

[5/3/2025, 1:35:07 PM] [READY] Bot is now ready!

[5/3/2025, 1:35:52 PM] [READY] Bot is now ready!

[5/3/2025, 1:36:20 PM] [READY] Bot is now ready!

[5/3/2025, 1:39:39 PM] [READY] Bot is now ready!

[5/3/2025, 1:39:53 PM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'startsWith')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:501:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/3/2025, 1:41:32 PM] [READY] Bot is now ready!

[5/3/2025, 1:41:48 PM] [READY] Bot is now ready!

[5/3/2025, 1:42:08 PM] [READY] Bot is now ready!

[5/3/2025, 1:42:27 PM] [READY] Bot is now ready!

[5/3/2025, 1:42:45 PM] [READY] Bot is now ready!

[5/3/2025, 1:42:47 PM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'startsWith')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1271:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/3/2025, 1:43:04 PM] [READY] Bot is now ready!

[5/3/2025, 1:43:10 PM] [READY] Bot is now ready!

[5/3/2025, 1:43:21 PM] [READY] Bot is now ready!

[5/3/2025, 1:43:25 PM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'startsWith')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1271:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/3/2025, 1:43:38 PM] [READY] Bot is now ready!

[5/3/2025, 1:43:51 PM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'startsWith')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:1271:28)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/3/2025, 1:44:08 PM] [READY] Bot is now ready!

[5/3/2025, 1:44:27 PM] [READY] Bot is now ready!

[5/3/2025, 1:44:55 PM] [READY] Bot is now ready!

[5/3/2025, 1:45:51 PM] [READY] Bot is now ready!

[5/3/2025, 1:46:36 PM] [READY] Bot is now ready!

[5/3/2025, 1:48:00 PM] [READY] Bot is now ready!

[5/3/2025, 1:48:56 PM] [READY] Bot is now ready!

[5/3/2025, 1:49:07 PM] [SLASH COMMAND] /clear used by skelee.dev#0

[5/3/2025, 1:49:19 PM] [READY] Bot is now ready!

[5/3/2025, 1:51:42 PM] [READY] Bot is now ready!

[5/3/2025, 2:08:25 PM] [READY] Bot is now ready!

[5/3/2025, 2:22:59 PM] [READY] Bot is now ready!

[5/3/2025, 2:24:26 PM] [READY] Bot is now ready!

[5/3/2025, 2:25:57 PM] [READY] Bot is now ready!

[5/3/2025, 2:26:40 PM] [READY] Bot is now ready!

[5/3/2025, 2:27:16 PM] [READY] Bot is now ready!

[5/3/2025, 2:27:58 PM] [READY] Bot is now ready!

[5/3/2025, 2:28:34 PM] [READY] Bot is now ready!

[5/3/2025, 5:24:51 PM] [READY] Bot is now ready!

[5/3/2025, 5:27:52 PM] [READY] Bot is now ready!

[5/3/2025, 5:28:10 PM] [READY] Bot is now ready!

[5/3/2025, 5:28:30 PM] [READY] Bot is now ready!

[5/3/2025, 5:28:46 PM] [READY] Bot is now ready!

[5/3/2025, 5:30:22 PM] [READY] Bot is now ready!

[5/3/2025, 5:31:26 PM] [READY] Bot is now ready!

[5/3/2025, 5:32:53 PM] [READY] Bot is now ready!

[5/3/2025, 5:33:04 PM] [READY] Bot is now ready!

[5/3/2025, 5:33:18 PM] [READY] Bot is now ready!

[5/3/2025, 5:33:26 PM] [READY] Bot is now ready!

[5/3/2025, 5:33:42 PM] [READY] Bot is now ready!

[5/3/2025, 5:34:05 PM] [READY] Bot is now ready!

[5/3/2025, 5:34:20 PM] [READY] Bot is now ready!

[5/3/2025, 5:34:34 PM] [READY] Bot is now ready!

[5/3/2025, 5:34:46 PM] [READY] Bot is now ready!

[5/3/2025, 5:35:02 PM] [READY] Bot is now ready!

[5/3/2025, 5:35:18 PM] [READY] Bot is now ready!

[5/3/2025, 5:35:35 PM] [READY] Bot is now ready!

[5/3/2025, 5:39:55 PM] [READY] Bot is now ready!

[5/3/2025, 5:42:36 PM] [READY] Bot is now ready!

[5/3/2025, 5:42:58 PM] [READY] Bot is now ready!

[5/3/2025, 5:43:19 PM] [READY] Bot is now ready!

[5/3/2025, 5:43:39 PM] [READY] Bot is now ready!

[5/3/2025, 5:44:01 PM] [READY] Bot is now ready!

[5/3/2025, 5:44:28 PM] [READY] Bot is now ready!

[5/3/2025, 5:45:18 PM] [READY] Bot is now ready!

[5/3/2025, 6:05:23 PM] [READY] Bot is now ready!

[5/3/2025, 6:06:40 PM] [READY] Bot is now ready!

[5/3/2025, 6:07:21 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:543
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/3/2025, 6:07:48 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:543
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/3/2025, 6:08:08 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:543
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/3/2025, 6:08:39 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:543
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/3/2025, 6:54:38 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:11:08 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:14:13 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:14:19 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:14:59 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:16:07 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:17:56 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:25:08 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:30:10 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 7:30:18 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 8:07:25 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 8:07:26 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 8:08:39 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 8:10:00 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 8:10:11 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/3/2025, 8:10:17 PM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/4/2025, 12:02:37 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:534
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:02:49 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:534
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:04:27 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:534
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:04:38 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:534
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:04:49 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:481
          });
           ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:05:01 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:05:12 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:05:22 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:05:33 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:05:49 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:06:05 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:06:22 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:06:34 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:06:47 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:07:05 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:07:18 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:07:29 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:542
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:09:25 AM] [ERROR] [v1.5.0]
MongooseError: Operation `tickets.findOne()` buffering timed out after 10000ms
    at Timeout.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:187:23)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[5/4/2025, 12:11:15 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:604
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:11:34 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:604
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:11:57 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:604
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:12:29 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:604
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:12:59 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:605
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:13:14 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:615
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:13:28 AM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:615
  }, 10000);
          ^

SyntaxError: Unexpected token ')'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17

[5/4/2025, 12:18:54 AM] [READY] Bot is now ready!

[5/4/2025, 12:24:07 AM] [READY] Bot is now ready!

[5/4/2025, 12:24:24 AM] [READY] Bot is now ready!

[5/4/2025, 12:31:12 AM] [READY] Bot is now ready!

[5/4/2025, 12:38:55 AM] [READY] Bot is now ready!

[5/4/2025, 12:41:36 AM] [READY] Bot is now ready!

[5/4/2025, 12:42:11 AM] [READY] Bot is now ready!

[5/4/2025, 12:42:38 AM] [READY] Bot is now ready!

[5/4/2025, 12:42:51 AM] [READY] Bot is now ready!

[5/4/2025, 12:43:12 AM] [READY] Bot is now ready!

[5/4/2025, 12:43:29 AM] [READY] Bot is now ready!

[5/4/2025, 12:44:11 AM] [READY] Bot is now ready!

[5/4/2025, 12:44:30 AM] [READY] Bot is now ready!

[5/4/2025, 12:44:38 AM] [READY] Bot is now ready!

[5/4/2025, 12:45:24 AM] [READY] Bot is now ready!

[5/4/2025, 12:45:46 AM] [READY] Bot is now ready!

[5/4/2025, 12:46:07 AM] [READY] Bot is now ready!

[5/4/2025, 12:46:22 AM] [READY] Bot is now ready!

[5/4/2025, 12:46:58 AM] [READY] Bot is now ready!

[5/4/2025, 12:48:00 AM] [READY] Bot is now ready!

[5/4/2025, 12:51:49 AM] [READY] Bot is now ready!

[5/4/2025, 12:52:20 AM] [READY] Bot is now ready!

[5/4/2025, 12:52:37 AM] [READY] Bot is now ready!

[5/4/2025, 12:52:48 AM] [READY] Bot is now ready!

[5/4/2025, 12:53:19 AM] [READY] Bot is now ready!

[5/4/2025, 12:53:46 AM] [READY] Bot is now ready!

[5/4/2025, 12:54:04 AM] [READY] Bot is now ready!

[5/4/2025, 12:54:37 AM] [READY] Bot is now ready!

[5/4/2025, 12:55:01 AM] [READY] Bot is now ready!

[5/4/2025, 12:55:30 AM] [READY] Bot is now ready!

[5/4/2025, 1:04:02 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:316:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:04:02 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:316:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:04:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:15 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:316:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:04:16 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:16 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:16 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:38 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:316:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:04:38 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:316:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:04:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:04:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:15 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:05:16 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:05:17 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:17 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:17 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:17 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:17 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:17 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:44 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:05:44 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:44 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:44 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:51 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:05:52 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:52 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:05:52 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:26 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:06:28 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:06:28 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:28 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:28 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:28 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:28 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:28 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:49 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:06:50 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:50 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:06:50 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:07:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:04 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:09 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:07:09 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:09 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:09 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:40 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:07:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:07:41 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:08:18 AM] [unhandledRejection] [v1.5.0]
TypeError: client.channels.cache.get is not a function
    at updateEmbed (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\updateEmbed.js:23:43)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:331:1)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Module._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at MessagePort.<anonymous> (node:internal/main/worker_thread:212:26)

[5/4/2025, 1:08:18 AM] [unhandledRejection] [v1.5.0]
TypeError: client.users.cache.set is not a function
    at module.exports [as READY] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\READY.js:12:24)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:08:18 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:08:18 AM] [unhandledRejection] [v1.5.0]
TypeError: client.guilds.cache.get is not a function
    at module.exports [as GUILD_CREATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_CREATE.js:7:35)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 1:08:55 AM] [READY] Bot is now ready!

[5/4/2025, 1:08:56 AM] [READY] Bot is now ready!

[5/4/2025, 1:10:05 AM] [READY] Bot is now ready!

[5/4/2025, 1:10:51 AM] [READY] Bot is now ready!

[5/4/2025, 1:11:05 AM] [unhandledRejection] [v1.5.0]
ReferenceError: CRITICAL_MEMORY_THRESHOLD_PERCENT is not defined
    at logMemoryUsage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\memoryMonitor.js:97:27)
    at Object.startMonitoring (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\memoryMonitor.js:239:27)
    at Object.start (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:29:19)
    at Client.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:379:17)
    at Object.onceWrapper (node:events:633:26)
    at Client.emit (node:events:530:35)
    at WebSocketManager.triggerClientReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:384:17)
    at WebSocketManager.checkShardsReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:367:10)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:197:16)
    at WebSocketShard.emit (node:events:518:28)

[5/4/2025, 1:11:45 AM] [unhandledRejection] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'status')
    at Object.startMonitoring (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils\memoryMonitor.js:146:57)
    at Object.start (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:29:19)
    at Client.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:379:17)
    at Object.onceWrapper (node:events:633:26)
    at Client.emit (node:events:530:35)
    at WebSocketManager.triggerClientReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:384:17)
    at WebSocketManager.checkShardsReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:367:10)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:197:16)
    at WebSocketShard.emit (node:events:518:28)
    at WebSocketShard.checkReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketShard.js:184:12)

[5/4/2025, 1:12:09 AM] [READY] Bot is now ready!

[5/4/2025, 1:12:24 AM] [READY] Bot is now ready!

[5/4/2025, 1:12:39 AM] [READY] Bot is now ready!

[5/4/2025, 1:13:01 AM] [READY] Bot is now ready!

[5/4/2025, 1:13:33 AM] [READY] Bot is now ready!

[5/4/2025, 1:13:56 AM] [READY] Bot is now ready!

[5/4/2025, 1:14:24 AM] [READY] Bot is now ready!

[5/4/2025, 1:14:52 AM] [READY] Bot is now ready!

[5/4/2025, 1:15:27 AM] [READY] Bot is now ready!

[5/4/2025, 1:15:36 AM] [READY] Bot is now ready!

[5/4/2025, 1:15:51 AM] [READY] Bot is now ready!

[5/4/2025, 1:16:25 AM] [READY] Bot is now ready!

[5/4/2025, 1:18:16 AM] [READY] Bot is now ready!

[5/4/2025, 1:21:12 AM] [READY] Bot is now ready!

[5/4/2025, 1:24:32 AM] [READY] Bot is now ready!

[5/4/2025, 1:26:40 AM] [READY] Bot is now ready!

[5/4/2025, 1:28:18 AM] [READY] Bot is now ready!

[5/4/2025, 1:32:26 AM] [READY] Bot is now ready!

[5/4/2025, 1:34:42 AM] [READY] Bot is now ready!

[5/4/2025, 1:35:42 AM] [READY] Bot is now ready!

[5/4/2025, 1:35:47 AM] [READY] Bot is now ready!

[5/4/2025, 1:36:04 AM] [READY] Bot is now ready!

[5/4/2025, 1:40:17 AM] [READY] Bot is now ready!

[5/4/2025, 1:42:48 AM] [READY] Bot is now ready!

[5/4/2025, 1:42:58 AM] [READY] Bot is now ready!

[5/4/2025, 1:43:17 AM] [READY] Bot is now ready!

[5/4/2025, 1:43:36 AM] [READY] Bot is now ready!

[5/4/2025, 1:44:41 AM] [READY] Bot is now ready!

[5/4/2025, 1:44:56 AM] [READY] Bot is now ready!

[5/4/2025, 1:45:10 AM] [READY] Bot is now ready!

[5/4/2025, 1:45:44 AM] [READY] Bot is now ready!

[5/4/2025, 1:46:16 AM] [READY] Bot is now ready!

[5/4/2025, 1:46:23 AM] [READY] Bot is now ready!

[5/4/2025, 1:49:15 AM] [READY] Bot is now ready!

[5/4/2025, 1:54:24 AM] [READY] Bot is now ready!

[5/4/2025, 1:56:56 AM] [READY] Bot is now ready!

[5/4/2025, 1:58:48 AM] [READY] Bot is now ready!

[5/4/2025, 2:00:48 AM] [READY] Bot is now ready!

[5/4/2025, 2:05:42 AM] [READY] Bot is now ready!

[5/4/2025, 2:05:53 AM] [READY] Bot is now ready!

[5/4/2025, 2:05:55 AM] [READY] Bot is now ready!

[5/4/2025, 2:06:11 AM] [READY] Bot is now ready!

[5/4/2025, 2:06:24 AM] [READY] Bot is now ready!

[5/4/2025, 2:06:40 AM] [READY] Bot is now ready!

[5/4/2025, 2:06:51 AM] [READY] Bot is now ready!

[5/4/2025, 2:07:00 AM] [READY] Bot is now ready!

[5/4/2025, 2:07:11 AM] [READY] Bot is now ready!

[5/4/2025, 2:07:42 AM] [READY] Bot is now ready!

[5/4/2025, 2:08:03 AM] [READY] Bot is now ready!

[5/4/2025, 2:08:23 AM] [READY] Bot is now ready!

[5/4/2025, 2:08:36 AM] [READY] Bot is now ready!

[5/4/2025, 2:08:48 AM] [READY] Bot is now ready!

[5/4/2025, 2:09:02 AM] [READY] Bot is now ready!

[5/4/2025, 2:09:44 AM] [READY] Bot is now ready!

[5/4/2025, 2:10:07 AM] [READY] Bot is now ready!

[5/4/2025, 2:10:21 AM] [READY] Bot is now ready!

[5/4/2025, 2:10:34 AM] [READY] Bot is now ready!

[5/4/2025, 2:10:46 AM] [READY] Bot is now ready!

[5/4/2025, 2:10:53 AM] [READY] Bot is now ready!

[5/4/2025, 2:10:57 AM] [READY] Bot is now ready!

[5/4/2025, 2:11:10 AM] [READY] Bot is now ready!

[5/4/2025, 2:11:23 AM] [READY] Bot is now ready!

[5/4/2025, 2:11:38 AM] [READY] Bot is now ready!

[5/4/2025, 2:11:54 AM] [READY] Bot is now ready!

[5/4/2025, 2:12:08 AM] [READY] Bot is now ready!

[5/4/2025, 2:12:21 AM] [READY] Bot is now ready!

[5/4/2025, 2:12:37 AM] [READY] Bot is now ready!

[5/4/2025, 2:12:56 AM] [READY] Bot is now ready!

[5/4/2025, 2:13:13 AM] [READY] Bot is now ready!

[5/4/2025, 2:13:30 AM] [READY] Bot is now ready!

[5/4/2025, 2:13:50 AM] [READY] Bot is now ready!

[5/4/2025, 2:14:02 AM] [READY] Bot is now ready!

[5/4/2025, 2:14:18 AM] [READY] Bot is now ready!

[5/4/2025, 2:14:32 AM] [READY] Bot is now ready!

[5/4/2025, 2:14:48 AM] [READY] Bot is now ready!

[5/4/2025, 2:15:04 AM] [READY] Bot is now ready!

[5/4/2025, 2:15:16 AM] [READY] Bot is now ready!

[5/4/2025, 2:15:29 AM] [READY] Bot is now ready!

[5/4/2025, 2:15:38 AM] [READY] Bot is now ready!

[5/4/2025, 2:16:25 AM] [READY] Bot is now ready!

[5/4/2025, 2:16:48 AM] [READY] Bot is now ready!

[5/4/2025, 2:17:13 AM] [READY] Bot is now ready!

[5/4/2025, 2:17:54 AM] [READY] Bot is now ready!

[5/4/2025, 2:18:24 AM] [READY] Bot is now ready!

[5/4/2025, 2:18:37 AM] [MEMORY] Current usage: 77.17MB / 80.98MB (95.3%)

[5/4/2025, 2:18:52 AM] [READY] Bot is now ready!

[5/4/2025, 2:19:17 AM] [READY] Bot is now ready!

[5/4/2025, 2:20:19 AM] [READY] Bot is now ready!

[5/4/2025, 2:20:21 AM] [READY] Bot is now ready!

[5/4/2025, 2:23:18 AM] [MEMORY] Current usage: 71.31MB / 79.48MB (89.7%)

[5/4/2025, 2:23:20 AM] [MEMORY] Current usage: 77.40MB / 82.98MB (93.3%)

[5/4/2025, 2:24:56 AM] [READY] Bot is now ready!

[5/4/2025, 2:26:50 AM] [READY] Bot is now ready!

[5/4/2025, 2:27:24 AM] [READY] Bot is now ready!

[5/4/2025, 2:29:05 AM] [READY] Bot is now ready!

[5/4/2025, 4:55:08 AM] [READY] Bot is now ready!

[5/4/2025, 4:55:32 AM] [READY] Bot is now ready!

[5/4/2025, 4:56:17 AM] [READY] Bot is now ready!

[5/4/2025, 4:56:40 AM] [READY] Bot is now ready!

[5/4/2025, 5:04:39 AM] [READY] Bot is now ready!

[5/4/2025, 5:05:33 AM] [READY] Bot is now ready!

[5/4/2025, 5:05:44 AM] [READY] Bot is now ready!

[5/4/2025, 5:05:55 AM] [READY] Bot is now ready!

[5/4/2025, 5:08:54 AM] [MEMORY] Current usage: 67.28MB / 70.48MB (95.5%)

[5/4/2025, 5:20:54 AM] [MEMORY] Current usage: 70.11MB / 75.98MB (92.3%)

[5/4/2025, 5:32:32 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../utils/mongoDbHealth'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:22:23)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/4/2025, 5:32:32 AM] [MEMORY] Current usage: 75.97MB / 98.59MB (77.1%)

[5/4/2025, 5:32:45 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../utils/mongoDbHealth'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:22:23)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/4/2025, 5:32:46 AM] [MEMORY] Current usage: 66.78MB / 96.64MB (69.1%)

[5/4/2025, 5:34:01 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../utils/mongoDbHealth'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:22:23)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/4/2025, 5:34:02 AM] [unhandledRejection] [v1.5.0]
ReferenceError: TaskManager is not defined
    at Client.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:434:23)
    at Object.onceWrapper (node:events:633:26)
    at Client.emit (node:events:530:35)
    at WebSocketManager.triggerClientReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:384:17)
    at WebSocketManager.checkShardsReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:367:10)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:197:16)
    at WebSocketShard.emit (node:events:518:28)
    at WebSocketShard.checkReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketShard.js:184:12)
    at WebSocketShard.gotGuild (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketShard.js:158:10)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:237:15)

[5/4/2025, 5:34:15 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../utils/mongoDbHealth'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:22:23)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/4/2025, 5:34:16 AM] [unhandledRejection] [v1.5.0]
ReferenceError: SystemMonitor is not defined
    at Client.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:463:3)
    at Object.onceWrapper (node:events:633:26)
    at Client.emit (node:events:530:35)
    at WebSocketManager.triggerClientReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:384:17)
    at WebSocketManager.checkShardsReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:367:10)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:197:16)
    at WebSocketShard.emit (node:events:518:28)
    at WebSocketShard.checkReady (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketShard.js:184:12)
    at WebSocketShard.gotGuild (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketShard.js:158:10)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:237:15)

[5/4/2025, 5:34:30 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../utils/mongoDbHealth'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:22:23)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/4/2025, 5:34:41 AM] [unhandledRejection] [v1.5.0]
Error: Cannot find module '../utils/mongoDbHealth'
Require stack:
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js
- C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:22:23)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)

[5/4/2025, 5:36:21 AM] [ERROR] [v1.5.0]
ReferenceError: MongoDbHealth is not defined
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ready.js:139:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)

[5/4/2025, 5:36:43 AM] [READY] Bot is now ready!

[5/4/2025, 7:36:43 AM] [READY] Bot is now ready!

[5/4/2025, 7:36:52 AM] [READY] Bot is now ready!
[5/4/2025, 7:37:07 AM] [MEMORY] Usage: 75.24 MB/98.11 MB (76.7%)

[5/4/2025, 7:37:07 AM] [READY] Bot is now ready!
[5/4/2025, 7:37:19 AM] [MEMORY] Usage: 66.87 MB/96.11 MB (69.6%)

[5/4/2025, 7:37:19 AM] [READY] Bot is now ready!
[5/4/2025, 7:37:35 AM] [MEMORY] Usage: 66.48 MB/96.11 MB (69.2%)

[5/4/2025, 7:37:35 AM] [MEMORY] Initial usage: RSS: 150.51MB, Heap: 71.24MB/97.86MB (72.8%)

[5/4/2025, 7:37:35 AM] [READY] Bot is now ready!
[5/4/2025, 7:42:35 AM] [MEMORY] Usage: 68.7 MB/72.85 MB (94.3%)
[5/4/2025, 7:47:35 AM] [MEMORY] Usage: 65.55 MB/76.35 MB (85.9%)
[5/4/2025, 7:52:35 AM] [MEMORY] Usage: 69.95 MB/76.6 MB (91.3%)
[5/4/2025, 7:57:35 AM] [MEMORY] Usage: 65.03 MB/77.85 MB (83.5%)
[5/4/2025, 8:02:35 AM] [MEMORY] Usage: 68.58 MB/77.85 MB (88.1%)
[5/4/2025, 8:07:35 AM] [MEMORY] Usage: 62.01 MB/76.84 MB (80.7%)
[5/4/2025, 8:12:35 AM] [MEMORY] Usage: 65.6 MB/76.84 MB (85.4%)
[5/4/2025, 8:17:35 AM] [MEMORY] Usage: 59.39 MB/75.59 MB (78.6%)
[5/4/2025, 8:22:35 AM] [MEMORY] Usage: 62.76 MB/75.59 MB (83.0%)
[5/4/2025, 8:27:35 AM] [MEMORY] Usage: 58.65 MB/74.84 MB (78.4%)
[5/4/2025, 8:32:35 AM] [MEMORY] Usage: 61.45 MB/75.84 MB (81.0%)
[5/4/2025, 8:37:35 AM] [MEMORY] Usage: 65.21 MB/74.84 MB (87.1%)
[5/4/2025, 8:42:35 AM] [MEMORY] Usage: 61.09 MB/74.84 MB (81.6%)
[5/4/2025, 8:47:35 AM] [MEMORY] Usage: 64.8 MB/74.84 MB (86.6%)
[5/4/2025, 8:52:35 AM] [MEMORY] Usage: 60.54 MB/75.09 MB (80.6%)
[5/4/2025, 8:57:35 AM] [MEMORY] Usage: 64.77 MB/76.09 MB (85.1%)
[5/4/2025, 9:02:35 AM] [MEMORY] Usage: 60.54 MB/75.34 MB (80.4%)
[5/4/2025, 9:07:35 AM] [MEMORY] Usage: 63.75 MB/75.34 MB (84.6%)
[5/4/2025, 9:12:35 AM] [MEMORY] Usage: 59.31 MB/75.59 MB (78.5%)
[5/4/2025, 9:17:35 AM] [MEMORY] Usage: 62.1 MB/75.59 MB (82.2%)
[5/4/2025, 9:22:35 AM] [MEMORY] Usage: 65.3 MB/75.59 MB (86.4%)
[5/4/2025, 9:27:35 AM] [MEMORY] Usage: 60.57 MB/75.34 MB (80.4%)
[5/4/2025, 9:32:35 AM] [MEMORY] Usage: 63.81 MB/75.34 MB (84.7%)
[5/4/2025, 9:37:35 AM] [MEMORY] Usage: 66.28 MB/75.34 MB (88.0%)
[5/4/2025, 9:42:35 AM] [MEMORY] Usage: 61.08 MB/75.09 MB (81.4%)
[5/4/2025, 9:47:35 AM] [MEMORY] Usage: 63.8 MB/75.09 MB (85.0%)
[5/4/2025, 9:52:35 AM] [MEMORY] Usage: 66.25 MB/75.09 MB (88.2%)
[5/4/2025, 9:57:35 AM] [MEMORY] Usage: 60.92 MB/75.34 MB (80.9%)
[5/4/2025, 10:02:35 AM] [MEMORY] Usage: 64.83 MB/75.59 MB (85.8%)
[5/4/2025, 10:07:35 AM] [MEMORY] Usage: 61.52 MB/76.34 MB (80.6%)
[5/4/2025, 10:12:35 AM] [MEMORY] Usage: 64.55 MB/75.34 MB (85.7%)
[5/4/2025, 10:17:35 AM] [MEMORY] Usage: 66.8 MB/75.34 MB (88.7%)
[5/4/2025, 10:22:35 AM] [MEMORY] Usage: 61.96 MB/75.59 MB (82.0%)
[5/4/2025, 10:27:35 AM] [MEMORY] Usage: 64.21 MB/76.59 MB (83.8%)
[5/4/2025, 10:32:35 AM] [MEMORY] Usage: 66.74 MB/75.59 MB (88.3%)
[5/4/2025, 10:37:35 AM] [MEMORY] Usage: 61.83 MB/75.84 MB (81.5%)
[5/4/2025, 10:42:35 AM] [MEMORY] Usage: 64.59 MB/75.84 MB (85.2%)
[5/4/2025, 10:47:35 AM] [MEMORY] Usage: 67.42 MB/75.84 MB (88.9%)
[5/4/2025, 10:52:35 AM] [MEMORY] Usage: 62.42 MB/76.09 MB (82.0%)
[5/4/2025, 10:57:35 AM] [MEMORY] Usage: 64.95 MB/76.09 MB (85.4%)
[5/4/2025, 11:02:35 AM] [MEMORY] Usage: 67.37 MB/76.09 MB (88.6%)
[5/4/2025, 11:07:35 AM] [MEMORY] Usage: 62.4 MB/76.34 MB (81.7%)
[5/4/2025, 11:12:35 AM] [MEMORY] Usage: 65.4 MB/77.34 MB (84.6%)
[5/4/2025, 11:17:35 AM] [MEMORY] Usage: 68.39 MB/77.34 MB (88.4%)
[5/4/2025, 11:22:35 AM] [MEMORY] Usage: 63.35 MB/76.59 MB (82.7%)
[5/4/2025, 11:27:35 AM] [MEMORY] Usage: 66.24 MB/76.59 MB (86.5%)
[5/4/2025, 11:32:35 AM] [MEMORY] Usage: 60.84 MB/76.84 MB (79.2%)
[5/4/2025, 11:37:35 AM] [MEMORY] Usage: 63.61 MB/76.84 MB (82.8%)
[5/4/2025, 11:42:35 AM] [MEMORY] Usage: 66.3 MB/76.84 MB (86.3%)
[5/4/2025, 11:47:35 AM] [MEMORY] Usage: 60.69 MB/76.59 MB (79.3%)
[5/4/2025, 11:52:35 AM] [MEMORY] Usage: 63.49 MB/76.84 MB (82.6%)
[5/4/2025, 11:57:35 AM] [MEMORY] Usage: 65.74 MB/76.84 MB (85.6%)
[5/4/2025, 12:02:35 PM] [MEMORY] Usage: 68.25 MB/76.84 MB (88.8%)
[5/4/2025, 12:07:35 PM] [MEMORY] Usage: 63.12 MB/77.09 MB (81.9%)
[5/4/2025, 12:12:35 PM] [MEMORY] Usage: 65.28 MB/78.09 MB (83.6%)
[5/4/2025, 12:17:35 PM] [MEMORY] Usage: 67.82 MB/77.09 MB (88.0%)
[5/4/2025, 12:22:35 PM] [MEMORY] Usage: 63.51 MB/77.09 MB (82.4%)
[5/4/2025, 12:27:35 PM] [MEMORY] Usage: 66.44 MB/77.09 MB (86.2%)
[5/4/2025, 12:32:35 PM] [MEMORY] Usage: 61.69 MB/77.34 MB (79.8%)
[5/4/2025, 12:37:35 PM] [MEMORY] Usage: 64.97 MB/77.34 MB (84.0%)
[5/4/2025, 12:42:35 PM] [MEMORY] Usage: 67.67 MB/77.34 MB (87.5%)
[5/4/2025, 12:47:35 PM] [MEMORY] Usage: 63.27 MB/76.84 MB (82.3%)
[5/4/2025, 12:52:35 PM] [MEMORY] Usage: 66.62 MB/76.84 MB (86.7%)
[5/4/2025, 12:57:35 PM] [MEMORY] Usage: 61.99 MB/77.09 MB (80.4%)
[5/4/2025, 1:02:35 PM] [MEMORY] Usage: 65.44 MB/77.09 MB (84.9%)
[5/4/2025, 1:07:35 PM] [MEMORY] Usage: 68.22 MB/77.09 MB (88.5%)
[5/4/2025, 1:12:35 PM] [MEMORY] Usage: 63.65 MB/77.34 MB (82.3%)
[5/4/2025, 1:17:35 PM] [MEMORY] Usage: 65.96 MB/77.34 MB (85.3%)
[5/4/2025, 1:22:35 PM] [MEMORY] Usage: 68.52 MB/77.34 MB (88.6%)
[5/4/2025, 1:27:35 PM] [MEMORY] Usage: 63.4 MB/77.59 MB (81.7%)
[5/4/2025, 1:32:35 PM] [MEMORY] Usage: 65.99 MB/77.59 MB (85.0%)
[5/4/2025, 1:37:35 PM] [MEMORY] Usage: 61.35 MB/76.84 MB (79.8%)
[5/4/2025, 1:42:35 PM] [MEMORY] Usage: 64.55 MB/76.84 MB (84.0%)
[5/4/2025, 1:47:35 PM] [MEMORY] Usage: 66.79 MB/76.84 MB (86.9%)
[5/4/2025, 1:52:35 PM] [MEMORY] Usage: 61.8 MB/77.09 MB (80.2%)
[5/4/2025, 1:57:35 PM] [MEMORY] Usage: 64.36 MB/77.09 MB (83.5%)
[5/4/2025, 2:02:35 PM] [MEMORY] Usage: 66.97 MB/77.09 MB (86.9%)
[5/4/2025, 2:07:35 PM] [MEMORY] Usage: 62.17 MB/77.34 MB (80.4%)
[5/4/2025, 2:12:35 PM] [MEMORY] Usage: 64.71 MB/77.34 MB (83.7%)
[5/4/2025, 2:17:35 PM] [MEMORY] Usage: 67.56 MB/77.34 MB (87.4%)
[5/4/2025, 2:22:35 PM] [MEMORY] Usage: 63.01 MB/77.59 MB (81.2%)
[5/4/2025, 2:27:35 PM] [MEMORY] Usage: 65.23 MB/78.59 MB (83.0%)
[5/4/2025, 2:32:35 PM] [MEMORY] Usage: 67.96 MB/77.59 MB (87.6%)
[5/4/2025, 2:37:35 PM] [MEMORY] Usage: 62.03 MB/77.34 MB (80.2%)
[5/4/2025, 2:42:35 PM] [MEMORY] Usage: 64.83 MB/77.34 MB (83.8%)
[5/4/2025, 2:47:35 PM] [MEMORY] Usage: 67.1 MB/77.34 MB (86.8%)
[5/4/2025, 2:52:35 PM] [MEMORY] Usage: 61.61 MB/77.59 MB (79.4%)
[5/4/2025, 2:57:35 PM] [MEMORY] Usage: 64.05 MB/77.59 MB (82.6%)
[5/4/2025, 3:02:35 PM] [MEMORY] Usage: 67.03 MB/77.59 MB (86.4%)
[5/4/2025, 3:07:35 PM] [MEMORY] Usage: 61.57 MB/77.09 MB (79.9%)
[5/4/2025, 3:12:35 PM] [MEMORY] Usage: 64.09 MB/77.09 MB (83.1%)
[5/4/2025, 3:17:35 PM] [MEMORY] Usage: 66.6 MB/77.09 MB (86.4%)
[5/4/2025, 3:22:35 PM] [MEMORY] Usage: 61.65 MB/77.34 MB (79.7%)
[5/4/2025, 3:27:35 PM] [MEMORY] Usage: 64.52 MB/77.34 MB (83.4%)
[5/4/2025, 3:32:35 PM] [MEMORY] Usage: 66.97 MB/77.34 MB (86.6%)
[5/4/2025, 3:37:35 PM] [MEMORY] Usage: 68.97 MB/77.34 MB (89.2%)
[5/4/2025, 3:42:35 PM] [MEMORY] Usage: 62.99 MB/77.34 MB (81.4%)
[5/4/2025, 3:47:35 PM] [MEMORY] Usage: 65.32 MB/77.34 MB (84.5%)
[5/4/2025, 3:52:35 PM] [MEMORY] Usage: 67.46 MB/78.34 MB (86.1%)
[5/4/2025, 3:57:35 PM] [MEMORY] Usage: 61.74 MB/77.34 MB (79.8%)
[5/4/2025, 4:02:35 PM] [MEMORY] Usage: 64.34 MB/77.59 MB (82.9%)
[5/4/2025, 4:07:35 PM] [MEMORY] Usage: 66.41 MB/77.59 MB (85.6%)
[5/4/2025, 4:12:35 PM] [MEMORY] Usage: 61.53 MB/77.84 MB (79.0%)
[5/4/2025, 4:17:35 PM] [MEMORY] Usage: 64.12 MB/77.84 MB (82.4%)
[5/4/2025, 4:22:35 PM] [MEMORY] Usage: 65.97 MB/78.84 MB (83.7%)
[5/4/2025, 4:27:35 PM] [MEMORY] Usage: 68.09 MB/77.84 MB (87.5%)
[5/4/2025, 4:32:35 PM] [MEMORY] Usage: 62.22 MB/78.09 MB (79.7%)
[5/4/2025, 4:37:35 PM] [MEMORY] Usage: 63.82 MB/79.09 MB (80.7%)
[5/4/2025, 4:42:35 PM] [MEMORY] Usage: 66.13 MB/78.09 MB (84.7%)
[5/4/2025, 4:47:35 PM] [MEMORY] Usage: 68.36 MB/79.09 MB (86.4%)
[5/4/2025, 4:52:35 PM] [MEMORY] Usage: 63.08 MB/78.09 MB (80.8%)
[5/4/2025, 4:57:35 PM] [MEMORY] Usage: 65.25 MB/78.09 MB (83.6%)
[5/4/2025, 5:02:35 PM] [MEMORY] Usage: 67.94 MB/79.09 MB (85.9%)
[5/4/2025, 5:07:35 PM] [MEMORY] Usage: 62.87 MB/78.34 MB (80.3%)
[5/4/2025, 5:12:35 PM] [MEMORY] Usage: 65.15 MB/78.34 MB (83.2%)
[5/4/2025, 5:17:35 PM] [MEMORY] Usage: 67.31 MB/78.34 MB (85.9%)
[5/4/2025, 5:22:35 PM] [MEMORY] Usage: 69.04 MB/78.34 MB (88.1%)
[5/4/2025, 5:27:35 PM] [MEMORY] Usage: 63.53 MB/77.84 MB (81.6%)
[5/4/2025, 5:32:35 PM] [MEMORY] Usage: 65.95 MB/77.84 MB (84.7%)
[5/4/2025, 5:37:35 PM] [MEMORY] Usage: 68.05 MB/77.84 MB (87.4%)
[5/4/2025, 5:42:35 PM] [MEMORY] Usage: 62 MB/77.84 MB (79.7%)
[5/4/2025, 5:47:35 PM] [MEMORY] Usage: 63.89 MB/77.84 MB (82.1%)
[5/4/2025, 5:52:35 PM] [MEMORY] Usage: 66.45 MB/77.84 MB (85.4%)
[5/4/2025, 5:57:35 PM] [MEMORY] Usage: 68.64 MB/77.84 MB (88.2%)
[5/4/2025, 6:02:35 PM] [MEMORY] Usage: 62.51 MB/77.84 MB (80.3%)
[5/4/2025, 6:07:35 PM] [MEMORY] Usage: 65.16 MB/77.84 MB (83.7%)
[5/4/2025, 6:12:35 PM] [MEMORY] Usage: 67.02 MB/78.84 MB (85.0%)
[5/4/2025, 6:17:35 PM] [MEMORY] Usage: 69.23 MB/77.84 MB (88.9%)
[5/4/2025, 6:22:35 PM] [MEMORY] Usage: 63.65 MB/78.09 MB (81.5%)
[5/4/2025, 6:27:35 PM] [MEMORY] Usage: 65.74 MB/78.09 MB (84.2%)
[5/4/2025, 6:32:35 PM] [MEMORY] Usage: 67.98 MB/79.09 MB (86.0%)
[5/4/2025, 6:37:35 PM] [MEMORY] Usage: 62.62 MB/78.09 MB (80.2%)
[5/4/2025, 6:42:35 PM] [MEMORY] Usage: 64.99 MB/78.09 MB (83.2%)
[5/4/2025, 6:47:35 PM] [MEMORY] Usage: 67.07 MB/79.09 MB (84.8%)
[5/4/2025, 6:52:35 PM] [MEMORY] Usage: 68.73 MB/78.09 MB (88.0%)
[5/4/2025, 6:57:35 PM] [MEMORY] Usage: 62.68 MB/78.09 MB (80.3%)
[5/4/2025, 7:02:35 PM] [MEMORY] Usage: 65.23 MB/78.09 MB (83.5%)
[5/4/2025, 7:07:35 PM] [MEMORY] Usage: 67.07 MB/78.09 MB (85.9%)
[5/4/2025, 7:12:35 PM] [MEMORY] Usage: 61.47 MB/78.09 MB (78.7%)
[5/4/2025, 7:17:35 PM] [MEMORY] Usage: 64.04 MB/78.09 MB (82.0%)
[5/4/2025, 7:22:35 PM] [MEMORY] Usage: 66.13 MB/78.09 MB (84.7%)
[5/4/2025, 7:27:35 PM] [MEMORY] Usage: 68 MB/78.09 MB (87.1%)
[5/4/2025, 7:32:35 PM] [MEMORY] Usage: 62.1 MB/78.34 MB (79.3%)
[5/4/2025, 7:37:35 PM] [MEMORY] Usage: 65.02 MB/78.34 MB (83.0%)
[5/4/2025, 7:42:35 PM] [MEMORY] Usage: 66.65 MB/78.34 MB (85.1%)
[5/4/2025, 7:47:35 PM] [MEMORY] Usage: 69.08 MB/78.34 MB (88.2%)
[5/4/2025, 7:52:35 PM] [MEMORY] Usage: 63.44 MB/78.09 MB (81.2%)
[5/4/2025, 7:57:35 PM] [MEMORY] Usage: 66.45 MB/78.09 MB (85.1%)
[5/4/2025, 8:02:35 PM] [MEMORY] Usage: 69.12 MB/78.09 MB (88.5%)
[5/4/2025, 8:07:35 PM] [MEMORY] Usage: 64.06 MB/78.09 MB (82.0%)
[5/4/2025, 8:12:35 PM] [MEMORY] Usage: 67.01 MB/78.09 MB (85.8%)
[5/4/2025, 8:17:35 PM] [MEMORY] Usage: 69.74 MB/78.09 MB (89.3%)
[5/4/2025, 8:22:35 PM] [MEMORY] Usage: 64.03 MB/78.09 MB (82.0%)
[5/4/2025, 8:27:35 PM] [MEMORY] Usage: 66.41 MB/79.09 MB (84.0%)
[5/4/2025, 8:32:35 PM] [MEMORY] Usage: 69.15 MB/78.09 MB (88.6%)
[5/4/2025, 8:34:27 PM] [MEMORY] Usage: 75.29 MB/96.08 MB (78.4%)

[5/4/2025, 8:34:28 PM] [READY] Bot is now ready!

[5/4/2025, 8:34:28 PM] [MEMORY] Initial usage: RSS: 148.64MB, Heap: 68.04MB/96.35MB (70.6%)
[5/4/2025, 8:36:13 PM] [MEMORY] Usage: 66.55 MB/96.58 MB (68.9%)

[5/4/2025, 8:36:13 PM] [MEMORY] Initial usage: RSS: 151.48MB, Heap: 71.14MB/97.58MB (72.9%)

[5/4/2025, 8:36:13 PM] [READY] Bot is now ready!
[5/4/2025, 8:37:15 PM] [MEMORY] Usage: 75.45 MB/95.83 MB (78.7%)

[5/4/2025, 8:37:15 PM] [MEMORY] Initial usage: RSS: 148.30MB, Heap: 68.06MB/96.10MB (70.8%)

[5/4/2025, 8:37:15 PM] [READY] Bot is now ready!
[5/4/2025, 8:37:24 PM] [MEMORY] Usage: 66.07 MB/95.58 MB (69.1%)

[5/4/2025, 8:37:25 PM] [MEMORY] Initial usage: RSS: 150.47MB, Heap: 70.73MB/97.58MB (72.5%)

[5/4/2025, 8:37:25 PM] [READY] Bot is now ready!
[5/4/2025, 8:37:48 PM] [MEMORY] Usage: 66.68 MB/96.08 MB (69.4%)

[5/4/2025, 8:37:48 PM] [MEMORY] Initial usage: RSS: 150.85MB, Heap: 71.35MB/97.58MB (73.1%)

[5/4/2025, 8:37:48 PM] [READY] Bot is now ready!
[5/4/2025, 8:39:10 PM] [MEMORY] Usage: 66.66 MB/95.58 MB (69.7%)

[5/4/2025, 8:39:10 PM] [MEMORY] Initial usage: RSS: 151.34MB, Heap: 71.42MB/97.58MB (73.2%)

[5/4/2025, 8:39:10 PM] [READY] Bot is now ready!
[5/4/2025, 8:39:28 PM] [MEMORY] Usage: 66.32 MB/96.58 MB (68.7%)

[5/4/2025, 8:39:28 PM] [READY] Bot is now ready!

[5/4/2025, 8:39:28 PM] [MEMORY] Initial usage: RSS: 151.07MB, Heap: 71.18MB/97.83MB (72.8%)
[5/4/2025, 8:40:12 PM] [MEMORY] Usage: 66.58 MB/97.08 MB (68.6%)

[5/4/2025, 8:40:12 PM] [MEMORY] Initial usage: RSS: 153.23MB, Heap: 68.71MB/103.33MB (66.5%)

[5/4/2025, 8:40:12 PM] [READY] Bot is now ready!
[5/4/2025, 8:45:12 PM] [MEMORY] Usage: 66.6 MB/71.6 MB (93.0%)
[5/4/2025, 8:49:39 PM] [MEMORY] Usage: 75.45 MB/96.58 MB (78.1%)

[5/4/2025, 8:50:23 PM] [ERROR] [v1.5.0]
MongoNotConnectedError: Client must be connected before running operations
    at autoConnect (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongodb\lib\operations\execute_operation.js:95:19)
    at executeOperation (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongodb\lib\operations\execute_operation.js:38:40)
    at FindCursor._initialize (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongodb\lib\cursor\find_cursor.js:61:73)
    at FindCursor.cursorInit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongodb\lib\cursor\abstract_cursor.js:633:38)
    at FindCursor.fetchBatch (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongodb\lib\cursor\abstract_cursor.js:667:24)
    at FindCursor.next (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongodb\lib\cursor\abstract_cursor.js:347:28)
    at Collection.findOne (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongodb\lib\collection.js:277:34)
    at NativeCollection.<computed> [as findOne] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\drivers\node-mongodb-native\collection.js:246:33)
    at model.Query._findOne (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\query.js:2661:45)
    at model.Query.exec (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\mongoose\lib\query.js:4548:80)
[5/4/2025, 8:51:20 PM] [MEMORY] Usage: 75.45 MB/97.08 MB (77.7%)

[5/4/2025, 8:51:20 PM] [MEMORY] Initial usage: RSS: 148.14MB, Heap: 67.34MB/101.10MB (66.6%)

[5/4/2025, 8:51:20 PM] [READY] Bot is now ready!
[5/4/2025, 8:56:20 PM] [MEMORY] Usage: 66.31 MB/70.6 MB (93.9%)
[5/4/2025, 9:01:20 PM] [MEMORY] Usage: 66.9 MB/71.35 MB (93.8%)
[5/4/2025, 9:06:20 PM] [MEMORY] Usage: 67.06 MB/71.1 MB (94.3%)
[5/4/2025, 9:11:20 PM] [MEMORY] Usage: 66.82 MB/71.1 MB (94.0%)
[5/4/2025, 9:16:20 PM] [MEMORY] Usage: 66.47 MB/71.6 MB (92.8%)
[5/4/2025, 9:19:40 PM] [MEMORY] Usage: 75.12 MB/96.59 MB (77.8%)

[5/4/2025, 9:19:40 PM] [MEMORY] Initial usage: RSS: 152.38MB, Heap: 68.76MB/102.09MB (67.4%)

[5/4/2025, 9:19:40 PM] [READY] Bot is now ready!
[5/4/2025, 9:24:40 PM] [MEMORY] Usage: 67.11 MB/71.1 MB (94.4%)
[5/4/2025, 9:29:40 PM] [MEMORY] Usage: 67.09 MB/71.1 MB (94.4%)
[5/4/2025, 9:34:40 PM] [MEMORY] Usage: 67.44 MB/71.85 MB (93.9%)
[5/4/2025, 9:39:40 PM] [MEMORY] Usage: 67.17 MB/71.85 MB (93.5%)
[5/4/2025, 9:44:40 PM] [MEMORY] Usage: 67.35 MB/71.85 MB (93.7%)
[5/4/2025, 9:48:01 PM] [MEMORY] Usage: 66.3 MB/95.83 MB (69.2%)

[5/4/2025, 9:48:01 PM] [MEMORY] Initial usage: RSS: 148.86MB, Heap: 68.65MB/102.58MB (66.9%)

[5/4/2025, 9:48:01 PM] [READY] Bot is now ready!

[5/4/2025, 9:48:07 PM] [SLASH COMMAND] /embed used by skelee.dev#0
[5/4/2025, 9:48:53 PM] [MEMORY] Usage: 66.28 MB/96.83 MB (68.4%)

[5/4/2025, 9:48:54 PM] [MEMORY] Initial usage: RSS: 148.71MB, Heap: 68.69MB/103.58MB (66.3%)

[5/4/2025, 9:48:54 PM] [READY] Bot is now ready!

[5/4/2025, 9:48:57 PM] [SLASH COMMAND] /embed used by skelee.dev#0

[5/4/2025, 9:49:25 PM] [SLASH COMMAND] /embed used by skelee.dev#0
[5/4/2025, 9:53:53 PM] [MEMORY] Usage: 68.13 MB/71.35 MB (95.5%)
[5/4/2025, 9:58:53 PM] [MEMORY] Usage: 67.87 MB/72.6 MB (93.5%)
[5/4/2025, 10:03:53 PM] [MEMORY] Usage: 68.49 MB/71.6 MB (95.7%)
[5/4/2025, 10:08:53 PM] [MEMORY] Usage: 68.65 MB/72.1 MB (95.2%)
[5/4/2025, 10:10:40 PM] [MEMORY] Usage: 66.15 MB/95.84 MB (69.0%)

[5/4/2025, 10:10:40 PM] [MEMORY] Initial usage: RSS: 149.17MB, Heap: 68.72MB/102.09MB (67.3%)

[5/4/2025, 10:10:40 PM] [READY] Bot is now ready!
[5/4/2025, 10:10:56 PM] [MEMORY] Usage: 66.32 MB/97.34 MB (68.1%)

[5/4/2025, 10:10:56 PM] [MEMORY] Initial usage: RSS: 150.23MB, Heap: 68.73MB/103.84MB (66.2%)

[5/4/2025, 10:10:56 PM] [READY] Bot is now ready!
[5/4/2025, 10:11:47 PM] [MEMORY] Usage: 66.07 MB/97.09 MB (68.0%)

[5/4/2025, 10:11:47 PM] [MEMORY] Initial usage: RSS: 150.23MB, Heap: 68.73MB/103.34MB (66.5%)

[5/4/2025, 10:11:47 PM] [READY] Bot is now ready!
[5/4/2025, 10:16:47 PM] [MEMORY] Usage: 68.63 MB/71.6 MB (95.8%)

[5/4/2025, 10:21:45 PM] [unhandledRejection] [v1.5.0]
ReferenceError: logsToSend is not defined
    at sendLogQueue (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:697:36)
    at logToWebhook (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:494:7)
    at checkUsersForDiscordRole (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:169:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[5/4/2025, 10:21:47 PM] [MEMORY] Usage: 66.08 MB/71.07 MB (93.0%)

[5/4/2025, 10:26:46 PM] [unhandledRejection] [v1.5.0]
ReferenceError: logsToSend is not defined
    at sendLogQueue (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:697:36)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:502:7)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
[5/4/2025, 10:26:47 PM] [MEMORY] Usage: 66.14 MB/71.57 MB (92.4%)
[5/4/2025, 10:31:47 PM] [MEMORY] Usage: 67.92 MB/72.1 MB (94.2%)

[5/4/2025, 10:36:45 PM] [unhandledRejection] [v1.5.0]
ReferenceError: logsToSend is not defined
    at sendLogQueue (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:697:36)
    at logToWebhook (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:494:7)
    at checkUsersForDiscordRole (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:169:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[5/4/2025, 10:36:47 PM] [MEMORY] Usage: 66.37 MB/72.07 MB (92.1%)

[5/4/2025, 10:41:46 PM] [unhandledRejection] [v1.5.0]
ReferenceError: logsToSend is not defined
    at sendLogQueue (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:697:36)
    at Timeout._onTimeout (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:502:7)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
[5/4/2025, 10:41:47 PM] [MEMORY] Usage: 66.4 MB/71.82 MB (92.5%)
[5/4/2025, 10:46:47 PM] [MEMORY] Usage: 68.56 MB/72.1 MB (95.1%)

[5/4/2025, 10:47:01 PM] [SLASH COMMAND] /clear used by skelee.dev#0

[5/4/2025, 10:51:45 PM] [unhandledRejection] [v1.5.0]
ReferenceError: logsToSend is not defined
    at sendLogQueue (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:697:36)
    at logToWebhook (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:494:7)
    at checkUsersForDiscordRole (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\index.js:169:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[5/4/2025, 10:51:47 PM] [MEMORY] Usage: 66.88 MB/73.07 MB (91.5%)
[5/4/2025, 10:52:31 PM] [MEMORY] Usage: 66.33 MB/96.59 MB (68.7%)

[5/4/2025, 10:52:31 PM] [MEMORY] Initial usage: RSS: 149.73MB, Heap: 68.80MB/103.34MB (66.6%)

[5/4/2025, 10:52:31 PM] [READY] Bot is now ready!

[5/4/2025, 10:53:29 PM] [unhandledRejection] [v1.5.0]
Error: Received one or more errors
    at _UnionValidator.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:1965:23)
    at _UnionValidator.parse (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@sapphire\shapeshift\dist\cjs\index.cjs:972:90)
    at EmbedBuilder.setTitle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\builders\dist\index.js:379:20)
    at ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:149:8)
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:206:9)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[5/4/2025, 10:54:19 PM] [MEMORY] Usage: 66.66 MB/96.84 MB (68.8%)

[5/4/2025, 10:54:19 PM] [MEMORY] Initial usage: RSS: 149.83MB, Heap: 68.71MB/102.84MB (66.8%)

[5/4/2025, 10:54:19 PM] [READY] Bot is now ready!
[5/4/2025, 10:54:51 PM] [MEMORY] Usage: 66.03 MB/96.59 MB (68.4%)

[5/4/2025, 10:54:51 PM] [MEMORY] Initial usage: RSS: 149.34MB, Heap: 68.74MB/103.09MB (66.7%)

[5/4/2025, 10:54:51 PM] [READY] Bot is now ready!
[5/4/2025, 10:55:09 PM] [MEMORY] Usage: 66.18 MB/96.59 MB (68.5%)

[5/4/2025, 10:55:09 PM] [MEMORY] Initial usage: RSS: 148.96MB, Heap: 68.76MB/102.84MB (66.9%)

[5/4/2025, 10:55:09 PM] [READY] Bot is now ready!
[5/4/2025, 10:55:27 PM] [MEMORY] Usage: 75.5 MB/97.59 MB (77.4%)

[5/4/2025, 10:55:28 PM] [MEMORY] Initial usage: RSS: 151.22MB, Heap: 67.16MB/100.35MB (66.9%)

[5/4/2025, 10:55:28 PM] [READY] Bot is now ready!
[5/4/2025, 10:55:45 PM] [MEMORY] Usage: 66.32 MB/96.84 MB (68.5%)

[5/4/2025, 10:55:45 PM] [MEMORY] Initial usage: RSS: 150.14MB, Heap: 68.82MB/103.09MB (66.8%)

[5/4/2025, 10:55:45 PM] [READY] Bot is now ready!
[5/4/2025, 10:56:01 PM] [MEMORY] Usage: 75.3 MB/97.34 MB (77.4%)

[5/4/2025, 10:56:01 PM] [MEMORY] Initial usage: RSS: 151.93MB, Heap: 67.15MB/100.85MB (66.6%)

[5/4/2025, 10:56:01 PM] [READY] Bot is now ready!
[5/4/2025, 10:56:52 PM] [MEMORY] Usage: 75.42 MB/96.84 MB (77.9%)

[5/4/2025, 10:56:52 PM] [READY] Bot is now ready!

[5/4/2025, 10:56:52 PM] [MEMORY] Initial usage: RSS: 151.13MB, Heap: 67.13MB/100.60MB (66.7%)
[5/4/2025, 10:57:03 PM] [MEMORY] Usage: 66.45 MB/97.59 MB (68.1%)

[5/4/2025, 10:57:03 PM] [MEMORY] Initial usage: RSS: 149.35MB, Heap: 68.70MB/103.59MB (66.3%)

[5/4/2025, 10:57:03 PM] [READY] Bot is now ready!
[5/4/2025, 10:57:16 PM] [MEMORY] Usage: 66.64 MB/97.09 MB (68.6%)

[5/4/2025, 10:57:16 PM] [MEMORY] Initial usage: RSS: 149.69MB, Heap: 68.73MB/103.09MB (66.7%)

[5/4/2025, 10:57:16 PM] [READY] Bot is now ready!
[5/4/2025, 10:57:30 PM] [MEMORY] Usage: 75.48 MB/96.84 MB (77.9%)

[5/4/2025, 10:57:30 PM] [MEMORY] Initial usage: RSS: 151.09MB, Heap: 67.17MB/100.35MB (66.9%)

[5/4/2025, 10:57:30 PM] [READY] Bot is now ready!
[5/4/2025, 10:57:44 PM] [MEMORY] Usage: 75.18 MB/97.34 MB (77.2%)

[5/4/2025, 10:57:44 PM] [MEMORY] Initial usage: RSS: 151.11MB, Heap: 67.22MB/100.85MB (66.7%)

[5/4/2025, 10:57:44 PM] [READY] Bot is now ready!
[5/4/2025, 10:57:58 PM] [MEMORY] Usage: 75.47 MB/96.59 MB (78.1%)

[5/4/2025, 10:57:59 PM] [MEMORY] Initial usage: RSS: 150.98MB, Heap: 67.14MB/100.10MB (67.1%)

[5/4/2025, 10:57:59 PM] [READY] Bot is now ready!
[5/4/2025, 10:58:15 PM] [MEMORY] Usage: 66.65 MB/97.09 MB (68.7%)

[5/4/2025, 10:58:15 PM] [MEMORY] Initial usage: RSS: 151.11MB, Heap: 68.80MB/103.59MB (66.4%)

[5/4/2025, 10:58:15 PM] [READY] Bot is now ready!
[5/4/2025, 10:58:30 PM] [MEMORY] Usage: 66.19 MB/96.34 MB (68.7%)

[5/4/2025, 10:58:30 PM] [READY] Bot is now ready!

[5/4/2025, 10:58:30 PM] [MEMORY] Initial usage: RSS: 150.07MB, Heap: 68.78MB/102.84MB (66.9%)
[5/4/2025, 10:58:55 PM] [MEMORY] Usage: 74.95 MB/96.84 MB (77.4%)

[5/4/2025, 10:58:55 PM] [MEMORY] Initial usage: RSS: 152.08MB, Heap: 68.75MB/102.34MB (67.2%)

[5/4/2025, 10:58:55 PM] [READY] Bot is now ready!
[5/4/2025, 10:59:13 PM] [MEMORY] Usage: 66.1 MB/97.34 MB (67.9%)

[5/4/2025, 10:59:14 PM] [MEMORY] Initial usage: RSS: 149.77MB, Heap: 68.78MB/103.84MB (66.2%)

[5/4/2025, 10:59:14 PM] [READY] Bot is now ready!

[5/4/2025, 11:02:43 PM] [SLASH COMMAND] /panel used by skelee.dev#0
[5/4/2025, 11:04:13 PM] [MEMORY] Usage: 70.82 MB/74.35 MB (95.3%)
[5/4/2025, 11:08:34 PM] [MEMORY] Usage: 66.46 MB/96.09 MB (69.2%)

[5/4/2025, 11:08:35 PM] [READY] Bot is now ready!

[5/4/2025, 11:08:35 PM] [MEMORY] Initial usage: RSS: 149.13MB, Heap: 69.08MB/102.59MB (67.3%)
[5/4/2025, 11:09:58 PM] [MEMORY] Usage: 75.27 MB/95.84 MB (78.5%)

[5/4/2025, 11:09:59 PM] [MEMORY] Initial usage: RSS: 150.61MB, Heap: 67.16MB/100.10MB (67.1%)

[5/4/2025, 11:09:59 PM] [READY] Bot is now ready!
[5/4/2025, 11:11:04 PM] [MEMORY] Usage: 66.05 MB/95.59 MB (69.1%)

[5/4/2025, 11:11:05 PM] [MEMORY] Initial usage: RSS: 149.09MB, Heap: 68.75MB/102.34MB (67.2%)

[5/4/2025, 11:11:05 PM] [READY] Bot is now ready!
[5/4/2025, 11:16:04 PM] [MEMORY] Usage: 70.46 MB/75.6 MB (93.2%)
[5/4/2025, 11:21:04 PM] [MEMORY] Usage: 70.81 MB/76.85 MB (92.1%)
[5/4/2025, 11:25:02 PM] [MEMORY] Usage: 66.42 MB/96.84 MB (68.6%)

[5/4/2025, 11:25:02 PM] [MEMORY] Initial usage: RSS: 150.19MB, Heap: 68.72MB/103.09MB (66.7%)

[5/4/2025, 11:25:02 PM] [READY] Bot is now ready!
[5/4/2025, 11:26:11 PM] [MEMORY] Usage: 66.53 MB/96.84 MB (68.7%)

[5/4/2025, 11:26:11 PM] [MEMORY] Initial usage: RSS: 150.00MB, Heap: 68.77MB/103.09MB (66.7%)

[5/4/2025, 11:26:11 PM] [READY] Bot is now ready!

[5/4/2025, 11:26:31 PM] [SLASH COMMAND] /reply used by botjason#0
[5/4/2025, 11:26:40 PM] [MEMORY] Usage: 66.39 MB/96.34 MB (68.9%)

[5/4/2025, 11:26:40 PM] [MEMORY] Initial usage: RSS: 149.11MB, Heap: 68.74MB/102.84MB (66.8%)

[5/4/2025, 11:26:40 PM] [READY] Bot is now ready!

[5/4/2025, 11:26:57 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:224
  const { attachment, timestamp } = await utils.saveTranscript(interaction);
          ^

SyntaxError: Identifier 'attachment' has already been declared
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17
[5/4/2025, 11:26:57 PM] [MEMORY] Usage: 66.52 MB/100.07 MB (66.5%)

[5/4/2025, 11:26:57 PM] [READY] Bot is now ready!

[5/4/2025, 11:26:57 PM] [MEMORY] Initial usage: RSS: 153.07MB, Heap: 68.85MB/103.09MB (66.8%)
[5/4/2025, 11:27:12 PM] [MEMORY] Usage: 66.48 MB/95.59 MB (69.5%)

[5/4/2025, 11:27:12 PM] [MEMORY] Initial usage: RSS: 149.39MB, Heap: 68.74MB/102.34MB (67.2%)

[5/4/2025, 11:27:12 PM] [READY] Bot is now ready!
[5/4/2025, 11:27:39 PM] [MEMORY] Usage: 75 MB/96.09 MB (78.0%)

[5/4/2025, 11:27:40 PM] [MEMORY] Initial usage: RSS: 152.10MB, Heap: 68.87MB/101.84MB (67.6%)

[5/4/2025, 11:27:40 PM] [READY] Bot is now ready!
[5/4/2025, 11:32:39 PM] [MEMORY] Usage: 71.56 MB/75.85 MB (94.3%)
[5/4/2025, 11:37:39 PM] [MEMORY] Usage: 69.35 MB/73.85 MB (93.9%)
[5/4/2025, 11:38:43 PM] [MEMORY] Usage: 66.55 MB/96.08 MB (69.3%)

[5/4/2025, 11:38:43 PM] [MEMORY] Initial usage: RSS: 149.54MB, Heap: 68.96MB/102.58MB (67.2%)

[5/4/2025, 11:38:43 PM] [READY] Bot is now ready!
[5/4/2025, 11:38:59 PM] [MEMORY] Usage: 66.46 MB/96.33 MB (69.0%)

[5/4/2025, 11:38:59 PM] [MEMORY] Initial usage: RSS: 150.29MB, Heap: 68.72MB/102.58MB (67.0%)

[5/4/2025, 11:38:59 PM] [READY] Bot is now ready!
[5/4/2025, 11:39:03 PM] [MEMORY] Usage: 66.42 MB/95.58 MB (69.5%)

[5/4/2025, 11:39:04 PM] [READY] Bot is now ready!

[5/4/2025, 11:39:04 PM] [MEMORY] Initial usage: RSS: 149.13MB, Heap: 68.79MB/102.58MB (67.1%)

[5/4/2025, 11:39:38 PM] [unhandledRejection] [v1.5.0]
C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\interactionCreate.js:785
    let supportRole = await **supportRole = await utils.checkIfUserHasSupportRoles(interaction);
                            ^^

SyntaxError: Unexpected token '**'
    at wrapSafe (node:internal/modules/cjs/loader:1486:18)
    at Module._compile (node:internal/modules/cjs/loader:1528:20)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\utils.js:204:17
[5/4/2025, 11:39:39 PM] [MEMORY] Usage: 66.41 MB/100.07 MB (66.4%)

[5/4/2025, 11:39:39 PM] [MEMORY] Initial usage: RSS: 152.16MB, Heap: 68.72MB/102.84MB (66.8%)

[5/4/2025, 11:39:39 PM] [READY] Bot is now ready!
[5/4/2025, 11:40:43 PM] [MEMORY] Usage: 66.47 MB/96.83 MB (68.6%)

[5/4/2025, 11:40:44 PM] [MEMORY] Initial usage: RSS: 149.46MB, Heap: 68.71MB/103.08MB (66.7%)

[5/4/2025, 11:40:44 PM] [READY] Bot is now ready!
[5/4/2025, 11:43:27 PM] [MEMORY] Usage: 66.55 MB/96.33 MB (69.1%)

[5/4/2025, 11:43:27 PM] [MEMORY] Initial usage: RSS: 149.70MB, Heap: 68.74MB/102.83MB (66.9%)

[5/4/2025, 11:43:27 PM] [READY] Bot is now ready!
[5/4/2025, 11:43:31 PM] [MEMORY] Usage: 75.27 MB/96.83 MB (77.7%)

[5/4/2025, 11:43:32 PM] [MEMORY] Initial usage: RSS: 151.01MB, Heap: 67.17MB/100.35MB (66.9%)

[5/4/2025, 11:43:32 PM] [READY] Bot is now ready!
[5/4/2025, 11:45:21 PM] [MEMORY] Usage: 66.53 MB/96.33 MB (69.1%)

[5/4/2025, 11:45:21 PM] [MEMORY] Initial usage: RSS: 148.82MB, Heap: 68.73MB/102.83MB (66.8%)

[5/4/2025, 11:45:21 PM] [READY] Bot is now ready!
[5/4/2025, 11:45:45 PM] [MEMORY] Usage: 66.31 MB/96.08 MB (69.0%)

[5/4/2025, 11:45:46 PM] [MEMORY] Initial usage: RSS: 149.19MB, Heap: 68.80MB/102.58MB (67.1%)

[5/4/2025, 11:45:46 PM] [READY] Bot is now ready!
[5/4/2025, 11:45:55 PM] [MEMORY] Usage: 66.1 MB/96.08 MB (68.8%)

[5/4/2025, 11:45:55 PM] [MEMORY] Initial usage: RSS: 149.07MB, Heap: 68.71MB/102.33MB (67.2%)

[5/4/2025, 11:45:55 PM] [READY] Bot is now ready!
[5/4/2025, 11:46:50 PM] [MEMORY] Usage: 66.06 MB/97.08 MB (68.0%)

[5/4/2025, 11:46:51 PM] [MEMORY] Initial usage: RSS: 149.35MB, Heap: 68.71MB/103.08MB (66.7%)

[5/4/2025, 11:46:51 PM] [READY] Bot is now ready!
[5/4/2025, 11:47:08 PM] [MEMORY] Usage: 66.36 MB/97.08 MB (68.4%)

[5/4/2025, 11:47:08 PM] [MEMORY] Initial usage: RSS: 149.85MB, Heap: 68.70MB/103.58MB (66.3%)

[5/4/2025, 11:47:08 PM] [READY] Bot is now ready!
[5/4/2025, 11:47:19 PM] [MEMORY] Usage: 74.87 MB/96.33 MB (77.7%)

[5/4/2025, 11:47:19 PM] [MEMORY] Initial usage: RSS: 151.02MB, Heap: 67.26MB/100.10MB (67.2%)

[5/4/2025, 11:47:19 PM] [READY] Bot is now ready!
[5/4/2025, 11:47:31 PM] [MEMORY] Usage: 75.12 MB/97.08 MB (77.4%)

[5/4/2025, 11:47:32 PM] [MEMORY] Initial usage: RSS: 152.66MB, Heap: 68.93MB/102.08MB (67.5%)

[5/4/2025, 11:47:32 PM] [READY] Bot is now ready!
[5/4/2025, 11:47:44 PM] [MEMORY] Usage: 74.97 MB/97.08 MB (77.2%)

[5/4/2025, 11:47:45 PM] [MEMORY] Initial usage: RSS: 153.02MB, Heap: 68.90MB/102.08MB (67.5%)

[5/4/2025, 11:47:45 PM] [READY] Bot is now ready!
[5/4/2025, 11:47:58 PM] [MEMORY] Usage: 75.05 MB/97.08 MB (77.3%)

[5/4/2025, 11:47:58 PM] [MEMORY] Initial usage: RSS: 152.29MB, Heap: 68.73MB/102.33MB (67.2%)

[5/4/2025, 11:47:58 PM] [READY] Bot is now ready!
[5/4/2025, 11:48:11 PM] [MEMORY] Usage: 66.06 MB/96.58 MB (68.4%)

[5/4/2025, 11:48:12 PM] [MEMORY] Initial usage: RSS: 149.71MB, Heap: 68.79MB/102.58MB (67.1%)

[5/4/2025, 11:48:12 PM] [READY] Bot is now ready!
[5/4/2025, 11:48:44 PM] [MEMORY] Usage: 75.29 MB/96.83 MB (77.8%)

[5/4/2025, 11:48:44 PM] [MEMORY] Initial usage: RSS: 152.07MB, Heap: 68.76MB/101.83MB (67.5%)

[5/4/2025, 11:48:44 PM] [READY] Bot is now ready!
[5/4/2025, 11:48:55 PM] [MEMORY] Usage: 66.41 MB/96.33 MB (68.9%)

[5/4/2025, 11:48:55 PM] [MEMORY] Initial usage: RSS: 149.11MB, Heap: 68.90MB/102.58MB (67.2%)

[5/4/2025, 11:48:55 PM] [READY] Bot is now ready!
[5/4/2025, 11:53:22 PM] [MEMORY] Usage: 66.29 MB/96.33 MB (68.8%)

[5/4/2025, 11:53:22 PM] [MEMORY] Initial usage: RSS: 149.25MB, Heap: 68.72MB/102.83MB (66.8%)

[5/4/2025, 11:53:22 PM] [READY] Bot is now ready!

[5/4/2025, 11:54:06 PM] [unhandledRejection] [v1.5.0]
DiscordAPIError[10003]: Unknown Channel
    at handleErrors (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SequentialHandler.runRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1149:23)
    at async SequentialHandler.queueRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:980:14)
    at async _REST.request (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async GuildChannelManager.edit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\managers\GuildChannelManager.js:315:21)

[5/4/2025, 11:54:06 PM] [unhandledRejection] [v1.5.0]
DiscordAPIError[10003]: Unknown Channel
    at handleErrors (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async SequentialHandler.runRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1149:23)
    at async SequentialHandler.queueRequest (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:980:14)
    at async _REST.request (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async GuildChannelManager.edit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\managers\GuildChannelManager.js:315:21)
    at async ArchiveTicket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\ticketClose.js:234:15)
[5/4/2025, 11:54:26 PM] [MEMORY] Usage: 74.96 MB/96.08 MB (78.0%)

[5/4/2025, 11:54:26 PM] [MEMORY] Initial usage: RSS: 152.63MB, Heap: 68.98MB/101.83MB (67.7%)

[5/4/2025, 11:54:26 PM] [READY] Bot is now ready!
[5/4/2025, 11:54:41 PM] [MEMORY] Usage: 66.26 MB/96.08 MB (69.0%)

[5/4/2025, 11:54:41 PM] [MEMORY] Initial usage: RSS: 149.80MB, Heap: 68.73MB/102.33MB (67.2%)

[5/4/2025, 11:54:41 PM] [READY] Bot is now ready!
[5/4/2025, 11:54:55 PM] [MEMORY] Usage: 66.14 MB/96.08 MB (68.8%)

[5/4/2025, 11:54:56 PM] [MEMORY] Initial usage: RSS: 148.91MB, Heap: 68.80MB/102.58MB (67.1%)

[5/4/2025, 11:54:56 PM] [READY] Bot is now ready!
[5/4/2025, 11:55:13 PM] [MEMORY] Usage: 66.4 MB/97.33 MB (68.2%)

[5/4/2025, 11:55:13 PM] [MEMORY] Initial usage: RSS: 150.58MB, Heap: 68.75MB/103.58MB (66.4%)

[5/4/2025, 11:55:13 PM] [READY] Bot is now ready!
[5/4/2025, 11:55:31 PM] [MEMORY] Usage: 66.33 MB/96.58 MB (68.7%)

[5/4/2025, 11:55:31 PM] [MEMORY] Initial usage: RSS: 149.86MB, Heap: 68.75MB/103.33MB (66.5%)

[5/4/2025, 11:55:31 PM] [READY] Bot is now ready!
[5/4/2025, 11:55:48 PM] [MEMORY] Usage: 66.41 MB/96.33 MB (68.9%)

[5/4/2025, 11:55:49 PM] [MEMORY] Initial usage: RSS: 149.04MB, Heap: 68.83MB/102.58MB (67.1%)

[5/4/2025, 11:55:49 PM] [READY] Bot is now ready!
[5/5/2025, 12:00:48 AM] [MEMORY] Usage: 67.73 MB/71.6 MB (94.6%)
[5/5/2025, 12:05:48 AM] [MEMORY] Usage: 67.48 MB/71.35 MB (94.6%)

[5/5/2025, 12:06:56 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of undefined (reading 'send')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\guildMemberUpdate.js:25:36)
    at Client.emit (node:events:518:28)
    at GuildMemberUpdateAction.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\actions\GuildMemberUpdate.js:30:74)
    at module.exports [as GUILD_MEMBER_UPDATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_MEMBER_UPDATE.js:4:36)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
[5/5/2025, 12:10:48 AM] [MEMORY] Usage: 67.49 MB/72.1 MB (93.6%)
[5/5/2025, 12:15:48 AM] [MEMORY] Usage: 67.61 MB/72.1 MB (93.8%)
[5/5/2025, 12:20:48 AM] [MEMORY] Usage: 67.52 MB/72.1 MB (93.7%)

[5/5/2025, 12:21:58 AM] [SLASH COMMAND] /bugreport used by skelee.dev#0

[5/5/2025, 12:22:09 AM] [SLASH COMMAND] /bugreport used by skelee.dev#0
[5/5/2025, 12:22:31 AM] [MEMORY] Usage: 75.2 MB/97.07 MB (77.5%)

[5/5/2025, 12:22:32 AM] [MEMORY] Initial usage: RSS: 151.67MB, Heap: 67.22MB/100.35MB (67.0%)

[5/5/2025, 12:22:32 AM] [READY] Bot is now ready!

[5/5/2025, 12:22:35 AM] [SLASH COMMAND] /bugreport used by skelee.dev#0

[5/5/2025, 12:22:57 AM] [SLASH COMMAND] /bugreport used by skelee.dev#0
[5/5/2025, 12:25:22 AM] [MEMORY] Usage: 66.46 MB/97.33 MB (68.3%)

[5/5/2025, 12:25:22 AM] [MEMORY] Initial usage: RSS: 149.76MB, Heap: 68.82MB/103.33MB (66.6%)

[5/5/2025, 12:25:22 AM] [READY] Bot is now ready!
[5/5/2025, 12:26:04 AM] [MEMORY] Usage: 65.93 MB/97.33 MB (67.7%)

[5/5/2025, 12:26:04 AM] [MEMORY] Initial usage: RSS: 148.71MB, Heap: 68.72MB/103.58MB (66.3%)

[5/5/2025, 12:26:04 AM] [READY] Bot is now ready!
[5/5/2025, 12:31:04 AM] [MEMORY] Usage: 66.97 MB/71.1 MB (94.2%)

[5/5/2025, 12:34:33 AM] [ERROR] [v1.5.0]
TypeError: Cannot read properties of null (reading 'send')
    at module.exports (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\events\guildMemberUpdate.js:60:65)
    at Client.emit (node:events:518:28)
    at GuildMemberUpdateAction.handle (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\actions\GuildMemberUpdate.js:30:74)
    at module.exports [as GUILD_MEMBER_UPDATE] (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\handlers\GUILD_MEMBER_UPDATE.js:4:36)
    at WebSocketManager.handlePacket (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:351:31)
    at WebSocketManager.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\discord.js\src\client\websocket\WebSocketManager.js:235:12)
    at WebSocketManager.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.<anonymous> (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1199:51)
    at WebSocketShard.emit (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@vladfrangu\async_event_emitter\dist\index.cjs:287:31)
    at WebSocketShard.onMessage (C:\Users\<USER>\Desktop\Mrk Bots\Manager Bot\node_modules\@discordjs\ws\dist\index.js:1016:14)
[5/5/2025, 12:36:04 AM] [MEMORY] Usage: 66.44 MB/71.1 MB (93.5%)
[5/5/2025, 12:41:04 AM] [MEMORY] Usage: 67.23 MB/71.35 MB (94.2%)
[5/5/2025, 12:46:04 AM] [MEMORY] Usage: 67.63 MB/71.85 MB (94.1%)
[5/5/2025, 12:51:04 AM] [MEMORY] Usage: 67.48 MB/71.85 MB (93.9%)
[5/5/2025, 12:56:04 AM] [MEMORY] Usage: 67.39 MB/72.35 MB (93.1%)
[5/5/2025, 1:01:04 AM] [MEMORY] Usage: 67.74 MB/72.85 MB (93.0%)
[5/5/2025, 1:06:04 AM] [MEMORY] Usage: 67.72 MB/74.1 MB (91.4%)
[5/5/2025, 1:11:04 AM] [MEMORY] Usage: 68.24 MB/72.85 MB (93.7%)
[5/5/2025, 1:16:04 AM] [MEMORY] Usage: 68.53 MB/72.85 MB (94.1%)
[5/5/2025, 1:21:04 AM] [MEMORY] Usage: 68.67 MB/73.85 MB (93.0%)
[5/5/2025, 1:26:04 AM] [MEMORY] Usage: 68.08 MB/73.1 MB (93.1%)
[5/5/2025, 1:31:04 AM] [MEMORY] Usage: 68.3 MB/73.1 MB (93.4%)
[5/5/2025, 1:36:04 AM] [MEMORY] Usage: 68.44 MB/73.35 MB (93.3%)
[5/5/2025, 1:41:04 AM] [MEMORY] Usage: 68.59 MB/73.35 MB (93.5%)
[5/5/2025, 1:46:04 AM] [MEMORY] Usage: 68.95 MB/73.85 MB (93.4%)
[5/5/2025, 1:51:04 AM] [MEMORY] Usage: 69.02 MB/73.85 MB (93.5%)
[5/5/2025, 1:56:04 AM] [MEMORY] Usage: 69.32 MB/73.6 MB (94.2%)
[5/5/2025, 2:01:04 AM] [MEMORY] Usage: 68.59 MB/73.6 MB (93.2%)
[5/5/2025, 2:06:04 AM] [MEMORY] Usage: 68.95 MB/73.6 MB (93.7%)
[5/5/2025, 2:11:04 AM] [MEMORY] Usage: 69.11 MB/73.6 MB (93.9%)
[5/5/2025, 2:16:04 AM] [MEMORY] Usage: 68.94 MB/74.1 MB (93.0%)
[5/5/2025, 2:21:04 AM] [MEMORY] Usage: 68.65 MB/74.85 MB (91.7%)
[5/5/2025, 2:26:04 AM] [MEMORY] Usage: 69.66 MB/75.1 MB (92.8%)
[5/5/2025, 2:31:04 AM] [MEMORY] Usage: 68.73 MB/73.6 MB (93.4%)
[5/5/2025, 2:36:04 AM] [MEMORY] Usage: 68.86 MB/73.85 MB (93.2%)
[5/5/2025, 2:41:04 AM] [MEMORY] Usage: 69.81 MB/74.35 MB (93.9%)
[5/5/2025, 2:46:04 AM] [MEMORY] Usage: 69.41 MB/74.6 MB (93.0%)
[5/5/2025, 2:51:04 AM] [MEMORY] Usage: 69.45 MB/75.6 MB (91.9%)
[5/5/2025, 2:56:04 AM] [MEMORY] Usage: 70.09 MB/75.6 MB (92.7%)
[5/5/2025, 3:01:04 AM] [MEMORY] Usage: 69.49 MB/74.1 MB (93.8%)
[5/5/2025, 3:06:04 AM] [MEMORY] Usage: 69.34 MB/74.1 MB (93.6%)
[5/5/2025, 3:11:04 AM] [MEMORY] Usage: 68.79 MB/75.35 MB (91.3%)
[5/5/2025, 3:16:04 AM] [MEMORY] Usage: 69.38 MB/74.35 MB (93.3%)
[5/5/2025, 3:21:04 AM] [MEMORY] Usage: 69.54 MB/74.1 MB (93.8%)
[5/5/2025, 3:26:04 AM] [MEMORY] Usage: 69.61 MB/74.6 MB (93.3%)
[5/5/2025, 3:31:04 AM] [MEMORY] Usage: 69.59 MB/74.35 MB (93.6%)
[5/5/2025, 3:36:04 AM] [MEMORY] Usage: 69.75 MB/74.1 MB (94.1%)
[5/5/2025, 3:41:04 AM] [MEMORY] Usage: 70.34 MB/74.85 MB (94.0%)
[5/5/2025, 3:46:04 AM] [MEMORY] Usage: 70.57 MB/74.85 MB (94.3%)
[5/5/2025, 3:51:04 AM] [MEMORY] Usage: 70.4 MB/75.35 MB (93.4%)
[5/5/2025, 3:56:04 AM] [MEMORY] Usage: 70.73 MB/76.85 MB (92.0%)
[5/5/2025, 4:01:04 AM] [MEMORY] Usage: 70.14 MB/75.6 MB (92.8%)
[5/5/2025, 4:06:04 AM] [MEMORY] Usage: 70.7 MB/75.6 MB (93.5%)
[5/5/2025, 4:11:04 AM] [MEMORY] Usage: 70.77 MB/75.6 MB (93.6%)
[5/5/2025, 4:16:04 AM] [MEMORY] Usage: 70.43 MB/76.85 MB (91.6%)
[5/5/2025, 4:21:04 AM] [MEMORY] Usage: 70.19 MB/76.85 MB (91.3%)
[5/5/2025, 4:26:04 AM] [MEMORY] Usage: 70.17 MB/75.35 MB (93.1%)
[5/5/2025, 4:28:02 AM] [MEMORY] Usage: 66.21 MB/97.81 MB (67.7%)

[5/5/2025, 4:28:02 AM] [MEMORY] Initial usage: RSS: 150.80MB, Heap: 68.70MB/104.06MB (66.0%)

[5/5/2025, 4:28:02 AM] [READY] Bot is now ready!
[5/5/2025, 4:33:02 AM] [MEMORY] Usage: 67.47 MB/71.1 MB (94.9%)
[5/5/2025, 4:38:02 AM] [MEMORY] Usage: 67.42 MB/71.35 MB (94.5%)
[5/5/2025, 4:43:02 AM] [MEMORY] Usage: 67.28 MB/72.35 MB (93.0%)
[5/5/2025, 4:48:02 AM] [MEMORY] Usage: 67.14 MB/71.6 MB (93.8%)
[5/5/2025, 4:53:02 AM] [MEMORY] Usage: 67.75 MB/71.6 MB (94.6%)
[5/5/2025, 4:58:02 AM] [MEMORY] Usage: 68.16 MB/71.6 MB (95.2%)
[5/5/2025, 5:03:02 AM] [MEMORY] Usage: 67.98 MB/71.6 MB (94.9%)
[5/5/2025, 5:08:02 AM] [MEMORY] Usage: 68.28 MB/71.85 MB (95.0%)
[5/5/2025, 5:13:02 AM] [MEMORY] Usage: 68.12 MB/72.1 MB (94.5%)
[5/5/2025, 5:18:02 AM] [MEMORY] Usage: 68.06 MB/72.1 MB (94.4%)
[5/5/2025, 5:23:02 AM] [MEMORY] Usage: 67.88 MB/72.35 MB (93.8%)
[5/5/2025, 5:28:02 AM] [MEMORY] Usage: 68.41 MB/72.35 MB (94.6%)
[5/5/2025, 5:33:02 AM] [MEMORY] Usage: 68.41 MB/73.35 MB (93.3%)
[5/5/2025, 5:38:02 AM] [MEMORY] Usage: 68.32 MB/73.1 MB (93.5%)
[5/5/2025, 5:43:02 AM] [MEMORY] Usage: 67.94 MB/73.35 MB (92.6%)
[5/5/2025, 5:48:02 AM] [MEMORY] Usage: 68.54 MB/72.6 MB (94.4%)
[5/5/2025, 5:53:02 AM] [MEMORY] Usage: 69.31 MB/73.35 MB (94.5%)
[5/5/2025, 5:58:02 AM] [MEMORY] Usage: 68.5 MB/73.35 MB (93.4%)
[5/5/2025, 6:03:02 AM] [MEMORY] Usage: 68.7 MB/73.1 MB (94.0%)
[5/5/2025, 6:08:02 AM] [MEMORY] Usage: 68.85 MB/73.1 MB (94.2%)
[5/5/2025, 6:13:02 AM] [MEMORY] Usage: 68.92 MB/73.35 MB (94.0%)
[5/5/2025, 6:18:02 AM] [MEMORY] Usage: 68.89 MB/73.35 MB (93.9%)
[5/5/2025, 6:23:02 AM] [MEMORY] Usage: 69.14 MB/73.35 MB (94.3%)
[5/5/2025, 6:28:02 AM] [MEMORY] Usage: 68.68 MB/74.35 MB (92.4%)
[5/5/2025, 6:33:02 AM] [MEMORY] Usage: 69 MB/73.35 MB (94.1%)
[5/5/2025, 6:38:02 AM] [MEMORY] Usage: 69.02 MB/73.35 MB (94.1%)
[5/5/2025, 6:43:02 AM] [MEMORY] Usage: 68.99 MB/73.35 MB (94.1%)
[5/5/2025, 6:48:02 AM] [MEMORY] Usage: 68.86 MB/73.35 MB (93.9%)
[5/5/2025, 6:53:02 AM] [MEMORY] Usage: 68.74 MB/74.6 MB (92.1%)
[5/5/2025, 6:58:02 AM] [MEMORY] Usage: 70.05 MB/74.1 MB (94.5%)
[5/5/2025, 7:03:02 AM] [MEMORY] Usage: 70.01 MB/74.35 MB (94.2%)
[5/5/2025, 7:08:02 AM] [MEMORY] Usage: 69.63 MB/74.1 MB (94.0%)
[5/5/2025, 7:13:02 AM] [MEMORY] Usage: 70.29 MB/74.35 MB (94.5%)
[5/5/2025, 7:18:02 AM] [MEMORY] Usage: 69.98 MB/75.35 MB (92.9%)
[5/5/2025, 7:23:02 AM] [MEMORY] Usage: 70.53 MB/74.6 MB (94.5%)
[5/5/2025, 7:28:02 AM] [MEMORY] Usage: 70.62 MB/75.1 MB (94.0%)
[5/5/2025, 7:33:02 AM] [MEMORY] Usage: 70.43 MB/76.1 MB (92.5%)
[5/5/2025, 7:38:02 AM] [MEMORY] Usage: 70.28 MB/74.6 MB (94.2%)
[5/5/2025, 7:43:02 AM] [MEMORY] Usage: 70.11 MB/74.6 MB (94.0%)
[5/5/2025, 7:48:02 AM] [MEMORY] Usage: 70.57 MB/75.1 MB (94.0%)
[5/5/2025, 7:53:02 AM] [MEMORY] Usage: 70.58 MB/74.85 MB (94.3%)
[5/5/2025, 7:58:02 AM] [MEMORY] Usage: 70.69 MB/75.85 MB (93.2%)
[5/5/2025, 8:03:02 AM] [MEMORY] Usage: 70.09 MB/75.1 MB (93.3%)
[5/5/2025, 8:08:02 AM] [MEMORY] Usage: 70.23 MB/74.85 MB (93.8%)
[5/5/2025, 8:13:02 AM] [MEMORY] Usage: 69.94 MB/74.6 MB (93.8%)
[5/5/2025, 8:18:02 AM] [MEMORY] Usage: 69.77 MB/74.35 MB (93.8%)
[5/5/2025, 8:23:02 AM] [MEMORY] Usage: 69.85 MB/74.6 MB (93.6%)
[5/5/2025, 8:28:02 AM] [MEMORY] Usage: 70.27 MB/74.85 MB (93.9%)
[5/5/2025, 8:33:02 AM] [MEMORY] Usage: 70.47 MB/74.85 MB (94.1%)
[5/5/2025, 8:38:02 AM] [MEMORY] Usage: 70.23 MB/75.1 MB (93.5%)
[5/5/2025, 8:43:02 AM] [MEMORY] Usage: 70.22 MB/75.1 MB (93.5%)
[5/5/2025, 8:48:02 AM] [MEMORY] Usage: 70.07 MB/75.35 MB (93.0%)
[5/5/2025, 8:53:02 AM] [MEMORY] Usage: 70.69 MB/75.35 MB (93.8%)
[5/5/2025, 8:58:02 AM] [MEMORY] Usage: 69.9 MB/75.35 MB (92.8%)
[5/5/2025, 9:03:02 AM] [MEMORY] Usage: 70.23 MB/75.6 MB (92.9%)
[5/5/2025, 9:08:02 AM] [MEMORY] Usage: 70.6 MB/75.35 MB (93.7%)
[5/5/2025, 9:13:02 AM] [MEMORY] Usage: 70.38 MB/75.35 MB (93.4%)
[5/5/2025, 9:18:02 AM] [MEMORY] Usage: 70.38 MB/75.6 MB (93.1%)
[5/5/2025, 9:23:02 AM] [MEMORY] Usage: 70.55 MB/75.35 MB (93.6%)
[5/5/2025, 9:28:02 AM] [MEMORY] Usage: 70.38 MB/75.6 MB (93.1%)
[5/5/2025, 9:33:02 AM] [MEMORY] Usage: 70.53 MB/75.6 MB (93.3%)
[5/5/2025, 9:38:02 AM] [MEMORY] Usage: 70.4 MB/76.85 MB (91.6%)
[5/5/2025, 9:43:02 AM] [MEMORY] Usage: 70.73 MB/76.6 MB (92.3%)
[5/5/2025, 9:48:02 AM] [MEMORY] Usage: 70.99 MB/75.6 MB (93.9%)
[5/5/2025, 9:53:02 AM] [MEMORY] Usage: 70.3 MB/75.35 MB (93.3%)
[5/5/2025, 9:58:02 AM] [MEMORY] Usage: 70.38 MB/75.6 MB (93.1%)
[5/5/2025, 10:03:02 AM] [MEMORY] Usage: 71.34 MB/77.6 MB (91.9%)
[5/5/2025, 10:08:02 AM] [MEMORY] Usage: 71.46 MB/76.35 MB (93.6%)
[5/5/2025, 10:13:02 AM] [MEMORY] Usage: 71.41 MB/76.6 MB (93.2%)
[5/5/2025, 10:18:02 AM] [MEMORY] Usage: 71.33 MB/76.35 MB (93.4%)
[5/5/2025, 10:23:02 AM] [MEMORY] Usage: 71.15 MB/76.85 MB (92.6%)
[5/5/2025, 10:28:02 AM] [MEMORY] Usage: 71.47 MB/76.1 MB (93.9%)
[5/5/2025, 10:33:02 AM] [MEMORY] Usage: 71.08 MB/75.85 MB (93.7%)
[5/5/2025, 10:38:02 AM] [MEMORY] Usage: 71.14 MB/76.1 MB (93.5%)
[5/5/2025, 10:43:02 AM] [MEMORY] Usage: 71.2 MB/76.1 MB (93.6%)
[5/5/2025, 10:48:02 AM] [MEMORY] Usage: 71.89 MB/76.35 MB (94.2%)
[5/5/2025, 10:53:02 AM] [MEMORY] Usage: 72.31 MB/76.85 MB (94.1%)
[5/5/2025, 10:58:02 AM] [MEMORY] Usage: 72.33 MB/77.35 MB (93.5%)
[5/5/2025, 11:03:02 AM] [MEMORY] Usage: 71.49 MB/77.6 MB (92.1%)
[5/5/2025, 11:08:02 AM] [MEMORY] Usage: 71.75 MB/76.6 MB (93.7%)
[5/5/2025, 11:13:02 AM] [MEMORY] Usage: 71.42 MB/76.6 MB (93.2%)
[5/5/2025, 11:18:02 AM] [MEMORY] Usage: 71.64 MB/76.85 MB (93.2%)
[5/5/2025, 11:23:02 AM] [MEMORY] Usage: 71.38 MB/76.85 MB (92.9%)
[5/5/2025, 11:28:02 AM] [MEMORY] Usage: 71.32 MB/77.1 MB (92.5%)
[5/5/2025, 11:33:02 AM] [MEMORY] Usage: 71.4 MB/76.85 MB (92.9%)
[5/5/2025, 11:38:02 AM] [MEMORY] Usage: 71.81 MB/76.85 MB (93.4%)
[5/5/2025, 11:43:02 AM] [MEMORY] Usage: 71.91 MB/76.85 MB (93.6%)
[5/5/2025, 11:48:02 AM] [MEMORY] Usage: 71.76 MB/77.1 MB (93.1%)
[5/5/2025, 11:53:02 AM] [MEMORY] Usage: 71.51 MB/77.85 MB (91.9%)
[5/5/2025, 11:58:02 AM] [MEMORY] Usage: 72.14 MB/77.1 MB (93.6%)
[5/5/2025, 12:03:02 PM] [MEMORY] Usage: 71.83 MB/77.85 MB (92.3%)
[5/5/2025, 12:08:02 PM] [MEMORY] Usage: 71.81 MB/76.85 MB (93.4%)
[5/5/2025, 12:13:02 PM] [MEMORY] Usage: 71.71 MB/76.85 MB (93.3%)
[5/5/2025, 12:18:02 PM] [MEMORY] Usage: 71.66 MB/77.35 MB (92.6%)
[5/5/2025, 12:23:02 PM] [MEMORY] Usage: 71.74 MB/78.35 MB (91.6%)
[5/5/2025, 12:28:02 PM] [MEMORY] Usage: 72.43 MB/77.35 MB (93.6%)
[5/5/2025, 12:33:02 PM] [MEMORY] Usage: 72.63 MB/77.6 MB (93.6%)
[5/5/2025, 12:38:02 PM] [MEMORY] Usage: 71.91 MB/77.6 MB (92.7%)
[5/5/2025, 12:43:02 PM] [MEMORY] Usage: 72.06 MB/77.6 MB (92.9%)
[5/5/2025, 12:48:02 PM] [MEMORY] Usage: 72.56 MB/77.6 MB (93.5%)
[5/5/2025, 12:53:02 PM] [MEMORY] Usage: 72.13 MB/78.35 MB (92.1%)
[5/5/2025, 12:58:02 PM] [MEMORY] Usage: 72.61 MB/78.6 MB (92.4%)
[5/5/2025, 1:03:02 PM] [MEMORY] Usage: 72.06 MB/77.6 MB (92.9%)
[5/5/2025, 1:08:02 PM] [MEMORY] Usage: 72.29 MB/77.35 MB (93.5%)
[5/5/2025, 1:13:02 PM] [MEMORY] Usage: 72.07 MB/77.35 MB (93.2%)
[5/5/2025, 1:18:02 PM] [MEMORY] Usage: 71.84 MB/77.1 MB (93.2%)
[5/5/2025, 1:23:02 PM] [MEMORY] Usage: 71.77 MB/77.1 MB (93.1%)
[5/5/2025, 1:28:02 PM] [MEMORY] Usage: 72.26 MB/78.35 MB (92.2%)
[5/5/2025, 1:33:02 PM] [MEMORY] Usage: 71.72 MB/77.1 MB (93.0%)
[5/5/2025, 1:38:02 PM] [MEMORY] Usage: 71.9 MB/76.85 MB (93.6%)
[5/5/2025, 1:43:02 PM] [MEMORY] Usage: 71.14 MB/77.85 MB (91.4%)
[5/5/2025, 1:48:02 PM] [MEMORY] Usage: 71.26 MB/76.35 MB (93.3%)
[5/5/2025, 1:53:02 PM] [MEMORY] Usage: 71.02 MB/77.6 MB (91.5%)
[5/5/2025, 1:58:02 PM] [MEMORY] Usage: 71.58 MB/77.1 MB (92.8%)
[5/5/2025, 2:03:02 PM] [MEMORY] Usage: 71.43 MB/76.6 MB (93.3%)
[5/5/2025, 2:08:02 PM] [MEMORY] Usage: 71.22 MB/76.6 MB (93.0%)
[5/5/2025, 2:13:02 PM] [MEMORY] Usage: 71.74 MB/77.85 MB (92.2%)
[5/5/2025, 2:18:02 PM] [MEMORY] Usage: 71.55 MB/76.35 MB (93.7%)
[5/5/2025, 2:23:02 PM] [MEMORY] Usage: 71.38 MB/77.1 MB (92.6%)
[5/5/2025, 2:28:02 PM] [MEMORY] Usage: 71.59 MB/76.85 MB (93.2%)
[5/5/2025, 2:33:02 PM] [MEMORY] Usage: 71.31 MB/76.85 MB (92.8%)
[5/5/2025, 2:38:02 PM] [MEMORY] Usage: 71.35 MB/76.35 MB (93.5%)
[5/5/2025, 2:43:02 PM] [MEMORY] Usage: 71.23 MB/76.85 MB (92.7%)
[5/5/2025, 2:48:02 PM] [MEMORY] Usage: 72.71 MB/78.35 MB (92.8%)
[5/5/2025, 2:53:02 PM] [MEMORY] Usage: 73.08 MB/78.85 MB (92.7%)
[5/5/2025, 2:58:02 PM] [MEMORY] Usage: 73.62 MB/78.6 MB (93.7%)
[5/5/2025, 3:03:02 PM] [MEMORY] Usage: 74.43 MB/78.85 MB (94.4%)
[5/5/2025, 3:08:02 PM] [MEMORY] Usage: 76.43 MB/80.35 MB (95.1%)
[5/5/2025, 3:13:02 PM] [MEMORY] Usage: 75.01 MB/79.84 MB (93.9%)
[5/5/2025, 3:18:02 PM] [MEMORY] Usage: 77.15 MB/82.09 MB (94.0%)
[5/5/2025, 3:23:02 PM] [MEMORY] Usage: 79.65 MB/83.84 MB (95.0%)
[5/5/2025, 3:28:02 PM] [MEMORY] Usage: 82.31 MB/86.09 MB (95.6%)
[5/5/2025, 3:33:02 PM] [MEMORY] Usage: 85.18 MB/89.09 MB (95.6%)
[5/5/2025, 3:38:02 PM] [MEMORY] Usage: 84.83 MB/90.34 MB (93.9%)
[5/5/2025, 3:43:02 PM] [MEMORY] Usage: 87.13 MB/94.09 MB (92.6%)
[5/5/2025, 3:48:02 PM] [MEMORY] Usage: 92.04 MB/96.84 MB (95.0%)
[5/5/2025, 3:53:02 PM] [MEMORY] Usage: 93.74 MB/104.09 MB (90.1%)
[5/5/2025, 3:58:02 PM] [MEMORY] Usage: 98.36 MB/104.84 MB (93.8%)
[5/5/2025, 4:03:02 PM] [MEMORY] Usage: 104.31 MB/110.09 MB (94.8%)
[5/5/2025, 4:08:02 PM] [MEMORY] Usage: 110.33 MB/120.59 MB (91.5%)
[5/5/2025, 4:13:02 PM] [MEMORY] Usage: 116.96 MB/121.34 MB (96.4%)
[5/5/2025, 4:18:02 PM] [MEMORY] Usage: 126.8 MB/134.34 MB (94.4%)
[5/5/2025, 4:23:02 PM] [MEMORY] Usage: 134.06 MB/141.59 MB (94.7%)
[5/5/2025, 4:28:02 PM] [MEMORY] Usage: 140.3 MB/147.59 MB (95.1%)
[5/5/2025, 4:33:02 PM] [MEMORY] Usage: 146.08 MB/158.84 MB (92.0%)
[5/5/2025, 4:38:02 PM] [MEMORY] Usage: 147.74 MB/168.34 MB (87.8%)
[5/5/2025, 4:43:02 PM] [MEMORY] Usage: 158.67 MB/169.59 MB (93.6%)
[5/5/2025, 4:48:02 PM] [MEMORY] Usage: 168.3 MB/179.09 MB (94.0%)
[5/5/2025, 4:53:02 PM] [MEMORY] Usage: 176.11 MB/184.09 MB (95.7%)
[5/5/2025, 4:58:02 PM] [MEMORY] Usage: 178.76 MB/210.59 MB (84.9%)
[5/5/2025, 5:03:02 PM] [MEMORY] Usage: 189.55 MB/220.09 MB (86.1%)
[5/5/2025, 5:08:02 PM] [MEMORY] Usage: 198.67 MB/218.34 MB (91.0%)
[5/5/2025, 5:13:02 PM] [MEMORY] Usage: 210.26 MB/234.59 MB (89.6%)
[5/5/2025, 5:18:02 PM] [MEMORY] Usage: 218.06 MB/242.84 MB (89.8%)
[5/5/2025, 5:23:02 PM] [MEMORY] Usage: 236.81 MB/243.84 MB (97.1%)
[5/5/2025, 5:28:02 PM] [MEMORY] Usage: 249.28 MB/259.09 MB (96.2%)
[5/5/2025, 5:33:02 PM] [MEMORY] Usage: 249.47 MB/281.59 MB (88.6%)
[5/5/2025, 5:38:02 PM] [MEMORY] Usage: 276.95 MB/289.09 MB (95.8%)
[5/5/2025, 5:43:02 PM] [MEMORY] Usage: 273.88 MB/309.09 MB (88.6%)
[5/5/2025, 5:48:02 PM] [MEMORY] Usage: 295.53 MB/308.84 MB (95.7%)
[5/5/2025, 5:53:02 PM] [MEMORY] Usage: 300.29 MB/310.34 MB (96.8%)
[5/5/2025, 5:58:02 PM] [MEMORY] Usage: 320.74 MB/336.59 MB (95.3%)
[5/5/2025, 6:03:02 PM] [MEMORY] Usage: 330.95 MB/341.34 MB (97.0%)
[5/5/2025, 6:08:02 PM] [MEMORY] Usage: 338.77 MB/348.84 MB (97.1%)
[5/5/2025, 6:13:02 PM] [MEMORY] Usage: 358.53 MB/390.84 MB (91.7%)
[5/5/2025, 6:18:02 PM] [MEMORY] Usage: 365.59 MB/381.34 MB (95.9%)
[5/5/2025, 6:23:02 PM] [MEMORY] Usage: 379.89 MB/389.34 MB (97.6%)
[5/5/2025, 6:28:02 PM] [MEMORY] Usage: 404.52 MB/416.34 MB (97.2%)
[5/5/2025, 6:33:02 PM] [MEMORY] Usage: 413.79 MB/430.09 MB (96.2%)
[5/5/2025, 6:38:02 PM] [MEMORY] Usage: 422.58 MB/454.09 MB (93.1%)
[5/5/2025, 6:43:02 PM] [MEMORY] Usage: 434.93 MB/455.59 MB (95.5%)
[5/5/2025, 6:48:02 PM] [MEMORY] Usage: 450.46 MB/496.84 MB (90.7%)
[5/5/2025, 6:53:02 PM] [MEMORY] Usage: 480.37 MB/499.59 MB (96.2%)
[5/5/2025, 6:58:02 PM] [MEMORY] Usage: 505.28 MB/532.34 MB (94.9%)
[5/5/2025, 7:03:02 PM] [MEMORY] Usage: 517.71 MB/537.34 MB (96.3%)
[5/5/2025, 7:08:02 PM] [MEMORY] Usage: 536.87 MB/559.34 MB (96.0%)
[5/5/2025, 7:13:02 PM] [MEMORY] Usage: 546.22 MB/566.34 MB (96.4%)
[5/5/2025, 7:18:02 PM] [MEMORY] Usage: 567.36 MB/608.09 MB (93.3%)
[5/5/2025, 7:23:02 PM] [MEMORY] Usage: 595.66 MB/629.59 MB (94.6%)
[5/5/2025, 7:28:02 PM] [MEMORY] Usage: 598.76 MB/626.34 MB (95.6%)
[5/5/2025, 7:33:02 PM] [MEMORY] Usage: 626.64 MB/667.34 MB (93.9%)
[5/5/2025, 7:38:02 PM] [MEMORY] Usage: 644.69 MB/681.09 MB (94.7%)
[5/5/2025, 7:43:02 PM] [MEMORY] Usage: 655.37 MB/687.34 MB (95.3%)
[5/5/2025, 7:48:02 PM] [MEMORY] Usage: 672.91 MB/717.34 MB (93.8%)
[5/5/2025, 7:53:02 PM] [MEMORY] Usage: 688.15 MB/730.09 MB (94.3%)
[5/5/2025, 7:58:02 PM] [MEMORY] Usage: 718.72 MB/756.34 MB (95.0%)
[5/5/2025, 8:03:02 PM] [MEMORY] Usage: 743.4 MB/781.59 MB (95.1%)
[5/5/2025, 8:08:02 PM] [MEMORY] Usage: 754.54 MB/798.09 MB (94.5%)
[5/5/2025, 8:13:02 PM] [MEMORY] Usage: 778.55 MB/809.59 MB (96.2%)
[5/5/2025, 8:18:02 PM] [MEMORY] Usage: 808.05 MB/846.84 MB (95.4%)
[5/5/2025, 8:23:02 PM] [MEMORY] Usage: 824.98 MB/863.59 MB (95.5%)
[5/5/2025, 8:28:02 PM] [MEMORY] Usage: 845.42 MB/876.09 MB (96.5%)
[5/5/2025, 8:33:02 PM] [MEMORY] Usage: 877.11 MB/904.59 MB (97.0%)
[5/5/2025, 8:38:02 PM] [MEMORY] Usage: 889.33 MB/915.34 MB (97.2%)
[5/5/2025, 8:43:02 PM] [MEMORY] Usage: 912.25 MB/948.84 MB (96.1%)
[5/5/2025, 8:48:02 PM] [MEMORY] Usage: 933.61 MB/982.59 MB (95.0%)
[5/5/2025, 8:53:02 PM] [MEMORY] Usage: 968.36 MB/1011.84 MB (95.7%)
[5/5/2025, 8:58:02 PM] [MEMORY] Usage: 983.27 MB/1.01 GB (95.2%)
[5/5/2025, 9:03:02 PM] [MEMORY] Usage: 1010.72 MB/1.04 GB (95.3%)
[5/5/2025, 9:08:02 PM] [MEMORY] Usage: 1.02 GB/1.06 GB (95.6%)
[5/5/2025, 9:13:02 PM] [MEMORY] Usage: 1.04 GB/1.09 GB (95.6%)
[5/5/2025, 9:18:02 PM] [MEMORY] Usage: 1.07 GB/1.11 GB (95.7%)
[5/5/2025, 9:23:02 PM] [MEMORY] Usage: 1.1 GB/1.15 GB (95.8%)
[5/5/2025, 9:28:02 PM] [MEMORY] Usage: 1.12 GB/1.16 GB (96.8%)
[5/5/2025, 9:33:02 PM] [MEMORY] Usage: 1.15 GB/1.2 GB (95.7%)
[5/5/2025, 9:38:02 PM] [MEMORY] Usage: 1.17 GB/1.22 GB (96.5%)
[5/5/2025, 9:43:02 PM] [MEMORY] Usage: 1.2 GB/1.26 GB (95.7%)
[5/5/2025, 9:48:02 PM] [MEMORY] Usage: 1.24 GB/1.28 GB (96.3%)
[5/5/2025, 9:53:02 PM] [MEMORY] Usage: 1.26 GB/1.32 GB (95.8%)
[5/5/2025, 9:58:02 PM] [MEMORY] Usage: 1.29 GB/1.33 GB (96.8%)
[5/5/2025, 10:03:02 PM] [MEMORY] Usage: 1.32 GB/1.36 GB (96.7%)
[5/5/2025, 10:08:02 PM] [MEMORY] Usage: 1.35 GB/1.39 GB (96.6%)
[5/5/2025, 10:13:02 PM] [MEMORY] Usage: 1.38 GB/1.42 GB (96.9%)
[5/5/2025, 10:18:02 PM] [MEMORY] Usage: 1.41 GB/1.47 GB (96.1%)
[5/5/2025, 10:23:02 PM] [MEMORY] Usage: 1.43 GB/1.48 GB (96.7%)
[5/5/2025, 10:28:02 PM] [MEMORY] Usage: 1.47 GB/1.52 GB (96.6%)
[5/5/2025, 10:33:02 PM] [MEMORY] Usage: 1.51 GB/1.56 GB (96.7%)
[5/5/2025, 10:38:02 PM] [MEMORY] Usage: 1.54 GB/1.59 GB (96.7%)
[5/5/2025, 10:43:02 PM] [MEMORY] Usage: 1.57 GB/1.63 GB (96.5%)
[5/5/2025, 10:48:02 PM] [MEMORY] Usage: 1.6 GB/1.65 GB (96.5%)
[5/5/2025, 10:53:02 PM] [MEMORY] Usage: 1.63 GB/1.69 GB (96.6%)
[5/5/2025, 10:58:02 PM] [MEMORY] Usage: 1.67 GB/1.73 GB (96.6%)
[5/5/2025, 11:03:02 PM] [MEMORY] Usage: 1.7 GB/1.76 GB (96.6%)
[5/5/2025, 11:08:02 PM] [MEMORY] Usage: 1.73 GB/1.8 GB (96.4%)
[5/5/2025, 11:13:02 PM] [MEMORY] Usage: 1.76 GB/1.83 GB (96.5%)
[5/5/2025, 11:18:03 PM] [MEMORY] Usage: 1.8 GB/1.87 GB (96.5%)
[5/5/2025, 11:23:03 PM] [MEMORY] Usage: 1.83 GB/1.89 GB (96.5%)
[5/5/2025, 11:28:03 PM] [MEMORY] Usage: 1.87 GB/1.93 GB (96.8%)
[5/5/2025, 11:33:03 PM] [MEMORY] Usage: 1.9 GB/1.96 GB (97.0%)
[5/5/2025, 11:38:03 PM] [MEMORY] Usage: 1.94 GB/2 GB (96.6%)
[5/5/2025, 11:43:03 PM] [MEMORY] Usage: 1.98 GB/2.05 GB (96.8%)
[5/5/2025, 11:48:03 PM] [MEMORY] Usage: 2.02 GB/2.08 GB (96.8%)
[5/5/2025, 11:53:03 PM] [MEMORY] Usage: 2.06 GB/2.13 GB (96.9%)
[5/5/2025, 11:58:03 PM] [MEMORY] Usage: 2.09 GB/2.17 GB (96.7%)
[5/6/2025, 12:03:03 AM] [MEMORY] Usage: 2.13 GB/2.2 GB (96.7%)
[5/6/2025, 12:08:03 AM] [MEMORY] Usage: 2.17 GB/2.24 GB (96.9%)
[5/6/2025, 12:13:03 AM] [MEMORY] Usage: 2.2 GB/2.27 GB (97.0%)
[5/6/2025, 12:18:03 AM] [MEMORY] Usage: 2.24 GB/2.32 GB (96.7%)
[5/6/2025, 12:23:03 AM] [MEMORY] Usage: 2.28 GB/2.36 GB (96.9%)
[5/6/2025, 12:28:03 AM] [MEMORY] Usage: 2.32 GB/2.4 GB (96.9%)
[5/6/2025, 12:33:03 AM] [MEMORY] Usage: 2.36 GB/2.43 GB (96.8%)
[5/6/2025, 12:38:03 AM] [MEMORY] Usage: 2.4 GB/2.47 GB (97.0%)
[5/6/2025, 12:43:03 AM] [MEMORY] Usage: 2.44 GB/2.52 GB (97.0%)
[5/6/2025, 12:48:03 AM] [MEMORY] Usage: 2.48 GB/2.56 GB (96.8%)
[5/6/2025, 12:53:03 AM] [MEMORY] Usage: 2.51 GB/2.59 GB (96.8%)
[5/6/2025, 12:58:03 AM] [MEMORY] Usage: 2.55 GB/2.62 GB (97.1%)
[5/6/2025, 1:03:03 AM] [MEMORY] Usage: 2.59 GB/2.67 GB (97.1%)
[5/6/2025, 1:08:03 AM] [MEMORY] Usage: 2.62 GB/2.71 GB (96.9%)
[5/6/2025, 1:13:03 AM] [MEMORY] Usage: 2.66 GB/2.75 GB (96.9%)
[5/6/2025, 1:18:03 AM] [MEMORY] Usage: 2.71 GB/2.79 GB (97.0%)
[5/6/2025, 1:23:03 AM] [MEMORY] Usage: 2.75 GB/2.83 GB (97.1%)
[5/6/2025, 1:28:03 AM] [MEMORY] Usage: 2.79 GB/2.87 GB (97.1%)
[5/6/2025, 1:33:03 AM] [MEMORY] Usage: 2.82 GB/2.91 GB (97.0%)
[5/6/2025, 1:38:03 AM] [MEMORY] Usage: 2.86 GB/2.95 GB (97.0%)
[5/6/2025, 1:43:03 AM] [MEMORY] Usage: 2.91 GB/2.99 GB (97.2%)
[5/6/2025, 1:48:03 AM] [MEMORY] Usage: 2.94 GB/3.03 GB (97.0%)
[5/6/2025, 1:53:03 AM] [MEMORY] Usage: 2.99 GB/3.08 GB (97.0%)
[5/6/2025, 1:58:03 AM] [MEMORY] Usage: 3.04 GB/3.13 GB (97.1%)
[5/6/2025, 2:03:03 AM] [MEMORY] Usage: 3.08 GB/3.17 GB (97.2%)
[5/6/2025, 2:08:03 AM] [MEMORY] Usage: 3.13 GB/3.22 GB (97.2%)
[5/6/2025, 2:13:03 AM] [MEMORY] Usage: 3.17 GB/3.26 GB (97.2%)
[5/6/2025, 2:18:03 AM] [MEMORY] Usage: 3.21 GB/3.31 GB (97.1%)
[5/6/2025, 2:23:03 AM] [MEMORY] Usage: 3.26 GB/3.36 GB (97.1%)
[5/6/2025, 2:28:03 AM] [MEMORY] Usage: 3.32 GB/3.41 GB (97.1%)
[5/6/2025, 2:33:03 AM] [MEMORY] Usage: 3.37 GB/3.47 GB (97.2%)
[5/6/2025, 2:38:03 AM] [MEMORY] Usage: 3.42 GB/3.51 GB (97.3%)
[5/6/2025, 2:43:03 AM] [MEMORY] Usage: 3.46 GB/3.56 GB (97.3%)
[5/6/2025, 2:48:03 AM] [MEMORY] Usage: 3.51 GB/3.62 GB (97.2%)
[5/6/2025, 2:53:03 AM] [MEMORY] Usage: 3.57 GB/3.67 GB (97.2%)
[5/6/2025, 2:58:03 AM] [MEMORY] Usage: 3.62 GB/3.72 GB (97.4%)
[5/6/2025, 3:03:03 AM] [MEMORY] Usage: 3.68 GB/3.78 GB (97.4%)
[5/6/2025, 3:08:03 AM] [MEMORY] Usage: 3.72 GB/3.83 GB (97.2%)
[5/6/2025, 3:13:03 AM] [MEMORY] Usage: 3.78 GB/3.89 GB (97.4%)
[5/6/2025, 3:18:03 AM] [MEMORY] Usage: 3.82 GB/3.93 GB (97.3%)
[5/6/2025, 3:23:03 AM] [MEMORY] Usage: 3.88 GB/3.98 GB (97.4%)
[5/6/2025, 3:28:03 AM] [MEMORY] Usage: 3.93 GB/4.04 GB (97.4%)
[5/6/2025, 3:33:03 AM] [MEMORY] Usage: 3.99 GB/4.09 GB (97.4%)
[5/6/2025, 3:38:03 AM] [MEMORY] Usage: 4.04 GB/4.15 GB (97.4%)
[5/6/2025, 3:43:03 AM] [MEMORY] Usage: 4.08 GB/4.2 GB (97.3%)
[5/6/2025, 3:48:03 AM] [MEMORY] Usage: 4.15 GB/4.26 GB (97.5%)
[5/6/2025, 3:53:03 AM] [MEMORY] Usage: 4.2 GB/4.31 GB (97.3%)
[5/6/2025, 3:58:03 AM] [MEMORY] Usage: 4.25 GB/4.36 GB (97.5%)
[5/6/2025, 4:03:03 AM] [MEMORY] Usage: 4.32 GB/4.43 GB (97.4%)
[5/6/2025, 4:08:03 AM] [MEMORY] Usage: 4.36 GB/4.47 GB (97.5%)
[5/6/2025, 4:13:03 AM] [MEMORY] Usage: 4.41 GB/4.53 GB (97.3%)
[5/6/2025, 4:18:03 AM] [MEMORY] Usage: 4.47 GB/4.59 GB (97.3%)
[5/6/2025, 4:23:03 AM] [MEMORY] Usage: 4.52 GB/4.65 GB (97.4%)
[5/6/2025, 4:28:03 AM] [MEMORY] Usage: 4.58 GB/4.71 GB (97.4%)
[5/6/2025, 4:33:03 AM] [MEMORY] Usage: 4.64 GB/4.76 GB (97.5%)
[5/6/2025, 4:38:03 AM] [MEMORY] Usage: 4.7 GB/4.83 GB (97.4%)
[5/6/2025, 4:43:03 AM] [MEMORY] Usage: 4.77 GB/4.89 GB (97.5%)
[5/6/2025, 4:48:03 AM] [MEMORY] Usage: 4.82 GB/4.95 GB (97.5%)
[5/6/2025, 4:53:03 AM] [MEMORY] Usage: 4.88 GB/5.01 GB (97.4%)
[5/6/2025, 4:58:03 AM] [MEMORY] Usage: 4.93 GB/5.06 GB (97.4%)
[5/6/2025, 5:03:03 AM] [MEMORY] Usage: 5 GB/5.13 GB (97.5%)
[5/6/2025, 5:08:03 AM] [MEMORY] Usage: 5.06 GB/5.19 GB (97.4%)
[5/6/2025, 5:13:03 AM] [MEMORY] Usage: 5.12 GB/5.25 GB (97.5%)
[5/6/2025, 5:18:03 AM] [MEMORY] Usage: 5.16 GB/5.3 GB (97.4%)
[5/6/2025, 5:23:03 AM] [MEMORY] Usage: 5.23 GB/5.37 GB (97.4%)
[5/6/2025, 5:28:03 AM] [MEMORY] Usage: 5.29 GB/5.43 GB (97.5%)
[5/6/2025, 5:33:03 AM] [MEMORY] Usage: 5.34 GB/5.48 GB (97.4%)
[5/6/2025, 5:38:03 AM] [MEMORY] Usage: 5.4 GB/5.54 GB (97.4%)
[5/6/2025, 5:43:03 AM] [MEMORY] Usage: 5.46 GB/5.6 GB (97.6%)
[5/6/2025, 5:48:03 AM] [MEMORY] Usage: 5.52 GB/5.66 GB (97.6%)
[5/6/2025, 5:53:03 AM] [MEMORY] Usage: 5.56 GB/5.71 GB (97.5%)
[5/6/2025, 5:58:03 AM] [MEMORY] Usage: 5.61 GB/5.76 GB (97.4%)
[5/6/2025, 6:03:03 AM] [MEMORY] Usage: 5.66 GB/5.81 GB (97.5%)
[5/6/2025, 6:08:03 AM] [MEMORY] Usage: 5.72 GB/5.87 GB (97.5%)
[5/6/2025, 6:13:03 AM] [MEMORY] Usage: 5.78 GB/5.93 GB (97.5%)
[5/6/2025, 6:18:03 AM] [MEMORY] Usage: 5.84 GB/5.99 GB (97.5%)
[5/6/2025, 6:23:03 AM] [MEMORY] Usage: 5.89 GB/6.05 GB (97.5%)
[5/6/2025, 6:28:03 AM] [MEMORY] Usage: 5.95 GB/6.1 GB (97.5%)
[5/6/2025, 6:33:03 AM] [MEMORY] Usage: 6.01 GB/6.16 GB (97.5%)
[5/6/2025, 6:38:03 AM] [MEMORY] Usage: 6.07 GB/6.23 GB (97.5%)
[5/6/2025, 6:43:03 AM] [MEMORY] Usage: 6.14 GB/6.29 GB (97.5%)
[5/6/2025, 6:48:03 AM] [MEMORY] Usage: 6.19 GB/6.34 GB (97.6%)
[5/6/2025, 6:53:03 AM] [MEMORY] Usage: 6.24 GB/6.4 GB (97.5%)
[5/6/2025, 6:58:03 AM] [MEMORY] Usage: 6.31 GB/6.47 GB (97.5%)
[5/6/2025, 7:03:03 AM] [MEMORY] Usage: 6.37 GB/6.53 GB (97.5%)
[5/6/2025, 7:08:03 AM] [MEMORY] Usage: 6.44 GB/6.6 GB (97.6%)
[5/6/2025, 7:13:03 AM] [MEMORY] Usage: 6.49 GB/6.66 GB (97.5%)
[5/6/2025, 7:18:03 AM] [MEMORY] Usage: 6.56 GB/6.72 GB (97.7%)
[5/6/2025, 7:23:03 AM] [MEMORY] Usage: 6.62 GB/6.79 GB (97.5%)
[5/6/2025, 7:28:03 AM] [MEMORY] Usage: 6.68 GB/6.85 GB (97.5%)
[5/6/2025, 7:33:03 AM] [MEMORY] Usage: 6.73 GB/6.9 GB (97.6%)
[5/6/2025, 7:38:03 AM] [MEMORY] Usage: 6.79 GB/6.96 GB (97.6%)
[5/6/2025, 7:43:03 AM] [MEMORY] Usage: 6.85 GB/7.02 GB (97.6%)
[5/6/2025, 7:48:03 AM] [MEMORY] Usage: 6.93 GB/7.1 GB (97.7%)
[5/6/2025, 7:53:03 AM] [MEMORY] Usage: 6.99 GB/7.15 GB (97.7%)
[5/6/2025, 7:58:03 AM] [MEMORY] Usage: 7.05 GB/7.22 GB (97.6%)
[5/6/2025, 8:03:03 AM] [MEMORY] Usage: 7.11 GB/7.28 GB (97.6%)
[5/6/2025, 8:08:03 AM] [MEMORY] Usage: 7.17 GB/7.35 GB (97.6%)
[5/6/2025, 8:13:03 AM] [MEMORY] Usage: 7.26 GB/7.43 GB (97.7%)
[5/6/2025, 8:18:03 AM] [MEMORY] Usage: 7.29 GB/7.47 GB (97.6%)
[5/6/2025, 8:23:03 AM] [MEMORY] Usage: 7.37 GB/7.54 GB (97.7%)
[5/6/2025, 8:28:03 AM] [MEMORY] Usage: 7.44 GB/7.62 GB (97.7%)
[5/6/2025, 8:33:03 AM] [MEMORY] Usage: 7.5 GB/7.69 GB (97.6%)
[5/6/2025, 8:38:03 AM] [MEMORY] Usage: 7.57 GB/7.76 GB (97.6%)
[5/6/2025, 8:43:03 AM] [MEMORY] Usage: 7.65 GB/7.83 GB (97.7%)
[5/6/2025, 8:48:03 AM] [MEMORY] Usage: 7.7 GB/7.89 GB (97.6%)
[5/6/2025, 8:53:03 AM] [MEMORY] Usage: 7.76 GB/7.95 GB (97.6%)
[5/6/2025, 8:58:03 AM] [MEMORY] Usage: 7.83 GB/8.02 GB (97.6%)
[5/6/2025, 9:03:03 AM] [MEMORY] Usage: 7.9 GB/8.09 GB (97.6%)
[5/6/2025, 9:08:03 AM] [MEMORY] Usage: 7.97 GB/8.16 GB (97.6%)
[5/6/2025, 9:13:03 AM] [MEMORY] Usage: 8.04 GB/8.24 GB (97.5%)
[5/6/2025, 9:18:03 AM] [MEMORY] Usage: 8.1 GB/8.29 GB (97.7%)
[5/6/2025, 9:23:03 AM] [MEMORY] Usage: 8.18 GB/8.37 GB (97.7%)
[5/6/2025, 9:28:03 AM] [MEMORY] Usage: 8.25 GB/8.44 GB (97.7%)
[5/6/2025, 9:33:03 AM] [MEMORY] Usage: 8.31 GB/8.51 GB (97.7%)
[5/6/2025, 9:38:03 AM] [MEMORY] Usage: 8.37 GB/8.56 GB (97.7%)
[5/6/2025, 9:43:03 AM] [MEMORY] Usage: 8.43 GB/8.63 GB (97.7%)
[5/6/2025, 9:48:03 AM] [MEMORY] Usage: 8.5 GB/8.7 GB (97.7%)
[5/6/2025, 9:53:03 AM] [MEMORY] Usage: 8.56 GB/8.76 GB (97.7%)
[5/6/2025, 9:58:03 AM] [MEMORY] Usage: 8.64 GB/8.84 GB (97.7%)
[5/6/2025, 10:03:03 AM] [MEMORY] Usage: 8.69 GB/8.89 GB (97.7%)
[5/6/2025, 10:08:03 AM] [MEMORY] Usage: 8.75 GB/8.96 GB (97.7%)
[5/6/2025, 10:13:03 AM] [MEMORY] Usage: 8.82 GB/9.03 GB (97.7%)
[5/6/2025, 10:18:03 AM] [MEMORY] Usage: 8.88 GB/9.09 GB (97.7%)
[5/6/2025, 10:23:03 AM] [MEMORY] Usage: 8.97 GB/9.18 GB (97.7%)
[5/6/2025, 10:28:03 AM] [MEMORY] Usage: 9.01 GB/9.22 GB (97.7%)
[5/6/2025, 10:33:03 AM] [MEMORY] Usage: 9.07 GB/9.28 GB (97.7%)
[5/6/2025, 10:38:03 AM] [MEMORY] Usage: 9.13 GB/9.34 GB (97.7%)
[5/6/2025, 10:43:03 AM] [MEMORY] Usage: 9.19 GB/9.4 GB (97.7%)
[5/6/2025, 10:48:03 AM] [MEMORY] Usage: 9.25 GB/9.47 GB (97.7%)
[5/6/2025, 10:53:03 AM] [MEMORY] Usage: 9.31 GB/9.53 GB (97.7%)
[5/6/2025, 10:58:03 AM] [MEMORY] Usage: 9.39 GB/9.6 GB (97.7%)
[5/6/2025, 11:03:04 AM] [MEMORY] Usage: 9.44 GB/9.66 GB (97.7%)
[5/6/2025, 11:08:04 AM] [MEMORY] Usage: 9.52 GB/9.74 GB (97.7%)
[5/6/2025, 11:13:04 AM] [MEMORY] Usage: 9.58 GB/9.8 GB (97.7%)
[5/6/2025, 11:18:04 AM] [MEMORY] Usage: 9.65 GB/9.87 GB (97.7%)
[5/6/2025, 11:23:04 AM] [MEMORY] Usage: 9.71 GB/9.93 GB (97.8%)
[5/6/2025, 11:28:04 AM] [MEMORY] Usage: 9.77 GB/9.97 GB (97.9%)
[5/6/2025, 11:33:04 AM] [MEMORY] Usage: 9.83 GB/10.06 GB (97.8%)
[5/6/2025, 11:38:04 AM] [MEMORY] Usage: 9.9 GB/10.13 GB (97.8%)
[5/6/2025, 11:43:04 AM] [MEMORY] Usage: 9.98 GB/10.21 GB (97.8%)
[5/6/2025, 11:48:04 AM] [MEMORY] Usage: 10.04 GB/10.27 GB (97.8%)
[5/6/2025, 11:53:04 AM] [MEMORY] Usage: 10.1 GB/10.33 GB (97.8%)
[5/6/2025, 11:58:04 AM] [MEMORY] Usage: 10.17 GB/10.4 GB (97.8%)
[5/6/2025, 12:03:04 PM] [MEMORY] Usage: 10.23 GB/10.46 GB (97.8%)
[5/6/2025, 12:08:04 PM] [MEMORY] Usage: 10.3 GB/10.53 GB (97.8%)
[5/6/2025, 12:13:04 PM] [MEMORY] Usage: 10.37 GB/10.61 GB (97.8%)
[5/6/2025, 12:18:04 PM] [MEMORY] Usage: 10.43 GB/10.67 GB (97.8%)
[5/6/2025, 12:23:04 PM] [MEMORY] Usage: 10.5 GB/10.74 GB (97.8%)
[5/6/2025, 12:28:04 PM] [MEMORY] Usage: 10.57 GB/10.81 GB (97.8%)
[5/6/2025, 12:33:04 PM] [MEMORY] Usage: 10.63 GB/10.87 GB (97.8%)
[5/6/2025, 12:38:04 PM] [MEMORY] Usage: 10.7 GB/10.94 GB (97.8%)
[5/6/2025, 12:43:04 PM] [MEMORY] Usage: 10.77 GB/11.02 GB (97.8%)
[5/6/2025, 12:48:04 PM] [MEMORY] Usage: 10.84 GB/11.09 GB (97.8%)
[5/6/2025, 12:53:04 PM] [MEMORY] Usage: 10.91 GB/11.16 GB (97.8%)
[5/6/2025, 12:58:04 PM] [MEMORY] Usage: 10.99 GB/11.25 GB (97.8%)
[5/6/2025, 1:03:04 PM] [MEMORY] Usage: 11.09 GB/11.34 GB (97.8%)
[5/6/2025, 1:08:04 PM] [MEMORY] Usage: 11.13 GB/11.39 GB (97.8%)
[5/6/2025, 1:13:04 PM] [MEMORY] Usage: 11.18 GB/11.44 GB (97.8%)
[5/6/2025, 1:18:04 PM] [MEMORY] Usage: 11.26 GB/11.51 GB (97.8%)
[5/6/2025, 1:23:05 PM] [MEMORY] Usage: 11.32 GB/11.58 GB (97.8%)
[5/6/2025, 1:28:05 PM] [MEMORY] Usage: 11.39 GB/11.65 GB (97.8%)
[5/6/2025, 1:33:05 PM] [MEMORY] Usage: 11.46 GB/11.72 GB (97.8%)
[5/6/2025, 1:38:05 PM] [MEMORY] Usage: 11.53 GB/11.79 GB (97.8%)
[5/6/2025, 1:43:05 PM] [MEMORY] Usage: 11.6 GB/11.86 GB (97.8%)
[5/6/2025, 1:48:05 PM] [MEMORY] Usage: 11.67 GB/11.94 GB (97.7%)
[5/6/2025, 1:53:05 PM] [MEMORY] Usage: 11.74 GB/12.01 GB (97.8%)
[5/6/2025, 1:58:05 PM] [MEMORY] Usage: 11.8 GB/12.08 GB (97.8%)
[5/6/2025, 2:03:05 PM] [MEMORY] Usage: 11.87 GB/12.14 GB (97.8%)
[5/6/2025, 2:08:05 PM] [MEMORY] Usage: 11.93 GB/12.21 GB (97.7%)
[5/6/2025, 2:13:05 PM] [MEMORY] Usage: 12 GB/12.28 GB (97.8%)
[5/6/2025, 2:18:05 PM] [MEMORY] Usage: 12.05 GB/12.33 GB (97.7%)
[5/6/2025, 2:23:06 PM] [MEMORY] Usage: 12.12 GB/12.4 GB (97.7%)
[5/6/2025, 2:28:06 PM] [MEMORY] Usage: 12.18 GB/12.46 GB (97.7%)
[5/6/2025, 2:33:07 PM] [MEMORY] Usage: 12.24 GB/12.52 GB (97.8%)
[5/6/2025, 2:38:07 PM] [MEMORY] Usage: 12.31 GB/12.59 GB (97.7%)
[5/6/2025, 2:43:07 PM] [MEMORY] Usage: 12.38 GB/12.66 GB (97.7%)
[5/6/2025, 2:48:08 PM] [MEMORY] Usage: 12.44 GB/12.73 GB (97.7%)
[5/6/2025, 2:53:08 PM] [MEMORY] Usage: 12.5 GB/12.79 GB (97.7%)
[5/6/2025, 2:58:08 PM] [MEMORY] Usage: 12.57 GB/12.86 GB (97.7%)
[5/6/2025, 3:03:08 PM] [MEMORY] Usage: 12.63 GB/12.92 GB (97.7%)
[5/6/2025, 3:08:08 PM] [MEMORY] Usage: 12.69 GB/12.99 GB (97.7%)
[5/6/2025, 3:13:08 PM] [MEMORY] Usage: 12.76 GB/13.05 GB (97.7%)
[5/6/2025, 3:18:08 PM] [MEMORY] Usage: 12.82 GB/13.12 GB (97.7%)
[5/6/2025, 3:23:08 PM] [MEMORY] Usage: 12.89 GB/13.19 GB (97.7%)
[5/6/2025, 3:28:08 PM] [MEMORY] Usage: 12.96 GB/13.26 GB (97.7%)
[5/6/2025, 3:33:08 PM] [MEMORY] Usage: 13.02 GB/13.33 GB (97.7%)
[5/6/2025, 3:38:08 PM] [MEMORY] Usage: 13.09 GB/13.4 GB (97.7%)
[5/6/2025, 3:43:08 PM] [MEMORY] Usage: 13.16 GB/13.46 GB (97.7%)
[5/6/2025, 3:48:08 PM] [MEMORY] Usage: 13.23 GB/13.55 GB (97.6%)
[5/6/2025, 3:53:08 PM] [MEMORY] Usage: 13.29 GB/13.63 GB (97.5%)
[5/6/2025, 3:58:08 PM] [MEMORY] Usage: 13.36 GB/13.69 GB (97.6%)
[5/6/2025, 4:03:08 PM] [MEMORY] Usage: 13.43 GB/13.8 GB (97.3%)
[5/6/2025, 4:08:08 PM] [MEMORY] Usage: 13.5 GB/13.88 GB (97.3%)
[5/6/2025, 4:13:08 PM] [MEMORY] Usage: 13.56 GB/13.92 GB (97.4%)
[5/6/2025, 4:18:08 PM] [MEMORY] Usage: 13.62 GB/14.02 GB (97.1%)
[5/6/2025, 4:23:08 PM] [MEMORY] Usage: 13.69 GB/14.11 GB (97.0%)
[5/6/2025, 4:28:11 PM] [MEMORY] Usage: 13.76 GB/14.21 GB (96.8%)
[5/6/2025, 4:33:13 PM] [MEMORY] Usage: 13.82 GB/14.26 GB (96.9%)
[5/6/2025, 4:38:18 PM] [MEMORY] Usage: 13.89 GB/14.34 GB (96.8%)
[5/6/2025, 4:43:21 PM] [MEMORY] Usage: 13.96 GB/14.43 GB (96.7%)
[5/6/2025, 4:48:25 PM] [MEMORY] Usage: 14.03 GB/14.48 GB (96.9%)
[5/6/2025, 4:53:28 PM] [MEMORY] Usage: 14.09 GB/14.55 GB (96.8%)
[5/6/2025, 4:58:32 PM] [MEMORY] Usage: 14.16 GB/14.63 GB (96.8%)
[5/6/2025, 5:03:35 PM] [MEMORY] Usage: 14.23 GB/14.7 GB (96.8%)
[5/6/2025, 5:08:40 PM] [MEMORY] Usage: 14.3 GB/14.8 GB (96.6%)
[5/6/2025, 5:13:45 PM] [MEMORY] Usage: 14.37 GB/14.86 GB (96.7%)
[5/6/2025, 5:18:49 PM] [MEMORY] Usage: 14.43 GB/14.93 GB (96.6%)
[5/6/2025, 5:23:54 PM] [MEMORY] Usage: 14.51 GB/15.05 GB (96.4%)
[5/6/2025, 5:28:59 PM] [MEMORY] Usage: 14.58 GB/15.13 GB (96.4%)
[5/6/2025, 5:34:04 PM] [MEMORY] Usage: 14.65 GB/15.17 GB (96.6%)
[5/6/2025, 5:39:08 PM] [MEMORY] Usage: 14.72 GB/15.26 GB (96.4%)
[5/6/2025, 5:44:13 PM] [MEMORY] Usage: 14.79 GB/15.33 GB (96.5%)
[5/6/2025, 5:49:13 PM] [MEMORY] Usage: 15.04 GB/15.41 GB (97.6%)
[5/6/2025, 5:54:13 PM] [MEMORY] Usage: 15.09 GB/15.46 GB (97.7%)
[5/6/2025, 5:59:13 PM] [MEMORY] Usage: 15.17 GB/15.54 GB (97.6%)
[5/6/2025, 6:04:13 PM] [MEMORY] Usage: 15.24 GB/15.61 GB (97.6%)
[5/6/2025, 6:09:13 PM] [MEMORY] Usage: 15.32 GB/15.7 GB (97.6%)
[5/6/2025, 6:14:13 PM] [MEMORY] Usage: 15.36 GB/15.73 GB (97.6%)
[5/6/2025, 6:19:13 PM] [MEMORY] Usage: 15.44 GB/15.82 GB (97.6%)
[5/6/2025, 6:24:13 PM] [MEMORY] Usage: 15.51 GB/15.89 GB (97.6%)
[5/6/2025, 6:29:14 PM] [MEMORY] Usage: 15.61 GB/16 GB (97.6%)
[5/6/2025, 6:34:14 PM] [MEMORY] Usage: 15.53 GB/16.01 GB (97.0%)
[5/6/2025, 6:39:14 PM] [MEMORY] Usage: 15.64 GB/16.03 GB (97.6%)