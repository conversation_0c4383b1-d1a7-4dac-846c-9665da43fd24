const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MAX_RESTARTS = 100; // Maximum number of restarts in the restart period
const RESTART_PERIOD = 60 * 60 * 1000; // 1 hour in milliseconds
const RESTART_DELAY = 5000; // 5 seconds delay between restarts
const LOG_FILE = path.join(__dirname, 'bot-monitor.log');

// Tracking variables
let restarts = 0;
let restartTimer = null;
let botProcess = null;
let shuttingDown = false;

/**
 * Log a message to console and file
 * @param {string} message - Message to log
 */
function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    
    console.log(logMessage);
    
    // Also log to file
    fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

/**
 * Start the bot process
 */
function startBot() {
    // Check if we've exceeded the maximum number of restarts
    if (restarts >= MAX_RESTARTS) {
        log(`ERROR: Exceeded maximum number of restarts (${MAX_RESTARTS}). Waiting for restart period to reset.`);
        
        // Wait for the restart period to reset before trying again
        setTimeout(() => {
            restarts = 0;
            startBot();
        }, RESTART_PERIOD);
        
        return;
    }
    
    log(`Starting bot (restart #${restarts + 1})...`);
    
    // Increase memory limit to 4GB
    botProcess = spawn('node', ['--max-old-space-size=4096', '.'], {
        stdio: 'inherit',
        shell: true
    });
    
    // Increment restart counter
    restarts++;
    
    // Reset restart counter after the restart period
    if (!restartTimer) {
        restartTimer = setTimeout(() => {
            log(`Resetting restart counter (was at ${restarts})`);
            restarts = 0;
            restartTimer = null;
        }, RESTART_PERIOD);
    }
    
    // Handle process exit
    botProcess.on('exit', (code, signal) => {
        if (shuttingDown) return;
        
        if (code === null) {
            log(`Bot process was killed by signal: ${signal}`);
        } else if (code === 0) {
            log('Bot process exited cleanly');
            process.exit(0); // Exit the monitor as well if the bot exited cleanly
        } else {
            log(`Bot process crashed with code: ${code}`);
            
            // If the exit code indicates an out of memory error
            if (code === 137 || code === 134) {
                log('Detected out of memory error. Restarting with increased memory...');
            }
            
            // Restart the bot after a delay
            log(`Restarting bot in ${RESTART_DELAY / 1000} seconds...`);
            setTimeout(startBot, RESTART_DELAY);
        }
    });
    
    // Handle process errors
    botProcess.on('error', (err) => {
        log(`Failed to start bot process: ${err.message}`);
        
        // Restart the bot after a delay
        log(`Restarting bot in ${RESTART_DELAY / 1000} seconds...`);
        setTimeout(startBot, RESTART_DELAY);
    });
}

/**
 * Handle graceful shutdown
 */
function shutdown() {
    shuttingDown = true;
    log('Shutting down bot monitor...');
    
    if (restartTimer) {
        clearTimeout(restartTimer);
        restartTimer = null;
    }
    
    if (botProcess) {
        log('Terminating bot process...');
        botProcess.kill('SIGTERM');
    }
    
    process.exit(0);
}

// Handle signals for graceful shutdown
process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);

// Handle uncaught exceptions in the monitor
process.on('uncaughtException', (err) => {
    log(`Uncaught exception in bot monitor: ${err.message}`);
    log(err.stack);
    
    // Continue running the monitor
    if (botProcess && !shuttingDown) {
        log('Bot monitor will continue running despite the error');
    }
});

// Start the bot
log('Bot monitor started');
startBot();
