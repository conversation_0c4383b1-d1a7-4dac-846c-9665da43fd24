{"name": "manager", "version": "1.5.0", "description": "Manager", "main": "index.js", "engines": {"node": ">=18.19.0"}, "scripts": {"start": "node --max-old-space-size=16384 --expose-gc --inspect index.js", "dev": "nodemon --max-old-space-size=16384 --expose-gc --inspect index.js"}, "author": "Skelee", "license": "ISC", "dependencies": {"@discordjs/collection": "^1.5.3", "@discordjs/rest": "^1.7.1", "@fastify/cors": "^9.0.1", "@fastify/formbody": "^7.4.0", "@fastify/multipart": "^6.0.0", "@fastify/rate-limit": "^9.1.0", "@fastify/session": "^10.9.0", "@fastify/static": "^7.0.4", "ansi-colors": "^4.1.3", "apexcharts": "^3.48.0", "axios": "^1.6.2", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cheerio": "^1.0.0", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "crypto": "^1.0.1", "crypto-convert": "^2.1.6", "discord-html-transcripts": "^3.2.0", "discord-logs": "^2.2.1", "discord.js": "^14.19.3", "ejs": "^3.1.9", "emoji-regex": "^10.3.0", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-session": "^1.17.3", "express-subdomain": "^1.0.6", "express-subdomain-handler": "^0.1.0", "fastify": "^4.28.1", "fastify-cookie": "^5.7.0", "fastify-cors": "^6.1.0", "fastify-file-upload": "^4.0.0", "fastify-formbody": "^5.3.0", "fastify-multipart": "^5.4.0", "fastify-passport": "^0.6.0", "fastify-rate-limit": "^5.9.0", "fastify-static": "^4.7.0", "flowbite": "^2.3.0", "fs-extra": "^11.2.0", "glob": "^7.2.3", "humanize-duration": "^3.31.0", "image-size": "^1.1.1", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "moment-timezone": "^0.5.43", "mongoose": "^8.0.3", "ms": "^2.1.3", "node-cron": "^3.0.3", "node-fetch": "^2.6.7", "nodemon": "^3.1.10", "notyf": "^3.10.0", "openai": "^4.28.0", "passport": "^0.7.0", "passport-discord": "^0.1.4", "paypal-rest-sdk": "^1.6.4", "postcss-cli": "^11.0.0", "pump": "^3.0.0", "qrcode": "^1.5.3", "rcon": "^1.1.0", "rustrcon": "^1.0.2", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "stream": "^0.0.3", "stripe": "^10.17.0", "systeminformation": "^5.23.4", "url": "^0.11.3", "util": "^0.12.5", "uuid": "^9.0.1", "vhost": "^3.0.2", "webrcon": "^0.2.3", "ws": "^8.16.0"}, "devDependencies": {"autoprefixer": "^10.4.18", "postcss": "^8.4.36", "tailwindcss": "^3.4.1"}}