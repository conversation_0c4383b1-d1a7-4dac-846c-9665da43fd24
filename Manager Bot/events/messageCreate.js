const { EmbedBuilder, WebhookClient } = require("discord.js");
const config = require("../utils/configAdapter");
const ticketModel = require("../models/ticketModel.js");
const guildModel = require("../models/guildModel.js");
const Suggestion = require("../models/suggestion.js");
const { updateDailyStats, updateYearlyStats } = require('./dailyStatsUpdater.js');
const ticketTimers = new Map();

module.exports = async (client, message) => {
    if (message.author.bot) return;

    const prefix = config.prefix || "!"; // ✅ Load prefix from config

    // ✅ Handle Prefix Commands
    if (message.content.startsWith(prefix)) {
        const args = message.content.slice(prefix.length).trim().split(/ +/);
        const commandName = args.shift().toLowerCase();

        const command = client.prefixCommands.get(commandName);
        if (!command) return; // Ignore if command doesn't exist

        try {
            await command.execute(message, args);
        } catch (error) {
            console.error(`❌ Error executing prefix command: ${error.message}`);
            message.reply("❌ An error occurred while executing the command.");
        }
        return;
    }

    // ✅ Ticket System Handling (DMs)
    if (!message.guild) {
        try {
            const activeTicket = await ticketModel.findOne({ userID: message.author.id, status: "Open" });
            if (!activeTicket) return message.reply("❌ You don't have an open ticket. Please create one first.");

            const ticketChannel = await client.channels.fetch(activeTicket.channelID).catch(() => null);
            if (!ticketChannel) return message.reply("❌ Ticket channel not found. Please contact staff.");

            const embed = new EmbedBuilder()
                .setColor('#0000FF') // Using hex color code instead of string 'Blue'
                .setAuthor({ name: `${message.author.username} (User)`, iconURL: message.author.displayAvatarURL({ dynamic: true }) })
                .setDescription(`\`\`\`${message.content}\`\`\`` || "*No message content*")
                .setTimestamp();

            if (message.attachments.size > 0) {
                embed.setImage(message.attachments.first().url);
            }

            const responseEmbed = new EmbedBuilder()
                .setColor('#00A300') // Green color
                .setDescription(`Ticket response has been sent to staff.`)
                .setFooter({ text: `Ticket ID: ${activeTicket.identifier}`, iconURL: `https://cdn.discordapp.com/attachments/1329591447468507247/1329979874739224728/CARZWaY.png?ex=68156bc7&is=68141a47&hm=8bc5fe2fd9ceaf6be3aabef5da5462beae464d8ee3101e81f972a3c1ff6f591c&` })
                .setTimestamp();

            if (activeTicket.claimed && activeTicket.claimUser) {
                const claimUserEmbed = new EmbedBuilder()
                    .setColor('#0000FF') // Using hex color code instead of string 'Blue'
                    .setAuthor({ name: `${message.author.username} (User)`, iconURL: message.author.displayAvatarURL({ dynamic: true }) })
                    .setDescription(`${message.author.username}(${activeTicket.claimUser}) has responded to ticket <#${activeTicket.channelID}>`)
                    .setTimestamp();

                const claimedStaff = await client.users.fetch(activeTicket.claimUser);
                if (claimedStaff) {
                    await claimedStaff.send({ content: `${message.author.username}(${activeTicket.claimUser}) has responded to ticket <#${activeTicket.channelID}>` });
                }
            } else {
                // Send to webhook if no claimed staff member
                if (config.ticketNotificationWebhook) {
                    try {
                        const webhook = config.ticketNotificationWebhook ? new WebhookClient({ url: config.ticketNotificationWebhook }) : null;
                        const notificationEmbed = new EmbedBuilder()
                            .setDescription(`Unclaimed ticket response from <@${message.author.id}>(${message.author.username}) for ticket <#${activeTicket.channelID}>`)
                            .setTimestamp()
                            .setColor('#FFFFFF'); // White color

                        await webhook.send({
                            content: `Unclaimed ticket response from <@${message.author.id}>(${message.author.username}) for ticket <#${activeTicket.channelID}>`
                        });
                    } catch (error) {
                        console.error(`Error sending to ticket webhook: ${error.message}`);
                    }
                }
            }

            await ticketChannel.send({ embeds: [embed] });
            await message.reply({ embeds: [responseEmbed] });
        } catch (error) {
            console.error(`❌ Error processing DM: ${error.message}`);
        }
        return;
    }

    // ✅ Suggestion System Handling
    const suggestionChannels = config.SuggestionChannels && config.SuggestionChannels[message.guild.id] || [];
    if (suggestionChannels.includes(message.channel.id)) {
        try {
            const suggestionText = message.content;

            // ✅ Check if the message contains links or Discord invites
            const forbiddenPatterns = /(https?:\/\/|discord|\.gg\/)/gi;
            if (forbiddenPatterns.test(suggestionText)) {
                await message.delete().catch(console.error);
                console.log("❌ Suggestion blocked due to containing a link or invite.");
                return;
            }

            await message.delete().catch(console.error);

            const embed = new EmbedBuilder()
                .setTitle("New Suggestion")
                .setDescription(`\`\`\`${suggestionText}\`\`\``)
                .setColor(config.EmbedColors || "#CCCCCC") // Provide a fallback color if config.EmbedColors is undefined
                .setFooter({ text: `Suggested by ${message.author.tag}`, iconURL: message.author.displayAvatarURL() })
                .setTimestamp();

            const sentMessage = await message.channel.send({ embeds: [embed] });
            await sentMessage.react("👍");
            await sentMessage.react("👎");

            const newSuggestion = new Suggestion({
                messageID: sentMessage.id,
                userID: message.author.id,
                guildID: message.guild.id,
                channelID: message.channel.id,
                suggestion: suggestionText,
                status: "Open",
                upvotes: 0,
                downvotes: 0
            });

            await newSuggestion.save();
            console.log(`✅ Suggestion saved with ID: ${sentMessage.id}`);
        } catch (error) {
            console.error("❌ Error saving suggestion:", error);
        }
        return;
    }

    // ✅ Ticket System Activity Tracking
    const ticketDB = await ticketModel.findOne({ channelID: message.channel.id });
    if (ticketDB) {
        if (!message.author.bot) {
            await ticketModel.findOneAndUpdate(
                { channelID: message.channel.id },
                { $set: { lastMessageSent: Date.now(), lastMessageSentUser: message.author.id }, $inc: { messages: 1 } },
                { new: true }
            );
        }

        await guildModel.findOneAndUpdate(
            { guildID: message.guild.id },
            { $inc: { totalMessages: 1 } }
        );

        const metricsToUpdate = ['totalMessages'];
        await updateDailyStats(config.GuildID, metricsToUpdate);
        await updateYearlyStats(config.GuildID, metricsToUpdate);
    }

    // ✅ Staff Message Activity Tracking
    if (!message.author.bot) {
        try {
            const StaffManagerIntegration = require('../utils/staffManagerIntegration');
            const staffIntegration = new StaffManagerIntegration(client);

            // Check if user is staff and notify Staff Manager
            const isStaff = await staffIntegration.isStaffMember(message.author.id);
            if (isStaff) {
                const channelType = ticketDB ? 'ticket' : 'general';
                await staffIntegration.notifyMessageActivity(message.author.id, 1, channelType);
            }
        } catch (error) {
            console.error('Error tracking staff message activity:', error);
        }
    }
};
