const { Discord, StringSelectMenuBuilder, EmbedBuilder, ActionRowBuilder, TextInputBuilder, ModalBuilder } = require("discord.js");
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../utils/configAdapter');
const guildModel = require("../models/guildModel");
const ticketModel = require("../models/ticketModel");
const { updateDailyStats, updateYearlyStats } = require('../events/dailyStatsUpdater');
const moment = require('moment-timezone');
const Users = require('../models/Users')
const axios = require("axios");
const cheerio = require("cheerio");
const path = require("path");


const logFilePath = path.join(__dirname, "logs.battlemetrics");

/**
 * @param {string} message - The log message
 */
function logToFile(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;

    fs.appendFile(logFilePath, logMessage, (err) => {
        if (err) console.error("❌ Failed to write log to file:", err);
    });
}

module.exports = async (client, interaction, channel, buttonConfig) => {
    const ticket = await ticketModel.findOne({ channelID: channel.id });
    const statsDB = await guildModel.findOne({ guildID: config.GuildID });
    statsDB.totalTickets++;
    await statsDB.save();

    const metricsToUpdate = ['totalTickets'];
    await updateDailyStats(config.GuildID, metricsToUpdate);
    await updateYearlyStats(config.GuildID, metricsToUpdate);
    const openNow = await ticketModel.countDocuments({ status: 'Open', guildID: config.GuildID });

    if (statsDB.openTickets !== openNow) {
        statsDB.openTickets = openNow;
        await statsDB.save();
    }
    // if (ticket) {
    //     const ticketChannel = client.guilds.cache.get(ticket.guildID).channels.cache.get(ticket.channelID);
    

    //     if(config.WorkingHours && config.WorkingHours.Enabled && config.WorkingHours.AllowTicketsOutsideWorkingHours && config.WorkingHours.SendNoticeInTicket) {
    //         const workingHoursRegex = /^(\d{1,2}:\d{2})-(\d{1,2}:\d{2})$/;
    //         const workingHoursMatch = config.WorkingHours.WorkingHours.match(workingHoursRegex);            
    //         const currentTime = moment().tz(config.WorkingHours.Timezone);
    //         const startDate = currentTime.format('YYYY-MM-DD');
    //         const startTime = moment.tz(startDate + ' ' + workingHoursMatch[1], 'YYYY-MM-DD H:mm', config.WorkingHours.Timezone);
    //         const endTime = moment.tz(startDate + ' ' + workingHoursMatch[2], 'YYYY-MM-DD H:mm', config.WorkingHours.Timezone);
    //         const withinWorkingHours = currentTime.isBetween(startTime, endTime);
    //         if (!withinWorkingHours) {        
    //           const startTimestamp = startTime.unix();
    //           const endTimestamp = endTime.unix();
    //           let workingHoursEmbedLocale = config.Locale.outsideWorkingHoursMsg.replace(/{startTime}/g, `<t:${startTimestamp}:t>`).replace(/{endTime}/g, `<t:${endTimestamp}:t>`);
    //           let workingHoursEmbed = new EmbedBuilder()
    //             .setTitle(config.Locale.outsideWorkingHoursTitle)
    //             .setColor("#CCCCCC")
    //             .setDescription(workingHoursEmbedLocale)
    //             .setTimestamp();
    //         if (ticketChannel) ticketChannel.send({ embeds: [workingHoursEmbed] });
    //         }
    //     }

    // // Check if the ticket has questions configured
    // if (!ticket.questions || ticket.questions.length === 0) return;

    // const buttonConfigValues = config[buttonConfig];
    // const mismatchedButtons = ticket.questions.some((question, index) => {
    //   const questionConfig = buttonConfigValues.Questions.find(configQuestion => configQuestion.customId === question.customId);
    //   return !questionConfig;
    // });
    // const responses = ticket.questions.reduce((acc, question) => {
    //   acc[question.question] = question.response || config.Locale.notAnswered;
    //   return acc;
    // }, {});
    // const mismatchedQuestions = ticket.questions.some(question => !responses.hasOwnProperty(question.question));
    // if (!buttonConfigValues) {
    //     console.log(`Button config not found for key: ${buttonConfig}`);
    //     return;
    // } else if (ticket.questions.length !== buttonConfigValues.Questions.length) {
    //     console.log('Number of questions mismatch.');
    //     return;
    // } else if (mismatchedButtons) {
    //     console.log('Button values in the database do not match the configuration.');
    //     return;
    // } else if (Object.keys(responses).length !== ticket.questions.length) {
    //     console.log('Number of responses mismatch.');
    //     return;
    // } else if (mismatchedQuestions) {
    //     console.log('Questions do not match the configured questions.');
    //     return;
    // }
    
    //     let ticketQuestionLocale = config.Locale.ticketQuestionsTitle.replace(/{category}/g, `${ticket.ticketType}`);
    //     const embed = new EmbedBuilder()
    //       .setTitle(ticketQuestionLocale)
    //       .setColor(config.EmbedColors);
    
    //     ticket.questions.forEach(question => {
    //       let response = responses[question.question];
    //       if (!response) response = config.Locale.notAnswered;
    //       embed.addFields({
    //         name: question.question,
    //         value: `\`\`\`${response}\`\`\``,
    //       });
    //     });
    
    //     if (ticketChannel) ticketChannel.send({ embeds: [embed] });
    //   }

    if (!ticket) return;
    const ticketChannel = client.guilds.cache.get(ticket.guildID).channels.cache.get(ticket.channelID);


    try {
        const ticketMessage = await ticketChannel.messages.fetch(ticket.msgID);
        let existingEmbed = EmbedBuilder.from(ticketMessage.embeds[0]);
    
        let claimedByIndex = existingEmbed.data.fields.findIndex(field => field.name === "Claimed by");
    
        let formattedQuestions = "";
        if (ticket.questions && ticket.questions.length > 0) {
            ticket.questions.forEach(question => {
                let response = question.response || "Not answered";
                formattedQuestions += `**${question.question}**\n\`\`\`${response}\`\`\`\n`;
            });
    
            if (claimedByIndex !== -1) {
                // ✅ Insert "Ticket Questions" directly under "Claimed by"
                existingEmbed.data.fields.splice(claimedByIndex + 1, 0, { 
                    name: "Ticket Questions", 
                    value: formattedQuestions 
                });
            } else {
                existingEmbed.addFields({ name: "Ticket Questions", value: formattedQuestions });
            }
        }
        
        await ticketMessage.edit({ embeds: [existingEmbed] });
    } catch (error) {
        console.error("Failed to update ticket message:", error);
    }

    const user = await Users.findOne({ discord_id: ticket.userID });
    if (!user) {
        console.log("User not found in database.");
        return;
    }

    const steamID = user.steam_id;
    if (!steamID) {
        console.log("User does not have a linked Steam ID.");
        return;
    }

    let steamData = { playtime: "N/A", banStatus: "N/A" };
    let battleMetricsData = { bans: "No Bans Found" };
    let rustStats = { kills: "N/A", deaths: "N/A", kdRatio: "N/A" }
    let banData = { banStatus: "N/A", nexusProfile: "N/A" }

    try {
        steamData = await getSteamData(steamID);
    } catch (error) {
        console.error("Failed to fetch Steam data:", error);
    }

    try {
      rustStats = await getRustStats(steamID)
    } catch (error) {
      console.error("Failed to get Rust Stats", error)
    }

    try {
      banData = await checkGameBan(steamID)
    } catch (error) {
      console.error("Failed to fetch Ban data:", error);
    }

    try {
      battleMetricsData = await getBattleMetricsData(steamID);
  } catch (error) {
      console.error("Failed to fetch BattleMetrics data:", error);
  }

  if (battleMetricsData.bannedServers.length > 0) {
      bannedServersText = battleMetricsData.bannedServers.map(server => `- ${server}`).join("\n");
  }

        let bans = await GetBans(steamID);
        // let notes = await GetNotes(steamID, config.bm_org_id);
        let hoursAndServers = await GetHoursandServerInfo(steamID);
        let kdF7 = await GetKDandF7(steamID);

    const playerEmbed = new EmbedBuilder()
    .setTitle("Player Information")
    .setColor(config.EmbedColors)
    .setThumbnail(user.discord_pfp || "")
    .addFields(
        { name: "Steam Username", value: user.steam_name || "Unknown", inline: true },
        { name: "Steam Profile", value: `[Click Here](https://steamcommunity.com/profiles/${steamID})`, inline: true },
        { name: "Total Hours", value: steamData.playtime, inline: true },
        { name: "Total Servers Played", value: battleMetricsData.serversPlayed, inline: true },
        { name: "Account Age", value: battleMetricsData.accountAge, inline: true },
        { name: "Kill/Death Ratio", value: `\`\`\`Kills: ${rustStats.kills} | Deaths: ${rustStats.deaths} | K/D: ${rustStats.kdRatio}\`\`\``, inline: false },
        { name: "Steam Bans", value: steamData.banStatus, inline: true },
        { name: "Game Bans", value: `${banData.banStatus} | [Check Profile](${banData.nexusProfile})`, inline: true },
        { name: "BattleMetrics Links", value: `[Public Profile](${battleMetricsData.profileURL}) | [RCON Profile](${battleMetricsData.rconURL})`, inline: false },
        { name: "Organization Hours", value: `${hoursAndServers.orgHours.toFixed(1)}` || "0", inline: true },
        { name: "Aimtrain Hours", value: `${hoursAndServers.aimtrainHours.toFixed(1)}` || "0", inline: true },
        { name: "Total Hours in Rust", value: `${hoursAndServers.allHours.toFixed(1)}` || "0", inline: true },
        { name: "K/D Over 14 Days", value: kdF7.KDDay || `N/A`, inline: true },
        { name: "F7 Reports Total", value: kdF7.F7Total || `N/A`, inline: true },
        { name: "F7 Reports 24 Hours", value: kdF7.F7Day || `N/A`, inline: true }
    )
    .setTimestamp();

if (bans.bans && bans.bans !== "No bans on record.") {
    playerEmbed.addFields({ name: "Recorded Bans", value: `\`\`\`${bans.bans}\`\`\``, inline: false });
}

if (bans.bannedServers && bans.bannedServers !== "No banned servers found.") {
    const bannedEmbed = new EmbedBuilder()
        .setTitle(`*Banned Servers*`)
        .setDescription(`\`\`\`${bans.bannedServers}\`\`\``)
        .setColor(config.EmbedColors)
        .setTimestamp();

    ticketChannel.send({ embeds: [playerEmbed, bannedEmbed] });
} else {
    ticketChannel.send({ embeds: [playerEmbed] });
}

    async function getSteamData(steamID) {
      if (!config.steam_api_key) {
          console.error("Missing Steam API Key!");
          return { playtime: "N/A", banStatus: "N/A", gameBans: "N/A" };
      }
  
    const bansURL = `https://api.steampowered.com/ISteamUser/GetPlayerBans/v1/?key=${config.steam_api_key}&steamids=${steamID}`;
    const playtimeURI = `https://api.steampowered.com/IPlayerService/GetOwnedGames/v1/?key=${config.steam_api_key}&steamid=${steamID}&include_played_free_games=true`;
    const playerSummariesURL = `https://api.steampowered.com/ISteamUser/GetPlayerSummaries/v2/?key=${config.steam_api_key}&steamids=${steamID}`;

    try {
        const [banResponse, playtimeResponse, summaryResponse] = await Promise.all([
            axios.get(bansURL),
            axios.get(playtimeURI),
            axios.get(playerSummariesURL)
        ]);

        // ✅ Check if the profile is private
        let profileStatus = "Public";
        let playerData = summaryResponse.data?.response?.players?.[0];

        if (playerData && playerData.communityvisibilitystate !== 3) {
            profileStatus = "Private";
            console.log(`ℹ️ Steam profile ${steamID} is private.`);
        }

        // ✅ Default playtime
        let playtime = "N/A";
        if (playtimeResponse.data?.response?.games && profileStatus === "Public") {
            let rustGame = playtimeResponse.data.response.games.find(game => game.appid === 252490);
            if (rustGame) {
                let totalMinutes = rustGame.playtime_forever || 0;
                playtime = (totalMinutes / 60).toFixed(1); // Convert to hours
            }
        }

        const banData = banResponse.data?.players?.[0] || {};
        let banStatus = banData.VACBanned ? `VAC Banned: ${banData.NumberOfVACBans} times` : "No Bans";
        let gameBans = banData.NumberOfGameBans > 0 ? `Game Bans: ${banData.NumberOfGameBans}` : "No Game Bans";

        return { playtime, banStatus, gameBans, profileStatus };
    } catch (error) {
        console.error("❌ Failed to fetch Steam data:", error.message);
        return { playtime: "N/A", banStatus: "N/A", gameBans: "N/A", profileStatus: "Error" };
    }
}

  async function checkGameBan(steamID) {
    const nexusURL = `https://www.nexusonline.co.uk/bans/profile/?id=${steamID}`;

    try {
        const response = await axios.get(nexusURL);
        const $ = cheerio.load(response.data);
        const banText = $(".header").text().trim();
        let banStatus = "No Game Bans Found";
        if (banText.includes("Player is currently game banned!")) {
            banStatus = "User is Game Banned!";
        }

        return { banStatus, nexusProfile: nexusURL };
    } catch (error) {
        console.error("❌ Failed to fetch game ban status:", error.message);
        return { banStatus: "N/A (Failed to Check)", nexusProfile: nexusURL };
    }
}
  
  async function getBattleMetricsData(steamID) {
    if (!config.bm_api_token) {
        logToFile("❌ Missing BattleMetrics API Key!");
        return {
            bans: "No Bans Found",
            bannedServers: [], // ✅ Always an array
            f7Total: "N/A",
            altAccounts: "No Alts Found",
            accountAge: "N/A",
            serversPlayed: "N/A",
            profileURL: "N/A",
            rconURL: "N/A"
        };
    }

    const headers = { Authorization: `Bearer ${config.bm_api_token}` };
    const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;

    try {
        const response = await axios.get(bmURL, { headers });

        if (!response.data || !response.data.data.length) {
            return {
                bans: "No Bans Found",
                bannedServers: [], // ✅ Always an array
                f7Total: "N/A",
                altAccounts: "No Alts Found",
                accountAge: "N/A",
                serversPlayed: "N/A",
                profileURL: "N/A",
                rconURL: "N/A",
                BMID: "N/A"
            };
        }

        const playerData = response.data.data[0];
        logToFile(response.data)

        let profileURL = `https://www.battlemetrics.com/players/${playerData.id}`;
        let rconURL = `https://www.battlemetrics.com/rcon/players/${playerData.id}`;
        let bans = playerData.attributes.banCount > 0 ? `🚨 ${playerData.attributes.banCount} Recorded Bans` : "No Bans Found";
        let serversPlayed = playerData.relationships?.servers?.data?.length ? `${playerData.relationships.servers.data.length}` : "N/A";
        let accountCreatedAt = playerData.attributes.createdAt || null;
        let accountAge = accountCreatedAt 
            ? `<t:${Math.floor(new Date(accountCreatedAt).getTime() / 1000)}:F>`
            : "N/A";

        // ✅ Fetch F7 Reports
        let f7Total = "N/A";
        if (playerData.relationships?.playerFlags?.data?.length > 0) {
            f7Total = `${playerData.relationships.playerFlags.data.length}`;
        }

        // ✅ Fetch Banned Servers (FIXED)
        let bannedServers = [];
        if (playerData.relationships?.flags?.data?.length > 0) {
            bannedServers = playerData.relationships.flags.data
                .map(flag => `[Server Ban](https://www.battlemetrics.com/players/${flag.id})`);
        }

        return {
            bans,
            bannedServers,
            f7Total,
            accountAge,
            serversPlayed,
            profileURL,
            rconURL,
            BMID: `${playerData.id}`
        };
    } catch (error) {
        logToFile(`❌ Failed to fetch BattleMetrics data for ${steamID}: ${error.response?.data?.errors?.[0]?.detail || error.message}`);
        return {
            bans: "No Bans Found",
            bannedServers: [],
            f7Total: "N/A",
            altAccounts: [],
            accountAge: "N/A",
            serversPlayed: "N/A",
            profileURL: "N/A",
            rconURL: "N/A",
            BMID: "N/A"
        };
    }
}

async function getRustStats(steamID) {
  if (!config.steam_api_key) {
      console.error("❌ Missing Steam API Key!");
      return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
  }

  const rustStatsURL = `https://api.steampowered.com/ISteamUserStats/GetUserStatsForGame/v0002/?appid=252490&key=${config.steam_api_key}&steamid=${steamID}`;

  try {
      const response = await axios.get(rustStatsURL);

      if (!response.data?.playerstats?.stats) {
          return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
      }

      const stats = response.data.playerstats.stats;

      // ✅ Extract kills and deaths
      const killsStat = stats.find(stat => stat.name === "kill_player");
      const deathsStat = stats.find(stat => stat.name === "deaths");

      const kills = killsStat ? killsStat.value : 0;
      const deaths = deathsStat ? deathsStat.value : 0;
      const kdRatio = deaths > 0 ? (kills / deaths).toFixed(2) : `${kills}.00`;

      return { kills, deaths, kdRatio };
  } catch (error) {
      console.error("❌ Failed to fetch Rust stats:", error.message);
      return { kills: "N/A", deaths: "N/A", kdRatio: "N/A" };
  }
}

async function getKDOver14Days(steamID) {
  if (!config.bm_api_token) return "N/A";

  const headers = { Authorization: `Bearer ${config.bm_api_token}` };

  const now = new Date();
  const fourteenDaysAgo = new Date(now - 14 * 24 * 60 * 60 * 1000);
  
  const combatLogURL = `https://api.battlemetrics.com/activity?filter[players]=${steamID}&filter[types]=kill,death&include=server&sort=-timestamp`;

  try {
      const response = await axios.get(combatLogURL, { headers });
      if (!response.data?.data.length) {
          return "N/A";
      }

      let kills = 0, deaths = 0;

      response.data.data.forEach(log => {
          let eventTime = new Date(log.attributes.timestamp);
          if (eventTime >= fourteenDaysAgo && eventTime <= now) {
              let eventType = log.attributes.type;
              if (eventType === "kill") kills++;
              if (eventType === "death") deaths++;
          }
      });

      let kdRatio = deaths > 0 ? (kills / deaths).toFixed(2) : `${kills}.00`;
      logToFile(`✅ ${steamID} KD Over 14 Days: ${kills}/${deaths} (K/D: ${kdRatio})`);
      return `${kills}/${deaths} (K/D: ${kdRatio})`;
  } catch (error) {
      logToFile(`❌ Failed to fetch K/D Over 14 Days for ${steamID}: ${error.response?.data?.errors?.[0]?.detail || error.message}`);
      console.log(error)
      return "N/A";
  }
}

async function GetBans(steamID) {
    if (!steamID) {
        console.error("❌ Error: steamID is undefined, cannot fetch bans.");
        return { bans: "Error: steamID is missing.", bannedServers: [] };
    }

    try {
        if (!config.bm_api_token) {
            console.error("❌ Error: Missing BattleMetrics API token.");
            return { bans: "Error: Missing API token.", bannedServers: [] };
        }

        const headers = { Authorization: `Bearer ${config.bm_api_token}` };

        // ✅ Fetch Player ID from BattleMetrics
        const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;
        const BMData = await axios.get(bmURL, { headers });

        if (!BMData.data?.data?.length) {
            return { bans: "Error: Player not found on BattleMetrics.", bannedServers: [] };
        }

        const playerData = BMData.data.data[0];

        // ✅ Fetch bans for the player
        const bansURL = `https://api.battlemetrics.com/bans?filter[player]=${playerData.id}&include=server&page[size]=100`;
        const response = await axios.get(bansURL, { headers });

        if (response.status === 401) {
            console.error("❌ Unauthorized: Check API token permissions.");
            return { bans: "Error: Unauthorized request. Check API token.", bannedServers: [] };
        }

        if (response.status !== 200) {
            console.error(`⚠️ BattleMetrics API returned status: ${response.status}`);
            return { bans: "Error fetching ban data.", bannedServers: [] };
        }

        const bans = response.data?.data || [];
        if (bans.length === 0) return { bans: "No bans on record.", bannedServers: [] };

        let bannedServers = [];

        // ✅ Fetch **Server Names** instead of IDs along with Ban Reasons
        for (let ban of bans) {
            let serverName = "Unknown Server";

            // 🔍 Check if the server data exists in the API response
            if (ban.relationships?.server?.data?.id) {
                const serverData = response.data.included?.find(s => s.id === ban.relationships.server.data.id);
                if (serverData) {
                    serverName = serverData.attributes?.name || "Unknown Server";
                }
            }

            let reason = ban.attributes?.reason || "No reason provided";
            bannedServers.push(`${serverName} - ${reason}\n`);
        }

        const banReasons = response.data?.data.length 
        ? response.data.data.map(ban => ban.attributes.reason).join("\n") 
        : "No bans on record.";

        return {
            bans: banReasons,
            bannedServers: bannedServers.length > 0 ? bannedServers.join("\n") : "No banned servers found.",
        };

    } catch (error) {
        if (error.response && error.response.status === 401) {
            console.error("❌ Unauthorized API request. Check API token.");
            return { bans: "Error: Unauthorized API request. Check API token.", bannedServers: [] };
        }

        console.error(`❌ Error fetching bans: ${error.message}`);
        return { bans: "Failed to retrieve ban data.", bannedServers: [] };
    }
}

// FInish later
// async function GetNotes(steamID, orgId) {
//     const headers = { Authorization: `Bearer ${config.bm_api_token}` };
//     const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;
//     const BMData = await axios.get(bmURL, { headers });
//     const playerData = BMData.data.data[0];
//     const url = `https://api.battlemetrics.com/activity?tagTypeMode=and&filter[types][blacklist]=event:query&filter[players]=${playerData.id}&include=organization,user&page[size]=1000`;
//     let retries = 3; // Number of retries allowed
//     let delay = 3000; // Initial delay in milliseconds

//     for (let attempt = 0; attempt < retries; attempt++) {
//         try {
//             const response = await axios.get(url, { timeout: 10000 });

//             if (response.status !== 200) {
//                 throw new Error(`Unexpected response: ${response.status}`);
//             }

//             const notesData = response.data?.data || [];

//             if (notesData.length === 0) {
//                 return { noteInformationfirst: "No notes on record.", noteCountfirst: 0, orgNoteCountfirst: 0 };
//             }

//             let finalNotes = notesData.filter(note => note.relationships.organization.data.id === orgId);
//             let noteCount = notesData.length;
//             let orgNoteCount = finalNotes.length;

//             let noteText = finalNotes.length > 0
//                 ? finalNotes.map(note => `\`\`\`${note.attributes.note}\`\`\``).join("\n")
//                 : "No notes from your organization.";

//             return {
//                 noteInformationfirst: noteText,
//                 noteCountfirst: noteCount,
//                 orgNoteCountfirst: orgNoteCount
//             };

//         } catch (error) {
//             if (error.response && error.response.status === 504) {
//                 console.warn(`⚠️ BattleMetrics API timeout (504). Retrying in ${delay / 1000}s...`);
//                 await new Promise(resolve => setTimeout(resolve, delay));
//                 delay *= 2; // Exponential backoff
//             } else {
//                 console.error(`❌ Failed to fetch notes: ${error.message}`);
//                 return { noteInformationfirst: "Error fetching notes.", noteCountfirst: "N/A", orgNoteCountfirst: "N/A" };
//             }
//         }
//     }

//     return { noteInformationfirst: "Failed after multiple retries.", noteCountfirst: "N/A", orgNoteCountfirst: "N/A" };
// }


async function GetHoursandServerInfo(steamID, retryCount = 0) {
    const headers = { Authorization: `Bearer ${config.bm_api_token}` };
    const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;
    const BMData = await axios.get(bmURL, { headers });

    // Check if playerData exists
    const playerData = BMData.data?.data?.[0];
    if (!playerData) {
        console.error(`❌ Player not found for steamID: ${steamID}`);
        return {
            allHours: 0,
            orgHours: 0,
            aimtrainHours: 0,
            totalServers: 0,
            orgServers: []
        };
    }

    const url = `https://api.battlemetrics.com/players/${playerData.id}?include=server&fields[server]=name&access_token=${config.bm_api_token}`;

    try {
        const response = await axios.get(url);

        if (response.status !== 200) {
            throw new Error(`Unexpected response status: ${response.status}`);
        }

        let totalHours = 0, aimtrainHours = 0, orgHours = 0, totalServers = 0;
        let orgServers = [];

        if (response.data?.included) {
            response.data.included.forEach(server => {
                if (server.relationships?.game?.data?.id === "rust") {
                    totalServers++;
                    totalHours += server.meta?.timePlayed || 0;

                    if (server.attributes.name.toLowerCase().includes("aim") || 
                        server.attributes.name.toLowerCase().includes("training grounds") || 
                        server.attributes.name.toLowerCase().includes("ukn") || 
                        server.attributes.name.toLowerCase().includes("aimtrain")) {
                        aimtrainHours += server.meta?.timePlayed || 0;
                    } else if (server?.relationships?.organization?.data?.id === config.bm_org_id) {
                        orgHours += server.meta?.timePlayed || 0;
                    }

                    if (server.relationships?.organization?.data?.id === config.bm_org_id) {
                        orgServers.push({
                            serverName: server.attributes.name || "Unknown",
                            serverId: server.id || "N/A",
                            online: server.meta?.online || false,
                            timePlayed: server.meta?.timePlayed || 0,
                            lastSeen: server.meta?.lastSeen || "Unknown"
                        });
                    }
                }
            });
        }

        return {
            allHours: totalHours / 3600 || 0, 
            orgHours: orgHours / 3600 || 0, 
            aimtrainHours: aimtrainHours / 3600 || 0, 
            totalServers: totalServers || 0, 
            orgServers
        };
    } catch (error) {
        console.error(`❌ Error in GetHoursandServerInfo: ${error.message}`);

        if (error.response) {
            console.error(`Response Data:`, error.response.data);
            if (error.response.status === 429 && retryCount < 3) {
                console.warn(`⚠️ Rate limited! Retrying in 5 seconds (attempt ${retryCount + 1}/3)...`);
                await new Promise(resolve => setTimeout(resolve, 5000));
                return await GetHoursandServerInfo(steamID, retryCount + 1);
            }
        }

        return {
            allHours: 0, 
            orgHours: 0, 
            aimtrainHours: 0, 
            totalServers: 0, 
            orgServers: []
        };
    }
}




async function GetKDandF7(steamID) {
    if (!steamID || steamID === "") {
        console.error("❌ Error: steamID is undefined or empty, cannot fetch K/D and F7 reports.");
        return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
    }

    let kills = 0, deaths = 0;
    let killsOneDay = 0, deathsOneDay = 0;
    let f7reports = 0, f7reportsOneDay = 0;
    let oneDay = Date.now() - (14 * 24 * 60 * 60 * 1000); // Time threshold for 14 days

    const headers = { Authorization: `Bearer ${config.bm_api_token}` };
    const bmURL = `https://api.battlemetrics.com/players?filter[search]=${steamID}&include=server,playerFlag,identifier,flagPlayer`;

    try {
        const BMData = await axios.get(bmURL, { headers });

        // Check if player data exists
        if (!BMData.data?.data?.length) {
            console.log(`❌ Player not found for steamID: ${steamID}`);
            return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
        }

        const playerData = BMData.data.data[0];
        if (!playerData?.id) {
            console.error(`❌ Player data missing ID for steamID: ${steamID}`);
            return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
        }

        let url = `https://api.battlemetrics.com/activity?tagTypeMode=and&filter[types][blacklist]=event:query&filter[players]=${playerData.id}&include=organization,user&page[size]=1000&access_token=${config.bm_api_token}`;

        await fetchData(url);
    } catch (error) {
        console.error(`❌ Error fetching K/D and F7 reports: ${error.message}`);
        return { KDTotal: "N/A", KDDay: "N/A", killsTotal: 0, deathsTotal: 0, kills24hr: 0, deaths24hr: 0, F7Total: 0, F7Day: 0 };
    }

    function parseEvent(event) {
        if (!event || !event.attributes) return; // Guard against undefined events

        let timestamp = new Date(event.attributes.timestamp).getTime();

        if (event.attributes.messageType === "rustLog:playerDeath:PVP") {
            if (event.attributes.data?.killer_id === playerData.id) { // Use playerData.id instead of BMID
                kills++;
                if (timestamp > oneDay) killsOneDay++;
            } else if (event.attributes.data?.victim_id === playerData.id) { // Ensure death tracking is accurate
                deaths++;
                if (timestamp > oneDay) deathsOneDay++;
            }
        }

        if (event.attributes.messageType === "rustLog:playerReport") {
            if (event.attributes.data?.forPlayerId === playerData.id) {
                f7reports++;
                if (timestamp > oneDay) f7reportsOneDay++;
            }
        }
    }

    async function fetchData(requestUrl) {
        try {
            const response = await axios.get(requestUrl, { timeout: 10000 });

            if (response.status !== 200) throw new Error(`Unexpected response status: ${response.status}`);

            const events = response.data?.data || [];
            events.forEach(parseEvent);

            if (response.data?.links?.next) {
                await fetchData(response.data.links.next);
            }
        } catch (error) {
            if (error.response?.status === 429) {
                console.warn(`⚠️ Rate limited! Retrying in 5 seconds...`);
                await new Promise(resolve => setTimeout(resolve, 5000));
                await fetchData(requestUrl);
            } else {
                throw error;
            }
        }
    }

    let kd = deaths > 0 ? (kills / deaths).toFixed(2) : kills.toFixed(2);
    let kdOneDay = deathsOneDay > 0 ? (killsOneDay / deathsOneDay).toFixed(2) : killsOneDay.toFixed(2);

    return { KDTotal: kd, KDDay: kdOneDay, killsTotal: kills, deathsTotal: deaths, kills24hr: killsOneDay, deaths24hr: deathsOneDay, F7Total: f7reports, F7Day: f7reportsOneDay };
}

};

// %%__FILEHASH__%%
// %%__NONCE__%%