// Use the config adapter for backward compatibility with the new config structure
const config = require('../utils/configAdapter');
const { ActivityType } = require('discord.js');
const BMRequests = require('../utils/bmRequests');
const { logToPublicChannel, logToStaffChannel, getServerName, getBanMonitorConfig } = require('../utils/banLogger');
const BanModel = require('../models/banModel');
const { updateDailyStats, updateYearlyStats } = require('./dailyStatsUpdater');

let monitorInterval = null;

/**
 * Initializes the ban monitoring system
 * @param {Client} client - Discord.js client
 */
module.exports = async (client) => {
    console.log('Ban monitoring system initializing...');

    // Initialize the ban monitoring
    await startBanMonitoring(client);

    // Set up interval to check if monitoring should be running
    setInterval(async () => {
        const banConfig = await getBanMonitorConfig();

        if (banConfig.enabled && !monitorInterval) {
            console.log('Ban monitoring was disabled but is now enabled. Starting...');
            await startBanMonitoring(client);
        } else if (!banConfig.enabled && monitorInterval) {
            console.log('Ban monitoring was enabled but is now disabled. Stopping...');
            clearInterval(monitorInterval);
            monitorInterval = null;
        }
    }, 60000); // Check every minute
};

/**
 * Starts the ban monitoring process
 * @param {Client} client - Discord.js client
 */
async function startBanMonitoring(client) {
    // Clear any existing interval
    if (monitorInterval) {
        clearInterval(monitorInterval);
        monitorInterval = null;
    }

    const banConfig = await getBanMonitorConfig();

    // Don't start if disabled or no ban list ID
    if (!banConfig.enabled || !banConfig.banListId) {
        console.log('Ban monitoring is disabled or missing ban list ID');
        return;
    }

    // Start the monitoring interval
    console.log(`Starting ban monitoring with interval: ${banConfig.checkInterval}ms`);

    // Run once immediately
    await checkForNewBans(client);

    // Then set up the interval
    monitorInterval = setInterval(async () => {
        await checkForNewBans(client);
    }, banConfig.checkInterval);
}

/**
 * Checks for new bans and processes them
 * @param {Client} client - Discord.js client
 */
async function checkForNewBans(client) {
    try {
        const banConfig = await getBanMonitorConfig();

        if (!banConfig.enabled || !banConfig.banListId) {
            return;
        }

        // Update last checked time
        banConfig.lastChecked = new Date();
        await banConfig.save();

        // Fetch bans from BattleMetrics
        const url = `https://api.battlemetrics.com/bans?filter[banList]=${banConfig.banListId}&include=user,server&filter[expired]=false&page[size]=10`;
        const resp = await BMRequests.getRequest(url, config.bm_api_token);

        if (resp.status !== 200) {
            console.error(`Error fetching bans: ${resp.status} - ${url}`);
            return;
        }

        const respData = JSON.parse(resp.text);

        // Update bot activity with ban count if available
        if (respData.meta && respData.meta.active !== undefined) {
            client.user.setActivity(`${respData.meta.active} Bans!`, { type: ActivityType.Watching });
        }

        // Process each ban
        for (const ban of respData.data) {
            // Check if we've already processed this ban
            const existingBan = await BanModel.findOne({ banId: ban.id });

            if (!existingBan) {
                // This is a new ban, process it
                await processNewBan(ban);

                // Update stats
                const metricsToUpdate = ['newBans'];
                await updateDailyStats(config.GuildID, metricsToUpdate);
                await updateYearlyStats(config.GuildID, metricsToUpdate);
            }
        }
    } catch (error) {
        console.error(`Error in checkForNewBans: ${error.message}`);
    }
}

/**
 * Processes a new ban
 * @param {Object} ban - Ban data from BattleMetrics
 */
async function processNewBan(ban) {
    try {
        const playerID = ban.relationships?.player?.data?.id || "Unknown Player";
        console.log(`New Ban: ${playerID}`);

        // Extract Steam ID (if available)
        let steamID = "Unknown";
        for (const identifier of ban.attributes.identifiers) {
            if (identifier.type === "steamID") {
                steamID = identifier.identifier;
                break;
            }
        }

        // Get Profile Picture
        let userAvatar = "https://avatars.akamai.steamstatic.com/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb_full.jpg";
        try {
            userAvatar = ban.attributes.identifiers[0].metadata.profile.avatarfull || userAvatar;
        } catch (err) {
            // Use default avatar if we can't get one
        }

        // Calculate unban time
        let timeUnbanned = "Permanent";
        let timeUn = "Permanent";

        if (ban.attributes.expires) {
            const unbanDate = new Date(ban.attributes.expires);
            timeUnbanned = `<t:${Math.floor(unbanDate.getTime() / 1000)}:F>`;
            timeUn = `<t:${Math.floor(unbanDate.getTime() / 1000)}:R>`;
        }

        // Get Ban Note
        let banNote = ban.attributes.note || "Empty Ban Note";

        // Get Server Name
        const serverName = await getServerName(ban);

        // Log Ban to Discord
        await logToPublicChannel(ban, steamID, serverName, timeUnbanned, userAvatar);
        await logToStaffChannel(ban, steamID, serverName, timeUn, userAvatar, banNote);

        // Try to identify which staff member issued the ban
        try {
            const StaffManagerIntegration = require('../utils/staffManagerIntegration');
            const staffIntegration = new StaffManagerIntegration(global.client);

            const staffUserId = await staffIntegration.identifyBanIssuer(ban);
            if (staffUserId) {
                console.log(`Ban ${ban.id} attributed to staff member: ${staffUserId}`);

                // Notify Staff Manager about the ban activity
                await staffIntegration.notifyBanActivity(staffUserId, {
                    banId: ban.id,
                    playerName: ban.attributes.identifiers?.[0]?.metadata?.profile?.personaname || 'Unknown',
                    playerSteamId: steamID,
                    reason: ban.attributes.reason,
                    serverName: serverName,
                    banNote: banNote
                });
            }
        } catch (error) {
            console.error('Error identifying ban issuer:', error);
        }

        // Save the ban to our database
        const newBan = new BanModel({
            banId: ban.id,
            playerBMId: playerID,
            steamId: steamID,
            reason: ban.attributes.reason,
            serverName: serverName,
            expiresAt: ban.attributes.expires ? new Date(ban.attributes.expires) : null
        });

        await newBan.save();
    } catch (error) {
        console.error(`Error processing new ban: ${error.message}`);
    }
}
