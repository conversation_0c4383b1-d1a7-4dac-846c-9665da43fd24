const Discord = require("discord.js");
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../utils/configAdapter');
const guildModel = require("../models/guildModel");
const ticketModel = require("../models/ticketModel");
const { updateDailyStats, updateYearlyStats } = require('../events/dailyStatsUpdater');
const StaffManagerIntegration = require('../utils/staffManagerIntegration');

module.exports = async (client, interaction) => {

    // Add 1 to totalClaims everytime a ticket gets claimed
    const statsDB = await guildModel.findOne({ guildID: config.GuildID });
    statsDB.totalClaims++;
    await statsDB.save();

    const metricsToUpdate = ['totalClaims'];
    await updateDailyStats(config.GuildID, metricsToUpdate);
    await updateYearlyStats(config.GuildID, metricsToUpdate);

    // Notify Staff Manager about the ticket claim
    try {
        const staffIntegration = new StaffManagerIntegration(client);
        const ticketData = await ticketModel.findOne({ channelID: interaction.channel.id });

        if (ticketData) {
            await staffIntegration.notifyTicketClaim(interaction.user.id, {
                channelID: interaction.channel.id,
                ticketType: ticketData.ticketType,
                userID: ticketData.userID,
                username: ticketData.username
            });

            console.log(`Notified Staff Manager of ticket claim by ${interaction.user.tag}`);
        }
    } catch (error) {
        console.error('Error notifying Staff Manager of ticket claim:', error);
    }

};