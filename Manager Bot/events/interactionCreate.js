const {
  ActionRow<PERSON>uilder,
  <PERSON>tonBuilder,
  ButtonStyle,
  EmbedBuilder,
  InteractionType,
  StringSelectMenuBuilder,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  WebhookClient
} = require("discord.js");
const fs = require('fs');
const config = require('../utils/configAdapter');
const utils = require("../utils.js");
const moment = require('moment-timezone');
let Cooldown = new Map();
const guildModel = require("../models/guildModel.js");
const ticketModel = require("../models/ticketModel.js");
const PolicyAgreement = require('../models/PolicyAgreement');

// Initialize webhooks from config
const logsHook = config.Webhooks.ticketWebhookURL ? new WebhookClient({ url: config.Webhooks.ticketWebhookURL }) : null;
const ticketNotificationWebhook = config.Webhooks.ticketNotificationWebhook ? new WebhookClient({ url: config.Webhooks.ticketNotificationWebhook }) : null;
const systemLogsHook = config.Webhooks.systemLogHook ? new WebhookClient({ url: config.Webhooks.systemLogHook }) : null;
const bugReportLogs = config.Webhooks.bugReportLogWebhook ? new WebhookClient({ url: config.Webhooks.bugReportLogWebhook }) : null;
const webhooks = Array.isArray(config.Webhooks.webhookURLs) ? config.Webhooks.webhookURLs.map(url => new WebhookClient({ url })) : [];

async function sendToAllWebhooks(embed) {
  if (webhooks.length === 0) {
    console.log("No webhooks configured. Skipping webhook notifications.");
    return;
  }

  for (const webhook of webhooks) {
    await webhook.send({ embeds: [embed] }).catch(err => console.error(`❌ Webhook Error: ${err.message}`));
  }
}

module.exports = async (client, interaction) => {
  if (interaction.isChatInputCommand()) return;

  // Initialize userSelection if it doesn't exist
  if (!client.userSelection) {
    client.userSelection = {};
  }

  let sMenu = interaction.values ? interaction.values[0] : (interaction.customId || '');
  const statsDB = await guildModel.findOne({ guildID: config.BotSettings.GuildID });

  async function processTicketCreation(interaction, buttonConfig, customIdentifier, buttonNumber, responses) {
    // Fetch the staff guild
    const staffGuild = client.guilds.cache.get(config.BotSettings.StaffGuild);
    if (!staffGuild) {
      console.error(`❌ Staff guild (${config.BotSettings.StaffGuild}) not found!`);
      return interaction.reply({
        content: "The staff server is not accessible. Please contact an administrator.",
        ephemeral: true
      });
    }

    // Verify the ticket category exists in the staff guild
    if (!staffGuild.channels.cache.get(buttonConfig.TicketCategoryID)) {
      console.log('\x1b[31m%s\x1b[0m', `[WARNING] ${buttonNumber}.TicketCategoryID is not a valid category in staff guild!`);
      return interaction.reply({
        content: "This ticket type is not properly configured in the staff server. Please contact an administrator.",
        ephemeral: true
      });
    }

    const selectedServer = client.userSelection?.[interaction.user.id]?.server || "Not Selected";

    let tChannelName = buttonConfig.ChannelName
      .replace(/{username}/g, interaction.user.username)
      .replace(/{total-tickets}/g, statsDB ? statsDB.totalTickets : 0)
      .replace(/{user-id}/g, interaction.user.id);

    const ticketDeleteButton = new ButtonBuilder()
      .setCustomId('closeTicket')
      .setLabel(config.Locale.TicketManagement.CloseTicketButton)
      .setStyle(config.ButtonCustomization.Colors.closeTicket);

    const ticketClaimButton = new ButtonBuilder()
      .setCustomId('ticketclaim')
      .setLabel(config.Locale.TicketClaiming.claimTicketButton)
      .setStyle(config.ButtonCustomization.Colors.ticketClaim);

    let row1 = config.TicketSystem.ClaimingSystem.Enabled
      ? new ActionRowBuilder().addComponents(ticketDeleteButton, ticketClaimButton)
      : new ActionRowBuilder().addComponents(ticketDeleteButton);

    let NewTicketMsg = buttonConfig.TicketMessage
      .replace(/{user}/g, interaction.user.username) // Use username instead of mention
      .replace(/{createdAt}/g, `<t:${(Date.now() / 1000 | 0)}:R>`)
      .replace(/{server}/g, selectedServer);

    let NewTicketMsgTitle = buttonConfig.TicketMessageTitle.replace(/{category}/g, buttonConfig.TicketName);
    let userIcon = interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 });

    await handleTicketChecks(interaction, buttonConfig, buttonNumber);
    await handleUserChecks(interaction, buttonConfig);

    let permissionOverwriteArray = [
      { id: staffGuild.id, deny: ['SendMessages', 'ViewChannel'] },
      { id: client.user.id, allow: ['SendMessages', 'ViewChannel', 'ManageChannels', 'UseApplicationCommands'] }
    ];

    if (buttonConfig.SupportRoles && Array.isArray(buttonConfig.SupportRoles)) {
      buttonConfig.SupportRoles.forEach(roleID => {
        if (typeof roleID === "string" && roleID.trim() !== "" && staffGuild.roles.cache.has(roleID)) {
          permissionOverwriteArray.push({
            id: roleID,
            allow: ['SendMessages', 'ViewChannel', 'UseApplicationCommands']
          });
        } else {
          console.error(`❌ Invalid or non-existent role ID in staff guild: ${roleID}`);
        }
      });
    }

    const channel = await staffGuild.channels.create({
      name: tChannelName,
      type: 0,
      parent: buttonConfig.TicketCategoryID,
      topic: config.TicketSystem.Settings.ChannelTopic
        .replace(/{username}/g, interaction.user.username)
        .replace(/{category}/g, buttonConfig.TicketName),
      permissionOverwrites: permissionOverwriteArray
    }).catch(err => {
      console.error(`❌ Failed to create channel in staff guild: ${err.message}`);
      return null;
    });

    if (!channel) {
      return interaction.reply({
        content: "Failed to create ticket channel in the staff server. Please try again or contact an administrator.",
        ephemeral: true
      });
    }

    const staffEmbed = new EmbedBuilder()
      .setTitle(NewTicketMsgTitle)
      .setDescription(NewTicketMsg)
      .setColor(config.TicketAppearance.OpenEmbed.EmbedColor)
      .setThumbnail(config.TicketAppearance.OpenEmbed.UserIconThumbnail ? userIcon : null)
      .setTimestamp();

    if (config.TicketSystem.ClaimingSystem.Enabled) {
      staffEmbed.addFields([{ name: config.Locale.TicketClaiming.ticketClaimedBy, value: `> ${config.Locale.TicketClaiming.ticketNotClaimed}` }]);
    }

    if (config.TicketAppearance.OpenEmbed.FooterMsg) {
      staffEmbed.setFooter({
        text: config.TicketAppearance.OpenEmbed.FooterMsg,
        iconURL: config.TicketAppearance.OpenEmbed.FooterIcon || null
      });
    }

    channel.send({ embeds: [staffEmbed], components: [row1], fetchReply: true }).then(async (m2) => {
      let ticketOpened = new EmbedBuilder()
        .setTitle(config.Locale.TicketCreation.ticketCreatedTitle)
        .setColor("Green")
        .setDescription("Your ticket has been created in the staff server. Staff will assist you soon.")
        .setFooter({ text: interaction.user.username, iconURL: userIcon })
        .setTimestamp();

      // Send confirmation to the user in the main server
      await interaction.reply({ embeds: [ticketOpened], ephemeral: true });

      // Send DM to user with ticket details
      await sendUserConfirmationDM(interaction.user, buttonConfig.TicketName, selectedServer);

      if (buttonConfig.MentionSupportRoles) {
        let supp = buttonConfig.SupportRoles
          .filter(r => staffGuild.roles.cache.has(r))
          .map(r => `<@&${r}>`);
        if (supp.length > 0) {
          channel.send(supp.join(" ")).then(msg => setTimeout(() => msg.delete().catch(() => {}), 500));
        }
      }

      // Send notification to logs webhook (if enabled)
      if (logsHook && config.TicketSystem.Logs.ticketClose.Enabled) {
        const logEmbed = new EmbedBuilder()
          .setTitle("New Ticket Opened")
          .setColor("Blue")
          .setDescription(
            `**Ticket Channel:** <#${channel.id}>\n` +
            `**Opened By:** ${interaction.user.username} (${interaction.user.id})\n` +
            `**Ticket Type:** ${buttonConfig.TicketName}\n` +
            `**Server:** ${selectedServer}`
          )
          .setThumbnail(userIcon)
          .setTimestamp();
        ticketNotificationWebhook.send({ username: "Ticket Notification", embeds: [logEmbed] }).catch(err => {
          console.error(`❌ Failed to send log webhook: ${err.message}`);
        });
      }

      await handleTicketDatebase(interaction, buttonConfig, customIdentifier, buttonNumber, responses, channel, m2);
      await handleTicketCooldown(interaction);
      client.emit('ticketCreate', interaction, channel, buttonNumber);
    }).catch(err => {
      console.error(`❌ Failed to send message in channel: ${err.message}`);
      interaction.reply({
        content: "Failed to initialize ticket channel in the staff server. Please try again or contact an administrator.",
        ephemeral: true
      });
    });
  }

  async function sendUserConfirmationDM(user, ticketType, selectedServer) {
    try {
      const userEmbed = new EmbedBuilder()
        .setTitle("Ticket Created Successfully!")
        .setDescription(
          `Your **${ticketType}** ticket has been created for **${selectedServer}**.\n` +
          `Staff will review your request and contact you soon. Please ensure your DMs are open for updates.`
        )
        .setColor("Green")
        .setFooter({ text: "Support Team", iconURL: user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) })
        .setTimestamp();

      await user.send({ embeds: [userEmbed] });
    } catch (error) {
      console.error("❌ Failed to send DM to user:", error);
    }
  }

  async function handleUserChecks(interaction, buttonConfig) {
    let blRole = config.TicketSystem.Settings.BlacklistedRole && interaction.member.roles.cache.has(config.TicketSystem.Settings.BlacklistedRole);
    if (blRole) {
      let ticketRoleBlacklisted = new EmbedBuilder()
        .setTitle("Role Blacklisted")
        .setColor("Red")
        .setDescription("You have a role that is blacklisted from creating tickets.")
        .setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) })
        .setTimestamp();

      if (!buttonConfig.Questions || buttonConfig.Questions.length === 0) {
        return interaction.editReply({ embeds: [ticketRoleBlacklisted], ephemeral: true });
      }
      return interaction.reply({ embeds: [ticketRoleBlacklisted], ephemeral: true });
    }
  }

  async function handleTicketChecks(interaction, buttonConfig, buttonNumber) {
    const ticketsDisabledInfo = await guildModel.findOne({ guildID: config.BotSettings.GuildID });
    const ticketsDisabled = ticketsDisabledInfo ? ticketsDisabledInfo.ticketsDisabled : false;
    const cooldown = Cooldown.get(interaction.user.id);
    const remainingTimeSeconds = Math.ceil((cooldown + (config.TicketSystem.Settings.TicketCooldown * 1000 - Date.now())) / 1000);
    const unixTimestamp = Math.floor(Date.now() / 1000) + remainingTimeSeconds;
    let max = config.TicketSystem.Settings.MaxTickets;
    let tNow = 0;

    if (ticketsDisabled) {
      const embed = new EmbedBuilder()
        .setTitle("Tickets Disabled")
        .setDescription("> ***We have disabled the tickets for a temporary time period, we will make an announcement/update letting you know when we are accepting support again.***")
        .setColor(config.BotSettings.EmbedColors)
        .setTimestamp();
      if (buttonConfig.Questions && buttonConfig.Questions.length > 0) {
        return interaction.reply({ embeds: [embed], ephemeral: true });
      }
      return interaction.editReply({ embeds: [embed], ephemeral: true });
    }

    if (config.TicketSystem.WorkingHours.Enabled && !config.TicketSystem.WorkingHours.AllowTicketsOutsideWorkingHours) {
      const workingHoursRegex = /^(\d{1,2}:\d{2})-(\d{1,2}:\d{2})$/;
      const workingHoursMatch = config.TicketSystem.WorkingHours.WorkingHours.match(workingHoursRegex);

      if (!workingHoursMatch) {
        console.log('\x1b[31m%s\x1b[0m', `[ERROR] Invalid working hours configuration (format).`);
        return interaction.reply({
          content: "Ticket system is misconfigured (working hours). Please contact an administrator.",
          ephemeral: true
        });
      }

      const currentTime = moment().tz(config.TicketSystem.WorkingHours.Timezone);
      const startDate = currentTime.format('YYYY-MM-DD');
      const startTime = moment.tz(startDate + ' ' + workingHoursMatch[1], 'YYYY-MM-DD H:mm', config.TicketSystem.WorkingHours.Timezone);
      const endTime = moment.tz(startDate + ' ' + workingHoursMatch[2], 'YYYY-MM-DD H:mm', config.TicketSystem.WorkingHours.Timezone);

      if (!startTime.isValid() || !endTime.isValid() || startTime.isSameOrAfter(endTime)) {
        console.log('\x1b[31m%s\x1b[0m', `[ERROR] Invalid working hours configuration.`);
        return interaction.reply({
          content: "Ticket system is misconfigured (working hours). Please contact an administrator.",
          ephemeral: true
        });
      }

      const withinWorkingHours = currentTime.isBetween(startTime, endTime);

      if (!withinWorkingHours) {
        const startTimestamp = startTime.unix();
        const endTimestamp = endTime.unix();
        let workingHoursEmbedLocale = config.Locale.WorkingHours.outsideWorkingHours
          .replace(/{startTime}/g, `<t:${startTimestamp}:t>`)
          .replace(/{endTime}/g, `<t:${endTimestamp}:t>`);
        let workingHoursEmbed = new EmbedBuilder()
          .setTitle(config.Locale.WorkingHours.outsideWorkingHoursTitle)
          .setColor("Red")
          .setDescription(workingHoursEmbedLocale)
          .setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) })
          .setTimestamp();

        if (buttonConfig.Questions && buttonConfig.Questions.length > 0) {
          return interaction.reply({ embeds: [workingHoursEmbed], ephemeral: true });
        }
        return interaction.editReply({ embeds: [workingHoursEmbed], ephemeral: true });
      }
    }

    if (cooldown) {
      let cooldownEmbedLocale = config.Locale.TicketCreation.cooldownEmbedMsg.replace(/{time}/g, `<t:${unixTimestamp}:R>`);
      let cooldownEmbed = new EmbedBuilder()
        .setTitle(config.Locale.TicketCreation.cooldownEmbedMsgTitle)
        .setColor("Red")
        .setDescription(cooldownEmbedLocale)
        .setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) })
        .setTimestamp();

      if (!buttonConfig.Questions || buttonConfig.Questions.length === 0) {
        return interaction.editReply({ embeds: [cooldownEmbed], ephemeral: true });
      }
      return interaction.reply({ embeds: [cooldownEmbed], ephemeral: true });
    }

    if (buttonConfig.RequiredRoles && buttonConfig.RequiredRoles.length > 0 && !buttonConfig.RequiredRoles.includes("ROLE_ID")) {
      let reqRole = false;
      let ticketRoleNotAllowed = new EmbedBuilder()
        .setTitle(config.Locale.TicketCreation.requiredRoleTitle)
        .setColor("Red")
        .setDescription(config.Locale.TicketCreation.requiredRoleMissing)
        .setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) })
        .setTimestamp();

      for (let i = 0; i < buttonConfig.RequiredRoles.length; i++) {
        if (interaction.member.roles.cache.has(buttonConfig.RequiredRoles[i])) {
          reqRole = true;
          break;
        }
      }

      if (!reqRole) {
        if (!buttonConfig.Questions || buttonConfig.Questions.length === 0) {
          return interaction.editReply({ embeds: [ticketRoleNotAllowed], ephemeral: true });
        }
        return interaction.reply({ embeds: [ticketRoleNotAllowed], ephemeral: true });
      }
    }

    let maxTickets = config.Locale.TicketCreation.AlreadyOpenMsg.replace(/{max}/g, max);
    let ticketAlreadyOpened = new EmbedBuilder()
      .setTitle(config.Locale.TicketCreation.AlreadyOpenTitle)
      .setColor("Red")
      .setDescription(maxTickets)
      .setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) })
      .setTimestamp();

    const ticketDB = await ticketModel.findOne({ userID: interaction.user.id, status: 'Open' });
    if (ticketDB) {
      const channels = Array.from(client.guilds.cache.get(config.BotSettings.StaffGuild).channels.cache);
      for (const c of channels) {
        const ticketInChannel = await ticketModel.findOne({ channelID: c[1].id });
        if (ticketInChannel && ticketInChannel.userID === interaction.user.id && ticketInChannel.status !== "Closed") {
          tNow += 1;
        }
      }
    }

    if (tNow >= max) {
      if (!buttonConfig.Questions || buttonConfig.Questions.length === 0) {
        return interaction.editReply({ embeds: [ticketAlreadyOpened], ephemeral: true }).then(() => { tNow = 0; });
      }
      return interaction.reply({ embeds: [ticketAlreadyOpened], ephemeral: true }).then(() => { tNow = 0; });
    }
  }

  async function handleTicketDatebase(interaction, buttonConfig, customIdentifier, buttonNumber, responses, channel, m2) {
    const ticketData = {
      guildID: config.BotSettings.StaffGuild, // Store staff guild ID
      channelID: channel.id,
      opened: true,
      userID: interaction.user.id,
      username: interaction.user.username,
      avatar: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }),
      ticketType: buttonConfig.TicketName,
      button: buttonNumber,
      msgID: m2.id,
      claimed: false,
      claimUser: "",
      messages: 0,
      lastMessageSent: Date.now(),
      status: "Open",
      closeUserID: "",
      ticketCreationDate: Date.now(),
      identifier: customIdentifier
    };

    if (buttonConfig.Questions && buttonConfig.Questions.length > 0) {
      ticketData.questions = buttonConfig.Questions.map(question => ({
        ...question,
        response: responses[question.customId] || ''
      }));
    }

    const newModel = new ticketModel(ticketData);
    await newModel.save();
  }

  async function handleTicketCooldown(interaction) {
    let ticketCooldown = config.TicketSystem.Settings.TicketCooldown * 1000;
    if (config.TicketSystem.Settings.TicketCooldown > 0) {
      Cooldown.set(interaction.user.id, Date.now());
      setTimeout(() => Cooldown.delete(interaction.user.id), ticketCooldown);
    }
  }

  async function handleTicketButton(interaction, buttonConfig, buttonNumber) {
    function generateUniqueIdentifier() {
      return Date.now().toString(36);
    }
    const customIdentifier = generateUniqueIdentifier();

    if (!buttonConfig || !buttonConfig.Enabled) {
      console.error(`[ERROR] Button config is undefined or disabled for TicketButton${buttonNumber}`);
      return interaction.reply({
        content: "This ticket type is not properly configured. Please contact an administrator.",
        ephemeral: true
      });
    }

    if (buttonConfig.Questions && Array.isArray(buttonConfig.Questions) && buttonConfig.Questions.length > 0 && !interaction.customId.startsWith('questionModal')) {
      try {
        const modal = new ModalBuilder()
          .setCustomId(`questionModal-${buttonNumber}-${customIdentifier}`)
          .setTitle(buttonConfig.TicketName || "Ticket Form");

        buttonConfig.Questions.forEach(question => {
          if (!question || !question.customId || !question.question) {
            console.warn(`[WARNING] Invalid question in TicketButton${buttonNumber}:`, question);
            return;
          }

          const textInput = new TextInputBuilder()
            .setCustomId(question.customId)
            .setLabel(question.question)
            .setStyle(TextInputStyle[question.style] || TextInputStyle.Short)
            .setRequired(question.required !== false)
            .setMaxLength(2000);

          const actionRow = new ActionRowBuilder().addComponents(textInput);
          modal.addComponents(actionRow);
        });

        await interaction.showModal(modal);
        return;
      } catch (error) {
        console.error(`[ERROR] Failed to create modal for TicketButton${buttonNumber}:`, error);
        return interaction.reply({
          content: "There was an error creating the ticket form. Please contact an administrator.",
          ephemeral: true
        });
      }
    }

    await processTicketCreation(interaction, buttonConfig, customIdentifier, buttonNumber);
  }

  if (interaction.type === InteractionType.ModalSubmit && interaction.customId && interaction.customId.startsWith('questionModal')) {
    const customIdParts = interaction.customId.split('-');
    if (customIdParts.length !== 3) {
      console.error(`Invalid customId format: ${interaction.customId}`);
      return interaction.reply({
        content: "Invalid modal submission. Please try again.",
        ephemeral: true
      });
    }

    const buttonNumber = customIdParts[1];
    const customIdentifier = customIdParts[2];
    const buttonConfig = config[`TicketButton${buttonNumber}`];

    if (!buttonConfig || !buttonConfig.Enabled) {
      console.error(`[ERROR] Button config not found or disabled for TicketButton${buttonNumber}`);
      return interaction.reply({
        content: "This ticket type is not properly configured. Please contact an administrator.",
        ephemeral: true
      });
    }

    const responses = {};
    buttonConfig.Questions.forEach(question => {
      responses[question.customId] = interaction.fields.getTextInputValue(question.customId);
    });

    await processTicketCreation(interaction, buttonConfig, customIdentifier, `TicketButton${buttonNumber}`, responses);
  }

  if (sMenu && typeof sMenu === 'string' && sMenu.startsWith('button') && sMenu !== 'button') {
    try {
      const buttonNumber = parseInt(sMenu.replace('button', ''));
      if (isNaN(buttonNumber) || buttonNumber < 1 || buttonNumber > 8) {
        console.error(`[ERROR] Invalid button number: ${sMenu}`);
        return interaction.reply({
          content: "Invalid ticket type selected. Please try again.",
          ephemeral: true
        });
      }

      const buttonConfigKey = `TicketButton${buttonNumber}`;
      console.log(`[DEBUG] Accessing config for ${buttonConfigKey}`);
      const buttonConfig = config[buttonConfigKey];
      console.log(`[DEBUG] buttonConfig:`, buttonConfig);

      if (!buttonConfig || !buttonConfig.Enabled) {
        console.error(`[ERROR] Button config not found or disabled for ${buttonConfigKey}`);
        return interaction.reply({
          content: "This ticket type is not properly configured. Please contact an administrator.",
          ephemeral: true
        });
      }

      const hasQuestions = Array.isArray(buttonConfig.Questions) && buttonConfig.Questions.length > 0;
      if (!hasQuestions) {
        await interaction.deferReply({ ephemeral: true });
      }

      await handleTicketButton(interaction, buttonConfig, buttonNumber);
    } catch (error) {
      console.error(`[ERROR] Error handling ticket button ${sMenu}:`, error);
      return interaction.reply({
        content: "An error occurred while processing your ticket request. Please try again or contact an administrator.",
        ephemeral: true
      });
    }
  }

  if (interaction.customId && interaction.customId === "ticketUserCheck") {
    await interaction.deferReply({ ephemeral: true });

    const userID = interaction.user.id;
    if (!Array.isArray(config.Servers) || config.Servers.length === 0) {
      console.log(`[WARNING] config.Servers is not properly defined.`);
      return interaction.editReply({
        content: "No server options are configured. Please contact an administrator.",
        components: []
      });
    }

    const serverOptions = config.Servers.map((server, index) => ({
      label: server.name || `Server ${index + 1}`,
      value: `server_${index}`,
      description: server.description || `Select ${server.name || `Server ${index + 1}`}`,
      emoji: server.emoji || undefined
    }));

    let serverMenu = new StringSelectMenuBuilder()
      .setCustomId(`serverSelect_${userID}`)
      .setPlaceholder("Select the server you're opening a ticket for")
      .setMinValues(1)
      .setMaxValues(1)
      .addOptions(serverOptions);

    let serverRow = new ActionRowBuilder().addComponents(serverMenu);

    return interaction.editReply({
      content: "Please select the server you're opening a ticket for:",
      components: [serverRow]
    });
  }

  if (interaction.customId && interaction.customId === "ticketUserCheckYes") {
    await interaction.deferUpdate();

    const userID = interaction.user.id;
    if (!Array.isArray(config.Servers) || config.Servers.length === 0) {
      console.log(`[WARNING] config.Servers is not properly defined.`);
      return interaction.editReply({
        content: "No server options are configured. Please contact an administrator.",
        embeds: [],
        components: []
      });
    }

    const serverOptions = config.Servers.map((server, index) => ({
      label: server.name || `Server ${index + 1}`,
      value: `server_${index}`,
      description: server.description || `Select ${server.name || `Server ${index + 1}`}`,
      emoji: server.emoji || undefined
    }));

    let serverMenu = new StringSelectMenuBuilder()
      .setCustomId(`serverSelect_${userID}`)
      .setPlaceholder("Select the server you're opening a ticket for")
      .setMinValues(1)
      .setMaxValues(1)
      .addOptions(serverOptions);

    let serverRow = new ActionRowBuilder().addComponents(serverMenu);

    await interaction.editReply({
      content: "Please select the server you're opening a ticket for:",
      embeds: [],
      components: [serverRow]
    });
  }

  if (interaction.customId && interaction.customId === "ticketUserCheckNo") {
    await interaction.deferUpdate();
    const row = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setStyle('Link')
        .setURL(config.LinkingSystem.linkingWebsite)
        .setLabel(config.Locale.TicketCreation.linkAccountButton)
    );
    await interaction.editReply({
      content: config.Locale.TicketCreation.linkingRequiredDescription,
      embeds: [],
      components: [row]
    });
  }

  if (interaction.customId && interaction.customId === `serverSelect_${interaction.user.id}`) {
    try {
      if (!Array.isArray(config.Servers) || config.Servers.length === 0) {
        console.log(`[WARNING] config.Servers is not properly defined in server selection handler.`);
        return interaction.update({
          content: "No server options are configured. Please contact an administrator.",
          components: []
        });
      }

      const selectedServerIndex = interaction.values[0].replace("server_", "");
      const selectedServer = config.Servers[selectedServerIndex]?.name || "Unknown Server";
      client.userSelection[interaction.user.id] = { server: selectedServer };

      const colorMap = {
        "Blurple": ButtonStyle.Primary,
        "Gray": ButtonStyle.Secondary,
        "Green": ButtonStyle.Success,
        "Red": ButtonStyle.Danger
      };

      console.log(`[DEBUG] Checking ticket buttons for select menu...`);
      for (let i = 1; i <= 8; i++) {
        const buttonConfig = config[`TicketButton${i}`];
        console.log(`[DEBUG] TicketButton${i}:`, buttonConfig);
        if (buttonConfig) {
          buttonConfig.ButtonColor = colorMap[buttonConfig.ButtonColor] || ButtonStyle.Secondary;
        }
      }

      const createButton = (id, buttonConfig) => {
        if (!buttonConfig || !buttonConfig.Enabled) {
          console.warn(`[WARNING] Button config is undefined or disabled for ${id}`);
          return new ButtonBuilder()
            .setCustomId(`invalid_${id}`)
            .setLabel("Invalid Button")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(true);
        }

        return new ButtonBuilder()
          .setCustomId(id)
          .setLabel(buttonConfig.TicketName || "Unnamed Button")
          .setStyle(buttonConfig.ButtonColor || ButtonStyle.Secondary);
      };

      if (config.TicketSystem.Settings.SelectMenu) {
        const options = [];
        for (let i = 1; i <= 8; i++) {
          const ticketButton = config[`TicketButton${i}`];
          if (!ticketButton || !ticketButton.Enabled) continue;

          const option = {
            label: ticketButton.TicketName || `Ticket Type ${i}`,
            value: `button${i}`,
            description: ticketButton.Description,
            emoji: ticketButton.ButtonEmoji
          };

          if (!option.emoji) delete option.emoji;
          if (!option.description) delete option.description;
          options.push(option);
        }

        if (options.length === 0) {
          return interaction.update({
            content: "No ticket types are configured. Please contact an administrator.",
            components: []
          });
        }

        const selectMenu = new StringSelectMenuBuilder()
          .setCustomId(`ticketSelect_${interaction.user.id}`)
          .setPlaceholder(config.Locale.TicketCreation.selectCategory)
          .setMinValues(1)
          .setMaxValues(1)
          .addOptions(options);

        const updatedMessage = await interaction.update({
          content: `✅ **Selected Server:** ${selectedServer}\nNow, select the type of ticket you want to open.\n\n*Make sure this is closed before you make a new ticket. You will need to redo the server selection per ticket.*`,
          components: [new ActionRowBuilder().addComponents(selectMenu)],
          embeds: []
        });

        setTimeout(async () => {
          try {
            await updatedMessage.delete();
          } catch (error) {
            console.error("❌ Failed to delete selection message:", error.message);
          }
        }, 15000);
      } else {
        const buttons = [];
        const validButtons = [];

        for (let i = 1; i <= 5; i++) {
          const buttonConfig = config[`TicketButton${i}`];
          if (buttonConfig && buttonConfig.Enabled) {
            validButtons.push({ index: i, config: buttonConfig });
          }
        }

        validButtons.forEach(item => {
          buttons.push(createButton(`button${item.index}`, item.config));
        });

        let buttonRow = new ActionRowBuilder();
        if (buttons.length > 0) {
          buttons.forEach(button => buttonRow.addComponents(button));
        } else {
          buttonRow.addComponents(
            new ButtonBuilder()
              .setCustomId('no_tickets')
              .setLabel('No ticket types available')
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(true)
          );
        }

        await interaction.update({
          content: `✅ **Selected Server:** ${selectedServer}\nNow, select the type of ticket you want to open.\n\n*Make sure this is closed before you make a new ticket. You will need to redo the server selection per ticket.*`,
          components: [buttonRow]
        });
      }
    } catch (error) {
      console.error(`[ERROR] Error in server selection handler:`, error);
      return interaction.update({
        content: "An error occurred while processing your selection. Please try again or contact an administrator.",
        components: []
      });
    }
  }

  if (interaction.customId && interaction.customId === 'closeTicket') {
    let supportRole = await utils.checkIfUserHasSupportRoles(interaction);
    if (config.TicketSystem.Settings.RestrictTicketClose && !supportRole) {
      return interaction.reply({ content: config.Locale.TicketManagement.restrictTicketClose, ephemeral: true });
    }

    await ticketModel.updateOne(
      { channelID: interaction.channel.id },
      { $set: { closeUserID: interaction.user.id, closedAt: Date.now() } }
    );

    await client.emit('ticketClose', interaction);
  }

  if (interaction.customId && interaction.customId === 'ticketclaim') {
    await interaction.deferReply({ ephemeral: true });

    let ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
    let logMsg = `\n\n[${new Date().toLocaleString()}] [TICKET CLAIM] User: ${interaction.user.username}`;
    fs.appendFile("./logs.ecommerify", logMsg, (e) => {
      if (e) console.log(e);
    });

    if (!config.TicketSystem.ClaimingSystem.Enabled) {
      return interaction.editReply({ content: "Ticket claiming is disabled in the config!", ephemeral: true });
    }

    let supportRole = await utils.checkIfUserHasSupportRoles(interaction);
    if (!supportRole) {
      return interaction.editReply({ content: config.Locale.TicketClaiming.restrictTicketClaim, ephemeral: true });
    }

    let embedClaimVar = config.Locale.TicketClaiming.ticketClaimed.replace(/{user}/g, `<@!${interaction.user.id}>`);
    const embed = new EmbedBuilder()
      .setTitle(config.Locale.TicketClaiming.ticketClaimedTitle)
      .setColor("Green")
      .setDescription(embedClaimVar)
      .setTimestamp()
      .setFooter({ text: `${config.Locale.TicketClaiming.ticketClaimedBy} ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) });

    interaction.editReply({ content: config.Locale.TicketClaiming.claimTicketMsg, ephemeral: false });
    interaction.channel.send({ embeds: [embed], ephemeral: false });

    interaction.channel.messages.fetch(ticketDB.msgID).then(async msg => {
      const embed = msg.embeds[0];
      embed.fields[0] = { name: config.Locale.TicketClaiming.ticketClaimedBy, value: `> <@!${interaction.user.id}> (${interaction.user.username})` };

      const ticketDeleteButton = new ButtonBuilder()
        .setCustomId('closeTicket')
        .setLabel(config.Locale.TicketManagement.CloseTicketButton)
        .setStyle(config.ButtonCustomization.Colors.closeTicket);

      const ticketClaimButton = new ButtonBuilder()
        .setCustomId('ticketclaim')
        .setLabel(config.Locale.TicketClaiming.claimTicketButton)
        .setStyle(config.ButtonCustomization.Colors.ticketClaim)
        .setDisabled(true);

      const ticketUnClaimButton = new ButtonBuilder()
        .setCustomId('ticketunclaim')
        .setLabel(config.Locale.TicketClaiming.unclaimTicketButton)
        .setStyle(config.ButtonCustomization.Colors.ticketUnclaim);

      let row2 = new ActionRowBuilder().addComponents(ticketDeleteButton, ticketClaimButton, ticketUnClaimButton);

      msg.edit({ embeds: [embed], components: [row2] });
      client.emit('ticketClaim', interaction);

      // Notify Staff Manager about the ticket claim
      try {
        const StaffManagerIntegration = require('../utils/staffManagerIntegration');
        const staffIntegration = new StaffManagerIntegration(client);

        await staffIntegration.notifyTicketClaim(interaction.user.id, {
          channelID: interaction.channel.id,
          ticketType: ticketDB.ticketType,
          userID: ticketDB.userID,
          username: ticketDB.username
        });

        console.log(`Notified Staff Manager of ticket claim by ${interaction.user.tag}`);
      } catch (error) {
        console.error('Error notifying Staff Manager of ticket claim:', error);
      }

      const editPermissionOverwrites = async (interaction, supportRoles) => {
        await Promise.all(supportRoles.map(async sRoles => {
          const role = interaction.guild.roles.cache.get(sRoles);
          if (role) {
            await interaction.channel.permissionOverwrites.edit(role, {
              SendMessages: config.TicketSystem.ClaimingSystem.UserPerms.SendMessages,
              ViewChannel: config.TicketSystem.ClaimingSystem.UserPerms.ViewChannel
            });
          }
        }));
      };

      let tButton = ticketDB.button;
      switch (tButton) {
        case "TicketButton1":
          await editPermissionOverwrites(interaction, config.TicketButton1.SupportRoles);
          break;
        case "TicketButton2":
          await editPermissionOverwrites(interaction, config.TicketButton2.SupportRoles);
          break;
        case "TicketButton3":
          await editPermissionOverwrites(interaction, config.TicketButton3.SupportRoles);
          break;
        case "TicketButton4":
          await editPermissionOverwrites(interaction, config.TicketButton4.SupportRoles);
          break;
        case "TicketButton5":
          await editPermissionOverwrites(interaction, config.TicketButton5.SupportRoles);
          break;
        case "TicketButton6":
          await editPermissionOverwrites(interaction, config.TicketButton6.SupportRoles);
          break;
        case "TicketButton7":
          await editPermissionOverwrites(interaction, config.TicketButton7.SupportRoles);
          break;
        case "TicketButton8":
          await editPermissionOverwrites(interaction, config.TicketButton8.SupportRoles);
          break;
      }

      await interaction.channel.permissionOverwrites.edit(interaction.user, {
        SendMessages: true,
        ViewChannel: true,
        AttachFiles: true,
        EmbedLinks: true,
        ReadMessageHistory: true
      });

      await ticketModel.updateOne(
        { channelID: interaction.channel.id },
        { $set: { claimed: true, claimUser: interaction.user.id } }
      );

      const log = new EmbedBuilder()
        .setColor("Green")
        .setTitle(config.Locale.Logs.ticketClaimedLog)
        .addFields([
          { name: `• ${config.Locale.Logs.logsExecutor}`, value: `> <@!${interaction.user.id}>\n> ${interaction.user.username}` },
          { name: `• ${config.Locale.Logs.logsTicket}`, value: `> <#${interaction.channel.id}>\n> #${interaction.channel.name}\n> ${ticketDB.ticketType}` }
        ])
        .setTimestamp()
        .setThumbnail(interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }))
        .setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) });

      if (config.TicketSystem.Logs.claimTicket.Enabled) logsHook.send({ username: 'Ticket Claimed', embeds: [log] });
    });
  }

  if (interaction.customId && interaction.customId === 'ticketunclaim') {
    await interaction.deferReply({ ephemeral: true });

    let ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
    let logMsg = `\n\n[${new Date().toLocaleString()}] [TICKET UNCLAIM] User: ${interaction.user.username}`;
    fs.appendFile("./logs.ecommerify", logMsg, (e) => {
      if (e) console.log(e);
    });

    if (!config.TicketSystem.ClaimingSystem.Enabled) {
      return interaction.editReply({ content: "Ticket claiming is disabled in the config!", ephemeral: true });
    }

    if (!ticketDB.claimed) {
      return interaction.editReply({ content: config.Locale.TicketClaiming.ticketNotClaimed, ephemeral: true });
    }

    let msgClaimUserVar = config.Locale.TicketClaiming.ticketDidntClaim.replace(/{user}/g, `<@!${ticketDB.claimUser}>`);
    if (ticketDB.claimUser !== interaction.user.id) {
      return interaction.editReply({ content: msgClaimUserVar, ephemeral: true });
    }

    let supportRole = await utils.checkIfUserHasSupportRoles(interaction);
    if (!supportRole) {
      return interaction.editReply({ content: config.Locale.TicketClaiming.restrictTicketClaim, ephemeral: true });
    }

    let tButton = ticketDB.button;
    const applyPermissionOverwrites = async (interaction, supportRoles) => {
      await Promise.all(supportRoles.map(async sRoles => {
        const role = interaction.guild.roles.cache.get(sRoles);
        if (role) {
          await interaction.channel.permissionOverwrites.edit(role, {
            SendMessages: true,
            ViewChannel: true
          });
        }
      }));
    };

    switch (tButton) {
      case "TicketButton1":
        await applyPermissionOverwrites(interaction, config.TicketButton1.SupportRoles);
        break;
      case "TicketButton2":
        await applyPermissionOverwrites(interaction, config.TicketButton2.SupportRoles);
        break;
      case "TicketButton3":
        await applyPermissionOverwrites(interaction, config.TicketButton3.SupportRoles);
        break;
      case "TicketButton4":
        await applyPermissionOverwrites(interaction, config.TicketButton4.SupportRoles);
        break;
      case "TicketButton5":
        await applyPermissionOverwrites(interaction, config.TicketButton5.SupportRoles);
        break;
      case "TicketButton6":
        await applyPermissionOverwrites(interaction, config.TicketButton6.SupportRoles);
        break;
      case "TicketButton7":
        await applyPermissionOverwrites(interaction, config.TicketButton7.SupportRoles);
        break;
      case "TicketButton8":
        await applyPermissionOverwrites(interaction, config.TicketButton8.SupportRoles);
        break;
    }

    let embedClaimVar2 = config.Locale.TicketClaiming.ticketUnClaimed.replace(/{user}/g, `<@!${interaction.user.id}>`);
    const embed = new EmbedBuilder()
      .setTitle(config.Locale.TicketClaiming.ticketUnClaimedTitle)
      .setColor("Red")
      .setDescription(embedClaimVar2)
      .setTimestamp()
      .setFooter({ text: `${config.Locale.TicketClaiming.ticketUnClaimedBy} ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) });

    interaction.editReply({ content: config.Locale.TicketClaiming.unclaimTicketMsg, ephemeral: true });
    interaction.channel.send({ embeds: [embed] });

    interaction.channel.messages.fetch(ticketDB.msgID).then(async msg => {
      const embed = msg.embeds[0];
      embed.fields[0] = { name: config.Locale.TicketClaiming.ticketClaimedBy, value: `> ${config.Locale.TicketClaiming.ticketNotClaimed}` };

      const ticketDeleteButton = new ButtonBuilder()
        .setCustomId('closeTicket')
        .setLabel(config.Locale.TicketManagement.CloseTicketButton)
        .setStyle(config.ButtonCustomization.Colors.closeTicket);

      const ticketClaimButton = new ButtonBuilder()
        .setCustomId('ticketclaim')
        .setLabel(config.Locale.TicketClaiming.claimTicketButton)
        .setStyle(config.ButtonCustomization.Colors.ticketClaim);

      let row3 = new ActionRowBuilder().addComponents(ticketDeleteButton, ticketClaimButton);

      msg.edit({ embeds: [embed], components: [row3] });

      await ticketModel.updateOne(
        { channelID: interaction.channel.id },
        { $set: { claimed: false, claimUser: "" } }
      );

      const log = new EmbedBuilder()
        .setColor("Red")
        .setTitle(config.Locale.Logs.ticketUnClaimedLog)
        .addFields([
          { name: `• ${config.Locale.Logs.logsExecutor}`, value: `> <@!${interaction.user.id}>\n> ${interaction.user.username}` },
          { name: `• ${config.Locale.Logs.logsTicket}`, value: `> <#${interaction.channel.id}>\n> #${interaction.channel.name}\n> ${ticketDB.ticketType}` }
        ])
        .setTimestamp()
        .setThumbnail(interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }))
        .setFooter({ text: interaction.user.username, iconURL: interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }) });

      if (config.TicketSystem.Logs.unclaimTicket.Enabled) logsHook.send({ username: 'Ticket Unclaimed', embeds: [log] });
    });
  }

  if (interaction.customId === 'deleteTicket') {
    let supportRole = await utils.checkIfUserHasSupportRoles(interaction);
    if (!supportRole) {
      return interaction.reply({ content: config.Locale.TicketTranscript.notAllowedDelete, ephemeral: true });
    }

    let ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });

    if (ticketDB.archiveMsgID) {
      interaction.channel.messages.fetch(ticketDB.archiveMsgID).then(msg => {
        msg.delete().catch(() => {});
      });
    }

    if (!config.TicketSystem.ArchiveSystem.Enabled) {
      await ticketModel.updateOne(
        { channelID: interaction.channel.id },
        { $set: { closeUserID: interaction.user.id, closedAt: Date.now() } }
      );
    }
    await client.emit('ticketClose', interaction);
  }

  if (interaction.customId === 'reOpen') {
    await interaction.deferReply();

    let ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
    let tButton = ticketDB.button;
    let ticketAuthor = client.users.cache.get(ticketDB.userID);
    let ticketChannel = interaction.guild.channels.cache.get(interaction.channel.id);
    let tButtonConfig = config[tButton];

    if (tButtonConfig && tButtonConfig.ClosedCategoryID && ticketChannel.parentId !== tButtonConfig.TicketCategoryID) {
      await ticketChannel.setParent(tButtonConfig.TicketCategoryID, { lockPermissions: false });
    }

    await tButtonConfig.SupportRoles.forEach(async sRoles => {
      let role = interaction.guild.roles.cache.get(sRoles);
      if (role) {
        await interaction.channel.permissionOverwrites.create(role.id, {
          ViewChannel: true,
          SendMessages: true,
          AttachFiles: true,
          EmbedLinks: true,
          ReadMessageHistory: true
        });
      }
    });

    let tChannelName = tButtonConfig.ChannelName
      .replace(/{username}/g, ticketAuthor.username)
      .replace(/{total-tickets}/g, statsDB ? statsDB.totalTickets : 0)
      .replace(/{user-id}/g, ticketAuthor.id);

    if (config.TicketSystem.ArchiveSystem.RenameClosedTicket) {
      interaction.channel.setName(tChannelName);
    }

    let claimUser = await client.users.cache.get(ticketDB.claimUser);

    await interaction.channel.permissionOverwrites.create(ticketAuthor.id, {
      ViewChannel: true,
      SendMessages: true,
      AttachFiles: true,
      EmbedLinks: true,
      ReadMessageHistory: true
    });

    if (claimUser && config.TicketSystem.ClaimingSystem.Enabled) {
      await interaction.channel.permissionOverwrites.create(claimUser.id, {
        ViewChannel: true,
        SendMessages: true,
        AttachFiles: true,
        EmbedLinks: true,
        ReadMessageHistory: true
      });
    }

    if (ticketDB.archiveMsgID) {
      await interaction.channel.messages.fetch(ticketDB.archiveMsgID).then(msg => {
        msg.delete().catch(() => {});
      });
    }

    await ticketModel.updateOne(
      { channelID: interaction.channel.id },
      { $set: { status: "Open" } }
    );

    const embed = new EmbedBuilder()
      .setColor("Green")
      .setDescription(config.Locale.TicketManagement.ticketReOpenedBy
        .replace(/{user}/g, `<@!${interaction.user.id}>`)
        .replace(/{username}/g, interaction.user.username)
      );

    await interaction.followUp({ embeds: [embed] });
  }

  if (interaction.customId === 'createTranscript') {
    let supportRole = await utils.checkIfUserHasSupportRoles(interaction);
    if (!supportRole) {
      return interaction.reply({ content: config.Locale.TicketTranscript.notAllowedTranscript, ephemeral: true });
    }

    let ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
    let ticketAuthor = client.users.cache.get(ticketDB.userID);
    const { attachment } = await utils.saveTranscript(interaction);

    let transcriptSavedByLocale = config.Locale.TicketTranscript.transcriptSavedBy.replace(/{user}/g, `<@!${interaction.user.id}>`);
    const embed = new EmbedBuilder()
      .setColor(config.BotSettings.EmbedColors)
      .setTitle(config.Locale.TicketTranscript.ticketTranscript)
      .setDescription(transcriptSavedByLocale)
      .addFields([
        { name: config.Locale.Logs.logsTicketAuthor, value: `<@!${ticketAuthor.id}>\n${ticketAuthor.username}`, inline: true },
        { name: config.Locale.TicketTranscript.ticketName, value: `<#${interaction.channel.id}>\n${interaction.channel.name}`, inline: true },
        { name: config.Locale.TicketTranscript.ticketTranscriptCategory, value: ticketDB.ticketType, inline: true }
      ])
      .setFooter({ text: ticketAuthor.username, iconURL: ticketAuthor.displayAvatarURL({ dynamic: true }) })
      .setTimestamp();

    let transcriptChannel = interaction.guild.channels.cache.get(config.TicketSystem.ArchiveSystem.TranscriptChannelID);
    if (!transcriptChannel) {
      return interaction.reply({ content: "Transcript channel has not been setup in the config!", ephemeral: true });
    }

    // Always send transcript to logs channel
    logsHook.send({
      username: 'Transcription Created',
      embeds: [embed],
      files: [attachment]
    }).then(() => {
      console.log(`Transcript sent to logs channel for ticket ${interaction.channel.name}`);
    }).catch(error => {
      console.error('Failed to send transcript to logs channel:', error);
    });

    let transcriptSavedLocale = config.Locale.TicketTranscript.transcriptSaved.replace(/{channel}/g, `<#${transcriptChannel.id}>`);
    interaction.reply({ content: transcriptSavedLocale, ephemeral: true });
  }

  if (interaction.customId === "acceptPolicy") {
    const modal = new ModalBuilder()
      .setCustomId("policy_modal")
      .setTitle("Policy Agreement");

    const nameInput = new TextInputBuilder()
      .setCustomId("policy_name")
      .setLabel("Enter Your Name")
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('Please use your FULL name')
      .setRequired(true);

    const agreementInput = new TextInputBuilder()
      .setCustomId("policy_confirmation")
      .setLabel("Type 'I Agree' to Accept")
      .setPlaceholder('Type "I AGREE" to accept!')
      .setStyle(TextInputStyle.Short)
      .setRequired(true);

    const firstRow = new ActionRowBuilder().addComponents(nameInput);
    const secondRow = new ActionRowBuilder().addComponents(agreementInput);

    modal.addComponents(firstRow, secondRow);

    try {
      await interaction.showModal(modal);
    } catch (error) {
      console.error("❌ Error opening modal:", error);
    }
  }

  if (interaction.isModalSubmit() && interaction.customId === "policy_modal") {
    const userName = interaction.fields.getTextInputValue("policy_name");
    const confirmation = interaction.fields.getTextInputValue("policy_confirmation");

    if (confirmation.toUpperCase() !== "I AGREE") {
      return interaction.reply({ content: "**You must type 'I AGREE' exactly to confirm.**", ephemeral: true });
    }

    const existingEntry = await PolicyAgreement.findOne({ userID: interaction.user.id });
    if (existingEntry) {
      return interaction.reply({ content: "**You have already agreed to the policy.**", ephemeral: true });
    }

    const roleID = "1339665055045779516";
    const member = interaction.guild.members.cache.get(interaction.user.id);
    if (!member) {
      return interaction.reply({ content: "❌ **Could not find your user data. Try again.**", ephemeral: true });
    }

    await member.roles.add(roleID);

    await PolicyAgreement.create({
      userID: interaction.user.id,
      userName,
      timestamp: new Date()
    });

    const embed = new EmbedBuilder()
      .setTitle("Policy Accepted")
      .setColor("Green")
      .setDescription(`**${userName}** (${interaction.user.tag}) has accepted the policy.`)
      .setTimestamp();

    await interaction.reply({ content: "**Your policy agreement has been recorded!**", ephemeral: true });

    const logChannel = interaction.client.channels.cache.get("1339659813537316886");
    if (logChannel) await logChannel.send({ embeds: [embed] });
  }

  if (interaction.isStringSelectMenu() && interaction.customId === "bug_type") {
    const bugType = interaction.values[0];

    const modal = new ModalBuilder()
      .setCustomId(`bug_report_modal_${bugType}`)
      .setTitle("Bug Report");

    const descriptionInput = new TextInputBuilder()
      .setCustomId("bug_description")
      .setLabel("Describe the issue:")
      .setStyle(TextInputStyle.Paragraph)
      .setRequired(true);

    const evidenceInput = new TextInputBuilder()
      .setCustomId("bug_evidence")
      .setLabel("Provide any evidence:")
      .setStyle(TextInputStyle.Short)
      .setRequired(false);

    modal.addComponents(
      new ActionRowBuilder().addComponents(descriptionInput),
      new ActionRowBuilder().addComponents(evidenceInput)
    );

    await interaction.showModal(modal);
  }

  if (interaction.isModalSubmit() && interaction.customId.startsWith("bug_report_modal_")) {
    const bugType = interaction.customId.replace("bug_report_modal_", "");
    const description = interaction.fields.getTextInputValue("bug_description");
    const evidence = interaction.fields.getTextInputValue("bug_evidence") || "No evidence provided";

    const bugEmbed = new EmbedBuilder()
      .setTitle("Bug Report")
      .setColor(bugType === "ingame_bug" ? "Red" : "Blue")
      .setDescription(`**Reported By:** ${interaction.user.tag} (${interaction.user.id})\n**Bug Type:** ${bugType === "ingame_bug" ? "In Game Plugin / Glitch" : "Discord Bot Glitch"}`)
      .addFields(
        { name: "Description", value: description },
        { name: "Evidence", value: evidence }
      )
      .setTimestamp();

    if (bugReportLogs) await bugReportLogs.send({ embeds: [bugEmbed] });

    const devID = bugType === "ingame_bug" ? config.DeveloperIDs.inGameDevID : config.DeveloperIDs.discordBotDevID;
    const devUser = await interaction.client.users.fetch(devID).catch(() => null);
    if (devUser) await devUser.send({ embeds: [bugEmbed] });

    await interaction.reply({
      content: "✅ Your bug report has been submitted successfully!",
      ephemeral: true
    });
  }

  if (interaction.customId && (interaction.customId.startsWith("accept-blacklist_") || interaction.customId.startsWith("decline-blacklist_"))) {
    const userId = interaction.customId.replace(/\D/g, "");
    const allowedRoleID = "1328131107623014563";
    if (!interaction.member.roles.cache.has(allowedRoleID)) {
      return interaction.reply({
        content: "❌ **You do not have permission to approve or deny blacklist requests.**",
        ephemeral: true
      });
    }

    const request = await BlacklistRequest.findOne({ userID: userId, status: "pending" });
    if (!request) {
      return interaction.reply({ content: "⚠️ **This blacklist request is no longer valid.**", ephemeral: true });
    }

    if (interaction.customId.startsWith("accept-blacklist_")) {
      const existingBlacklist = await Blacklist.findOne({ userID: userId });
      if (existingBlacklist) {
        return interaction.reply({ content: `⚠️ **User is already blacklisted** for: ${existingBlacklist.reason}`, ephemeral: true });
      }

      await Blacklist.create({ userID: userId, reason: request.reason, userWhoBlacklisted: interaction.user.id, alreadyLogged: false });

      for (const [guildID] of Object.entries(config.GuildSettings.guildBlacklistMapping)) {
        const guild = interaction.client.guilds.cache.get(guildID);
        if (!guild) continue;

        const member = await guild.members.fetch(userId).catch(() => null);
        if (member) {
          await member.ban({ reason: `Blacklisted: ${request.reason}` }).catch(() => null);
        }
      }

      await BlacklistRequest.updateOne({ userID: userId }, { $set: { status: "accepted" } });

      const embed = new EmbedBuilder()
        .setTitle("User Blacklisted")
        .setColor("Red")
        .setDescription(`**User ID:** ${userId} has been blacklisted by <@${interaction.user.id}>.`)
        .setTimestamp();

      await sendToAllWebhooks(embed);
      await interaction.reply({ content: `**User has been blacklisted.**`, components: [] });
    } else {
      await BlacklistRequest.updateOne({ userID: userId }, { $set: { status: "declined" } });
      await interaction.update({ content: `❌ **Blacklist request denied.**`, components: [] });

      const embed = new EmbedBuilder()
        .setTitle("Blacklist Request Denied")
        .setColor("Gray")
        .setDescription(`**User ID:** ${userId}\n**Denied by:** <@${interaction.user.id}>`)
        .setTimestamp();

      await sendToAllWebhooks(embed);
    }
  }
};