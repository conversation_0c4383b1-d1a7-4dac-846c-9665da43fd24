if (process.platform !== "win32") require("child_process").exec("npm install");

const color = require('ansi-colors');
const axios = require('axios');
console.log(`${color.yellow(`Starting bot, this can take a while..`)}`);

// Import Fastify properly
const fs = require("fs-extra");
const path = require("path");

// Import memory logger
const MemoryLogger = require('./utils/memoryLogger');

// Memory monitoring functionality has been removed

const fastify = require('fastify')();
const fastifyFormbody = require('@fastify/formbody');
const fastifyMultipart = require('@fastify/multipart');
const fastifyStatic = require('@fastify/static');
const fastifyCors = require('@fastify/cors')

const version = Number(process.version.split('.')[0].replace('v', ''));
if (version < 18) {
  console.log(`${color.red(`[ERROR] Ecommerify Tickets requires a NodeJS version of 18 or higher!\nYou can check your NodeJS by running the "node -v" command in your terminal.`)}`);

  // Add update instructions
  console.log(`${color.blue(`\n[INFO] To update Node.js, follow the instructions below for your operating system:`)}`);
  console.log(`${color.magentaBright(`- Windows:`)} Download and run the installer from ${color.cyan(`https://nodejs.org/`)}`);
  console.log(`${color.magentaBright(`- Ubuntu/Debian:`)} Run the following commands in the Terminal:`);
  console.log(`${color.cyan(`  - sudo apt update`)}`);
  console.log(`${color.cyan(`  - sudo apt upgrade nodejs`)}`);
  console.log(`${color.magentaBright(`- CentOS:`)} Run the following commands in the Terminal:`);
  console.log(`${color.cyan(`  - sudo yum update`)}`);
  console.log(`${color.cyan(`  - sudo yum install -y nodejs`)}`);

  let logMsg = `\n\n[${new Date().toLocaleString()}] [ERROR] Ecommerify Tickets requires a NodeJS version of 18 or higher!`;
  fs.appendFile("./logs.ecommerify", logMsg, (e) => {
    if(e) console.log(e);
  });

  process.exit()
}
process.title = `Manager | v1.0.0`;
console.clear();

const packageFile = require('./package.json');
let logMsg = `\n\n[${new Date().toLocaleString()}] [STARTING] Attempting to start the bot..\nNodeJS Version: ${process.version}\nBot Version: ${packageFile.version}`;
fs.appendFile("./logs.ecommerify", logMsg, (e) => {
  if(e) console.log(e);
});

const { Collection, Client, Events, ButtonBuilder, GatewayIntentBits, WebhookClient, EmbedBuilder, ButtonStyle, ActionRowBuilder, Partials } = require('discord.js');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('./utils/configAdapter')
const Users = require('./models/Users')
// Blacklist functionality has been removed
const updateEmbed = require('./utils/updateEmbed')
// Ban monitor functionality has been removed
// Task manager functionality has been removed
const client = new Client({
  restRequestTimeout: 60000,
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildEmojisAndStickers,
    GatewayIntentBits.GuildIntegrations,
    GatewayIntentBits.GuildInvites,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.GuildMessageReactions,
    GatewayIntentBits.GuildMessageTyping,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildPresences,
    GatewayIntentBits.GuildScheduledEvents,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.GuildWebhooks,
    GatewayIntentBits.DirectMessages,
    GatewayIntentBits.DirectMessageTyping,
    GatewayIntentBits.DirectMessageReactions,
    GatewayIntentBits.MessageContent,
  ],
  partials: [
    Partials.Channel,
    Partials.GuildMember,
    Partials.GuildScheduledEvent,
    Partials.Reaction,
    Partials.ThreadMember,
    Partials.User,
  ],
});
const logHook = config.ErrorLogsHook ? new WebhookClient({ url: config.ErrorLogsHook }) : null;
const cdnHook = config.cdnLogsHook ? new WebhookClient({ url: config.cdnLogsHook }) : null;
// Blacklist check functionality has been removed
let roleCheckInterval;

if (config.RoleCheck === true) {
    // Use a 1-minute interval as per user request
    roleCheckInterval = setInterval(() => {
        try {
            console.log('Running automatic role check...');
            checkUsersForDiscordRole(client);
        } catch (error) {
            // Limit error message size to prevent memory leaks
            const errorMsg = error.message?.substring(0, 200) || 'Unknown error';
            console.error('Error in role check interval:', errorMsg);

            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
        }
    }, 60000); // Check every 1 minute (changed from 30 minutes per user request)
}

// Function to check and assign roles
async function checkUsersForDiscordRole(client) {
    try {
        // Only proceed if RoleCheck is enabled
        if (!config.RoleCheck) {
            console.log('Role check is disabled in config');
            return;
        }

        const guildId = "1253023946006204558"; // Hardcoded guild ID
        const roleId = "1342714797417959497"; // Hardcoded role ID

        // Fetch the guild
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            console.log(`Guild with ID ${guildId} not found. Skipping role check.`);
            return;
        }

        // Ensure lastChecked is initialized
        if (!lastChecked) lastChecked = new Date(0);

        // Process only users in batches to reduce memory usage
        const totalUsers = await Users.countDocuments();

        if (totalUsers === 0) {
            console.log('No users found in database for role check.');
            return;
        }

        // Process only users added in the last day to reduce memory usage
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const recentUsers = await Users.find({
            timestamp: { $gt: oneDayAgo }
        }).limit(50); // Limit to 50 users at a time

        // Fallback to processing a small batch if no recent users
        const usersToProcess = recentUsers.length > 0 ? recentUsers : await Users.find().limit(50);

        // Process each user
        for (const user of usersToProcess) {
            try {
                const member = await guild.members.fetch(user.discord_id).catch(() => null);
                if (!member) {
                    console.log(`User ${user.discord_id} not found in guild ${guildId}.`);
                    continue;
                }

                if (!member.roles.cache.has(roleId)) {
                    await member.roles.add(roleId);
                    console.log(`Assigned role ${roleId} to user ${user.discord_id} in guild ${guildId}.`);
                }
            } catch (error) {
                // Limit error message size to prevent memory leaks
                const errorMsg = error.message?.substring(0, 200) || 'Unknown error';
                console.log(`Error processing user ${user.discord_id}: ${errorMsg}`);
            }
        }

        console.log('Role Check Completed');

        // Update lastChecked to now
        lastChecked = new Date();
    } catch (error) {
        // Limit error message size to prevent memory leaks
        const errorMsg = error.message?.substring(0, 200) || 'Unknown error';
        console.log('Error in checkUsersForDiscordRole: ' + errorMsg);
    }
}

// Optional: Function to manually start/stop the interval
function toggleRoleCheck(enable) {
    if (enable && !roleCheckInterval) {
        roleCheckInterval = setInterval(() => {
            checkUsersForDiscordRole(client);
        }, 60000); // 1 minute (changed from 15 minutes per user request)
    } else if (!enable && roleCheckInterval) {
        clearInterval(roleCheckInterval);
        roleCheckInterval = null;
    }
}

module.exports = client

// Initialize status update
updateEmbed(client, config.statusChannelID)

// Store interval reference for cleanup
let statusUpdateInterval;

// Update less frequently to reduce memory usage
statusUpdateInterval = setInterval(() => {
    try {
        updateEmbed(client, config.statusChannelID);
    } catch (error) {
        // Limit error message size to prevent memory leaks
        const errorMsg = error.message?.substring(0, 200) || 'Unknown error';
        console.error('Error updating status embed:', errorMsg);

        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }
    }
}, 300000); // Every 5 minutes
require("./utils.js");

const utils = require("./utils.js");
const createTranscriptFolder = async () => {
  let dashboardExists = await utils.checkDashboard();
  if(config.TicketTranscriptSettings.SaveInFolder && !dashboardExists && !fs.existsSync('./transcripts')) fs.mkdirSync('./transcripts');
  if(dashboardExists && !fs.existsSync('./src/Dashboard/transcripts')) fs.mkdirSync('./src/Dashboard/transcripts');
};
createTranscriptFolder()
const filePath = './logs.manager';
const maxLength = 300; // Maximum lines of content

async function handleError(errorType, error) {
  console.log(error);

  const errorPrefix = `[${new Date().toLocaleString()}] [${errorType}] [v${packageFile.version}]`;
  const errorMsg = `\n\n${errorPrefix}\n${error.stack}`;
  fs.appendFile("./logs.manager", errorMsg, (e) => {
    if (e) console.log(e);
  });

  fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
      console.error('Error reading file:', err.message);
      return;
    }
    console.log(`[${errorType}] | ${error}`)

    if (logHook) {
      const logEmbed = new EmbedBuilder()
        .setTitle(`Error Logs - ${errorType}`)
        .setDescription(`\`\`\`${error}\n\n${error.stack}\`\`\``)
        .setColor(`#CCCCCC`)
        .setFooter({ text: `Logs` })

      logHook.send({ embeds: [logEmbed] }).catch(err => console.error(`Error sending to webhook: ${err.message}`));
    }
  });
}

client.on('warn', async (error) => {
  handleError('WARN', error);
});

client.on('error', async (error) => {
  handleError('ERROR', error);

  // Run garbage collection if available
  if (global.gc) {
    console.log(`${color.yellow(`[ERROR] Running garbage collection after client error...`)}`);
    global.gc();
  }

  // Check if it's a memory-related error
  if (error && error.message && (
    error.message.includes('heap') ||
    error.message.includes('memory') ||
    error.code === 'ERR_WORKER_OUT_OF_MEMORY'
  )) {
    console.log(`${color.yellow(`[ERROR] Memory-related client error detected. Running garbage collection...`)}`);
    // Just run garbage collection without restarting
    if (global.gc) {
      global.gc();
      // Run it again after a short delay
      setTimeout(() => {
        if (global.gc) global.gc();
      }, 1000);
    }
  }
});

process.on('unhandledRejection', async (error) => {
  handleError('unhandledRejection', error);

  // Run garbage collection if available
  if (global.gc) {
    console.log(`${color.yellow(`[ERROR] Running garbage collection after unhandled rejection...`)}`);
    global.gc();
  }

  // Check if it's a memory-related error
  if (error && error.message && (
    error.message.includes('heap') ||
    error.message.includes('memory') ||
    error.code === 'ERR_WORKER_OUT_OF_MEMORY'
  )) {
    console.log(`${color.yellow(`[ERROR] Memory-related error detected. Running garbage collection...`)}`);
    // Just run garbage collection without restarting
    if (global.gc) {
      global.gc();
      // Run it again after a short delay
      setTimeout(() => {
        if (global.gc) global.gc();
      }, 1000);
    }
  }
});

// Handle graceful shutdown
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Handle out of memory errors
process.on('--inspect-brk', () => {
  console.log(`${color.red(`[MEMORY] Detected memory debugging flag. Memory issues may be present.`)}`);
});

// Add specific handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  handleError('Uncaught Exception', error);

  // Run garbage collection if available
  if (global.gc) {
    console.log(`${color.yellow(`[ERROR] Running garbage collection after uncaught exception...`)}`);
    global.gc();
  }

  // Check if it's a memory-related error
  if (error && error.message && (
    error.message.includes('heap') ||
    error.message.includes('memory') ||
    error.code === 'ERR_WORKER_OUT_OF_MEMORY'
  )) {
    console.log(`${color.yellow(`[ERROR] Memory-related error detected. Running garbage collection...`)}`);
    // Just run garbage collection without restarting
    if (global.gc) {
      global.gc();
      // Run it again after a short delay
      setTimeout(() => {
        if (global.gc) global.gc();
      }, 1000);
    }
  }
});

// Store references to monitors for cleanup
let systemMonitorRef = null;

/**
 * Perform a graceful shutdown of the bot
 */
async function gracefulShutdown() {
  console.log(`${color.yellow(`[SHUTDOWN] Graceful shutdown initiated...`)}`);

  // Stop memory logger
  if (client.memoryLogger) {
    client.memoryLogger.stop();
    console.log(`${color.yellow(`[SHUTDOWN] Memory logger stopped.`)}`);
  }

  // Clear intervals
  if (roleCheckInterval) {
    clearInterval(roleCheckInterval);
    console.log(`${color.yellow(`[SHUTDOWN] Role check interval cleared.`)}`);
  }

  // Clear status update interval
  if (statusUpdateInterval) {
    clearInterval(statusUpdateInterval);
    console.log(`${color.yellow(`[SHUTDOWN] Status update interval cleared.`)}`);
  }

  // Clear any pending timeouts
  if (client.pendingTimeouts && client.pendingTimeouts.length > 0) {
    let clearedTimeouts = 0;
    for (const timeoutId of client.pendingTimeouts) {
      clearTimeout(timeoutId);
      clearedTimeouts++;
    }
    console.log(`${color.yellow(`[SHUTDOWN] Cleared ${clearedTimeouts} pending timeouts.`)}`);
    client.pendingTimeouts = [];
  }

  // Clear garbage collection interval
  if (client.gcInterval) {
    clearInterval(client.gcInterval);
    console.log(`${color.yellow(`[SHUTDOWN] Garbage collection interval cleared.`)}`);
  }

  // Run final garbage collection
  if (global.gc) {
    console.log(`${color.yellow(`[SHUTDOWN] Running final garbage collection...`)}`);
    global.gc();
  }

  // Log shutdown
  const logMsg = `\n\n[${new Date().toLocaleString()}] [SHUTDOWN] Bot is shutting down gracefully.`;
  fs.appendFileSync("./logs.ecommerify", logMsg);

  try {
    // Disconnect from Discord
    if (client && client.isReady()) {
      console.log(`${color.yellow(`[SHUTDOWN] Disconnecting from Discord...`)}`);
      await client.destroy();
      console.log(`${color.green(`[SHUTDOWN] Successfully disconnected from Discord.`)}`);
    }

    // Close any database connections here if needed
    console.log(`${color.green(`[SHUTDOWN] Graceful shutdown complete. Exiting...`)}`);
    process.exit(0);
  } catch (error) {
    console.error(`${color.red(`[SHUTDOWN] Error during shutdown: ${error.message}`)}`);
    process.exit(1);
  }
}

// Initialize task manager when client is ready
client.once(Events.ClientReady, () => {
  console.log(`${color.green(`[BOT] Task manager functionality has been removed`)}`);

  // Initialize memory logger
  console.log(`${color.green(`[MEMORY] Initializing memory logger...`)}`);
  const memoryLogger = new MemoryLogger({
    logInterval: 5 * 60 * 1000, // Log every 5 minutes
    logToConsole: true,
    logToFile: true,
    logFilePath: './logs/memory-usage.log',
    warningThreshold: 80,
    criticalThreshold: 90
  });

  // Start memory logger
  memoryLogger.start();
  console.log(`${color.green(`[MEMORY] Memory logger started successfully!`)}`);

  // Store reference for cleanup
  client.memoryLogger = memoryLogger;

  // Run initial garbage collection
  if (global.gc) {
    console.log(`${color.green(`[MEMORY] Running initial garbage collection...`)}`);
    global.gc();
  }

  // Set up periodic garbage collection
  let gcInterval = null;
  if (global.gc) {
    // Run initial garbage collection
    console.log(`${color.cyan(`[SYSTEM] Running initial garbage collection...`)}`);
    global.gc();

    // Set up periodic garbage collection every 5 minutes
    gcInterval = setInterval(() => {
      console.log(`${color.cyan(`[SYSTEM] Running scheduled garbage collection...`)}`);
      global.gc();

      // Log current memory usage
      const memoryUsage = process.memoryUsage();
      console.log(`${color.cyan(`[SYSTEM] Memory usage: ${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB / ${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`)}`);
    }, 5 * 60 * 1000); // Every 5 minutes

    // Store interval for cleanup
    client.gcInterval = gcInterval;
  }

  // System monitoring functionality has been removed
  // Log bot ready status
  console.log(`${color.green(`[BOT] Bot is now ready and running!`)}`);
  console.log(`${color.cyan(`[INFO] Memory monitoring is active with no limits. Garbage collection will run periodically.`)}`);
});

global.client = client;