BotSettings:
  Token: "MTM2NzU1NzkzOTI1ODMyNzEwMA.Gl-MNY.tnYDtu7u7bEjK-i_khZIy4qFhpBkkcCBEXqZZo"
  BotName: "Mrk Manager"
  prefix: ","
  GuildID: "***************4558"
  StaffGuild: "1250601096783593573"
  EmbedColors: "#CCCCCC"
  LogCommands: true
  Statistics: true
  DevMode: false
  StaffRole: "1329591326324560044"
  PolicyMessage: ""
  WelcomeBanner: ""
  SERVER_NAME: "Mrk"
Database:
  MongoURI: "mongodb://admin:xTW8mgv5bl8jbQ@89.39.210.59:27017"
APIKeys:
  steam_api_key: "BE076C1F77ADE2C84412BC428312D4C4"
  bm_api_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6ImRjMjU5NWUwYTgxYmIyM2IiLCJpYXQiOjE3NDYxMjE5MTksIm5iZiI6MTc0NjEyMTkxOSwiaXNzIjoiaHR0cHM6Ly93d3cuYmF0dGxlbWV0cmljcy5jb20iLCJzdWIiOiJ1cm46dXNlcjo0OTUxNjMifQ.16I9eIAO_QwXQELIzJlbo9aBD9NuF5vH10Umw88TmSc
  bm_org_id: "106455"
  bm_ban_list_id: "b45a3070-f172-11ef-8919-b1029e5519b0"
  SECRET_KEY: "MRKISTHEBESTFUCKINGSERVER"
Channels:
  statusChannelID: "1347546446693662801"
  UpdatingCategory: ""

Servers:
  - name: "AU Mrk"
    long_name: "AU Mrk"
    ip: "*************"
    rcon_port: "29019"
    rcon_password: "24fnn3y8f4n308"
    battlemetrics_id: "31654621"
  - name: "EU Mrk"
    long_name: "EU Mrk"
    ip: "**************"
    rcon_port: "29019"
    rcon_password: "9uqwef927234qwef"
    battlemetrics_id: "32179603"
  - name: "NA Mrk"
    long_name: "NA Mrk"
    ip: "************"
    rcon_port: "28016"
    rcon_password: "1Lygttv58vbEEP"
    battlemetrics_id: "33281924"

Webhooks:
  LogsWebhook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  ErrorLogsHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  DiscLogs: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  ticketWebhookURL: https://discord.com/api/webhooks/1367559520410276020/WGngg6SmCw552Cc-std_G--9WmlD-p0oCn62pG34kQETFczg_GI4yGuNYAWX3m9zRbrn
  banStaffWebhook: https://discord.com/api/webhooks/1332971372108058667/QmzjoVld3YQR_5y7M_4ExVjijncFCqiuIH61bjOSvGayaI76YZ4YGjL5-I5IjqLv-989
  banPublicWebhook: "https://discord.com/api/webhooks/1361927692299014245/XathzmstYlgLFsmbMuFw-OnBvSlCkzDwQOhSJ4pG5mx-PdI-fImHY9MFG3K-P7_cvLa5"
  bugReportLogWebhook: "https://discord.com/api/webhooks/1367568187675119636/o5kL023K1aabSSTA1ZWQl32rr1PBdjpr2OH_MmfDLvPVNwkfYAyfzwoKE1GUWB3t_L-G"
  systemLogHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  adminLogHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  staffLogHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  joinLogHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  boostWebhook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  cdnLogsHook: "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
  ticketNotificationWebhook: "https://discord.com/api/webhooks/1368719628733255751/pVDjuvfPooC2AIsskM5HIhWRluH68_67H0AHG9cLIa45U2mPjZSPpYjjGHeRzfkkva7H"
  webhookURLs:
    - "https://discord.com/api/webhooks/1367559411395985458/4pZDm5pxViqieB38K_5MrNn84MIum3UC0o8yxIsouSN6IjzAzrk58KEcbJZSlPLdSihZ"
    - "https://discord.com/api/webhooks/1348386347798364170/pQ1kpZucT7bdCtrTn5JTJSIxvdorpihFjVAnklpZpuiCBhBSByk_kEqpYw1jVPDlWWI7"
LinkingSystem:
  # Type of linking system to use: 'steamcord', 'platformsync', 'custom', or 'disabled'
  # Set to 'disabled' to completely bypass account linking checks
  Type: custom
  # URL for the custom linking system
  WEBSITE_URL: https://link.mrkrust.xyz/
  # URL for the linking website (shown to users)
  linkingWebsite: https://link.mrkrust.xyz/
  # Logo URL for embeds
  LOGO_URL: https://cdn.discordapp.com/attachments/1329591447468507247/1329979874059747431/MGdHdOr.png?ex=6814c307&is=********&hm=af26acaa653c403549da8db108a6d28780a568aff228c1b517cfc6c1261877dd&
  # Server name for embeds
  SERVER_NAME: ""
  # Secret key for API authentication
  SECRET_KEY: "MRK"
  # Steamcord API key (if using Steamcord)
  SteamcordApiKey: ""
  # PlatformSync API key and URL (if using PlatformSync)
  PlatformSyncApiKey: ""
  PlatformSyncUrl: "https://api.platformsync.net"
  # Roles to assign when a user links their account
  keyRoles:
    - "1329591326324560044"
DeveloperIDs:
  inGameDevID: "797396356997447691"
  discordBotDevID: "1143288931681566891"
GuildSettings:
  guildColors:
    "***************4558": "#0000FF"
  RoleCheck: true
  guildRoleMapping:
    "***************4558": "1342714797417959497"
  guildWelcomeLogMapping:
    "***************4558": "1342451071028559894"
  guildBlacklistMapping:
    "1328596661395787786": Tournaments
  SuggestionChannels:
    "***************4558":
      - "1361917581170184324"
# BotActivitySettings section
BotActivitySettings:
  Enabled: true
  Type: WATCHING
  Interval: 10
  Statuses:
    - "Tickets: {total-tickets}"
    - "Total Members: {total-users}"
    - "Linked: {linkedUsers}"
TicketSystem:
  # Server Settings
  Server:
    # Set to true to create tickets in a different server than the main server
    UseSeperateServer: false
    # The ID of the server where tickets should be created (if UseSeperateServer is true)
    TicketServerID: "***************4558"

  Panel:
    Embed:
      Title: "Mrk Support"
      Description: "> ```Support - General Support Inquires\n> \n> Player Report - Do you think someone is cheating or using an unfair advantage?\n> \n> VIP/Payment Related - Do you have an issue with your payment or have you not been issued your purchase?\n> \n> Ban or Mute Appeal - Have you been muted or banned and want to appeal it?\n> \n> Staff Application - Do you want to apply to become a staff member?``` \n> ***Pick a ticket category to get started with your support journey!***\n> \n> ```Please ensure you have a public steam profile!\n> \n> We have right to close your ticket if it isn't public.```"
      Color: "#CCCCCC"
      PanelImage: "https://cdn.discordapp.com/attachments/1329591447468507247/1329979591426834595/npb3h0u.gif?ex=6814c2c4&is=68137144&hm=c4f454c9735f67a689f18b3fbea870113a17ce6fb89afb62612919340af518ca&"
      CustomThumbnailURL: ""
      Timestamp: true
      Footer:
        Enabled: true
        CustomIconURL: ""
        text: You may receive DMs from the bot!
  Settings:
    LogsChannelID: "1329591422596419636"
    BlacklistedRole: ""
    MentionAuthor: true
    MaxTickets: 1
    DeleteTime: 10
    RestrictTicketClose: false
    TicketCooldown: 0
    SelectMenu: true
    DeleteCommandTranscript: true
    ChannelTopic: "Creator: {username} | Category: {category}"
    # Whether to require users to be linked with the linking system before creating tickets
    # Set to false if you don't have the linking system set up or don't want to require linking
    # Set to true if you want to enforce account linking before ticket creation
    RequireLinking: true
  Transcripts:
    TranscriptType: HTML  # HTML or TXT
    SaveInFolder: true    # Save transcripts in the transcripts folder
    SaveImages: true      # Include images in HTML transcripts
    MessagesRequirement: 1  # Minimum messages required to generate a transcript
  ClaimingSystem:
    Enabled: true
    UserPerms:
      ViewChannel: true
      SendMessages: true
  ArchiveSystem:
    Enabled: true                           # Enable or disable the archive system
    TranscriptChannelID: "1328218621176189028"  # Channel where transcripts will be sent
    ViewClosedTicket: true                  # Allow users to view closed tickets
    RenameClosedTicket: true                # Rename tickets when closed
    TicketOpenLimit: false                  # Limit the number of open tickets
  AlertSystem:
    Enabled: true
    Time: 6h
    Title: Ticket Closure Notification
    DMUser: true
    DMMessage: Your ticket in {channel} will be closed {time} if no response has been received.
    Message: This ticket will be closed {time} if no response has been received.
  WorkingHours:
    Enabled: false
    Timezone: America/New_York
    WorkingHours: 07:00-20:00
    AllowTicketsOutsideWorkingHours: true
    SendNoticeInTicket: true
  Logs:
    userAdd:
      Enabled: true
      ChannelID: ""
    userRemove:
      Enabled: true
      ChannelID: ""
    renameTicket:
      Enabled: true
      ChannelID: ""
    ticketClose:
      Enabled: true
      ChannelID: ""
    paypalInvoice:
      Enabled: true
      ChannelID: ""
    cryptoPayments:
      Enabled: true
      ChannelID: ""
    stripeInvoice:
      Enabled: true
      ChannelID: ""
    claimTicket:
      Enabled: true
      ChannelID: ""
    unclaimTicket:
      Enabled: true
      ChannelID: ""
    deleteTicket:
      Enabled: true
      ChannelID: ""
  CDN:
    Enabled: true
    AllowedRoles:
      - ""

TicketAppearance:
  OpenEmbed:
    EmbedColor: "#CCCCCC"
    FooterMsg: ""
    FooterIcon: ""
    Timestamp: true
    UserIconThumbnail: true
    UserIconAuthor: false

TicketButton1:
  Enabled: true
  TicketName: Support
  Description: Get support for anything that is not listed on our tickets system.

  TicketCategoryID: "1333662573404487781"
  ClosedCategoryID: "1368720657495621772"
  TicketMessageTitle: "*How can we help?*"
  TicketMessage: |-
    > ***We appreciate you contacting our support team.***
    > *We will get back to as fast as we can!*
    > ```Please describe your issue and await a response.```
    >
    > ```{server}```
  ButtonEmoji: ""
  ButtonColor: Green
  SupportRoles:
    - "1329591332918001697"
  MentionSupportRoles: true
  ChannelName: support-{username}
  RequiredRoles:
    - ROLE_ID
  Questions:
    - customId: questionOne
      required: true
      question: What can we help you with?
      style: Short
TicketButton2:
  Enabled: true
  TicketName: Player Report
  Description: Do you think someone is cheating or using an unfair advantage?
  TicketCategoryID: "1333662573404487781"
  ClosedCategoryID: "1368720657495621772"
  TicketMessageTitle: "*How can we help?*"
  TicketMessage: |-
    > ***We appreciate you contacting our support team.***
    > *We will get back to as fast as we can!*
    > ```Please describe your issue and await a response.```
    >
    > ```{server}```
  ButtonEmoji: ""
  ButtonColor: Red
  SupportRoles:
    - "1329591332918001697"
  MentionSupportRoles: false
  ChannelName: player-report-{username}-{total-tickets}
  RequiredRoles:
    - ROLE_ID
  Questions:
    - customId: questinOne
      required: true
      question: Players Steam ID (76561198960542145)
      style: Paragraph
    - customId: questinTwo
      required: true
      question: Evidence regarding cheater report
      style: Paragraph
TicketButton3:
  Enabled: true
  TicketName: Payment Related
  Description: Do you have an issue with your payment?
  TicketCategoryID: "1333662573404487781"
  ClosedCategoryID: "1368720657495621772"
  TicketMessageTitle: "*How can we help?*"
  TicketMessage: |-
    > ***We appreciate you contacting our support team.***
    > *We will get back to as fast as we can!*
    > ```Please describe your issue and await a response.```
    >
    > ```{server}```
  ButtonEmoji: ""
  ButtonColor: Gray
  SupportRoles:
    - "1329591332918001697"
  MentionSupportRoles: false
  ChannelName: payment-related-{username}-{total-tickets}
  RequiredRoles:
    - ROLE_ID
  Questions:
    - customId: questionFive
      required: true
      question: What is your Steam64
      style: Short
    - customId: questionSix
      required: true
      question: What is your PaymentID
      style: Short
    - customId: questionSeven
      required: true
      question: What did you buy? (Package and Server)
      style: Paragraph
    - customId: questionEight
      required: true
      question: "Reason for ticket? "
      style: Paragraph
TicketButton4:
  Enabled: true
  TicketName: Ban Appeal
  Description: Have you been muted or banned and want to appeal it?
  TicketCategoryID: "1333662573404487781"
  ClosedCategoryID: "1368720657495621772"
  TicketMessageTitle: "*How can we help?*"
  TicketMessage: |-
    > ***We appreciate you contacting our support team.***
    > *We will get back to as fast as we can!*
    > ```Please describe your issue and await a response.```
    >
    > ```{server}```
  ButtonEmoji: ""
  ButtonColor: Gray
  SupportRoles:
    - "1329591332918001697"
  MentionSupportRoles: false
  ChannelName: banormute-appeal-{username}-{total-tickets}
  RequiredRoles:
    - ROLE_ID
  Questions:
    - customId: needHelp4
      required: true
      question: What is the Ban Reason?
      style: Short
    - customId: needHelp5
      required: true
      question: Why should we unban you?
      style: Paragraph
    - customId: needHelp6
      required: true
      question: "What is your steam64? "
      style: Short
TicketButton5:
  Enabled: true
  TicketName: Staff Application
  Description: Do you want to apply to become a staff member?
  TicketCategoryID: "1333662573404487781"
  ClosedCategoryID: "1368720657495621772"
  TicketMessageTitle: "*How can we help?*"
  TicketMessage: |-
    > ***We appreciate you contacting our support team.***
    > *We will get back to as fast as we can!*
    > ```Please describe your issue and await a response.```
    >
    > ```{server}```
  ButtonEmoji: ""
  ButtonColor: Gray
  SupportRoles:
    - "1329591324189528065"
    - "1335608208546594846"
    - "1329591327339319296"
  MentionSupportRoles: false
  ChannelName: staff-application-{username}-{total-tickets}
  RequiredRoles:
    - ROLE_ID
  Questions:
    - customId: staffApp1
      required: true
      question: How old are you? What's your SteamID
      style: Paragraph
    - customId: staffApp2
      required: true
      question: What role are you interested in?
      style: Paragraph
    - customId: staffApp3
      required: true
      question: Do you have an admin experience?
      style: Paragraph
    - customId: staffApp4
      required: true
      question: Why do you want to join our staff team?
      style: Paragraph
TicketButton6:
  Enabled: false
  TicketName: Example 6
  Description: ""
  TicketCategoryID: "1334310609088680037"
  ClosedCategoryID: "1334310609088680037"
  TicketMessageTitle: Support Ticket ({category})
  TicketMessage: |-
    > Thank you for contacting support.
    > Please describe your issue and await a response.
  ButtonEmoji: ""
  ButtonColor: Gray
  SupportRoles:
    - "1334305829725409345"
  MentionSupportRoles: false
  ChannelName: ticket-{username}
  RequiredRoles:
    - ""
  Questions:
    - customId: needHelp6
      required: true
      question: What do you need help with?
      style: Short
TicketButton7:
  Enabled: false
  TicketName: Example 7
  Description: ""
  TicketCategoryID: CATEGORY_ID
  ClosedCategoryID: ""
  TicketMessageTitle: Support Ticket ({category})
  TicketMessage: |-
    > Thank you for contacting support.
    > Please describe your issue and await a response.
  ButtonEmoji: ""
  ButtonColor: Gray
  SupportRoles:
    - ROLE_ID
  MentionSupportRoles: false
  ChannelName: ticket-{username}
  RequiredRoles:
    - ""
  Questions:
    - customId: needHelp7
      required: true
      question: What do you need help with?
      style: Short
TicketButton8:
  Enabled: false
  TicketName: Example 8
  Description: ""
  TicketCategoryID: CATEGORY_ID
  ClosedCategoryID: ""
  TicketMessageTitle: Support Ticket ({category})
  TicketMessage: |-
    > Thank you for contacting support.
    > Please describe your issue and await a response.
  ButtonEmoji: ""
  ButtonColor: Gray
  SupportRoles:
    - ROLE_ID
  MentionSupportRoles: false
  ChannelName: ticket-{username}
  RequiredRoles:
    - ""
  Questions:
    - customId: needHelp8
      required: true
      question: What do you need help with?
      style: Short
TicketFeedback:
  UserCloseDM:
    Enabled: true  # Set to true to send closure DMs to users
    SendTranscript: false # Set to false to not send transcripts to users
    TicketInformation: true  # Set to true to include ticket information in the DM
    CloseEmbedMsg: "> Your ticket **({ticketID})** has been closed, if you still need assistance, please make another ticket."
    CloseEmbedFooter: "Ticket ID: {ticketID}"
    CloseEmbedFooterIcon: ""
  ReviewSystem:
    Enabled: false
    AskWhyModal: true
    CloseEmbedReviewMsg: |-
      > Your ticket has been closed in **{guildName}**
      > ```We take support in {guildName} extremely seriously, we are very appreciative of everyone who helps us grow, reviews us, etc.```
      > *Thank you*
      > - Staff
    ticketRated: "> You have rated this ticket: {star} ({rating}/5)"
    ticketReviewed: |-
      > You rated this ticket: {star} ({rating}/5)
      > Your Review: {reviewMessage}
    ReviewMsg: Thank you for leaving a review!
    MinimumWords: 20
    MaximumWords: 250
  ReviewRequirements:
    Enabled: false
    TotalMessages: 5
  ReviewChannel:
    Enabled: false
    ChannelID: ""
    Embed:
      Title: ""
      Color: "#CCCCCC"
      ThumbnailEnabled: true
      CustomThumbnail: ""
      Fields: null
      Timestamp: true
      Footer:
        Enabled: false
        IconEnabled: true
        CustomIconURL: ""
        text: "{ticketCreator.username}"
PaymentSystems:
  PayPal:
    Enabled: false
    PayPalSecretKey: PAYPAL_SECRET_KEY
    PayPalClientID: PAYPAL_CLIENT_ID
    AllowedRoles:
      - ""
    Email: ""
    Currency: USD
    CurrencySymbol: $
    OnlyInTicketChannels: true
    Description: "Terms of Service: "
    Logo: ""
    RoleToGive: ""
  Stripe:
    Enabled: false
    StripeSecretKey: ""
    AllowedRoles:
      - ""
    Currency: USD
    CurrencySymbol: $
    OnlyInTicketChannels: false
    RoleToGive: ""
    PaymentMethods:
      - card
      - cashapp
  Crypto:
    Enabled: false
    AllowedRoles:
      - ""
    Currency: USD
    CurrencySymbol: $
    OnlyInTicketChannels: false
    Rates:
      bitfinex: false
      coinbase: true
      kraken: false
      binance: false
    Addresses:
      BTC: ""
      ETH: ""
      USDT: ""
      LTC: ""
SuggestionSystem:
  Settings:
    Enabled: false
    ChannelID: ""
    EnableAcceptDenySystem: true
    RemoveAllButtonsIfAcceptedOrDenied: true
    AllowedRoles:
      - ""
    LogsChannel: ""
  Statuses:
    Pending: " | ***Pending***"
    Accepted: " | ***Accepted***"
    Denied: " | ***Denied***"
  Colors:
    Pending: "#CCCCCC"
    Accepted: "#CCCCCC"
    Denied: "#E74C3C"
  Buttons:
    Upvote:
      ButtonName: Upvote
      ButtonEmoji: ""
      ButtonColor: Gray
    Downvote:
      ButtonName: Downvote
      ButtonEmoji: ""
      ButtonColor: Gray
    ResetVote:
      ButtonName: Reset Vote
      ButtonEmoji: ""
      ButtonColor: Gray
    Accept:
      ButtonName: Accept
      ButtonEmoji: ""
      ButtonColor: Blurple
    Deny:
      ButtonName: Deny
      ButtonEmoji: ""
      ButtonColor: Blurple
ChannelStats:
  Tickets:
    Total:
      Enabled: false
      ChannelID: "1328232355957309450"
      ChannelName: "Total Tickets: {total-tickets}"
    Open:
      Enabled: false
      ChannelID: "1328232369815293993"
      ChannelName: "Open Tickets: {open-tickets}"
  Rating:
    Average:
      Enabled: false
      ChannelID: "1328232383576543332"
      ChannelName: "Rating: {average-rating}/5.0"
  Members:
    Total:
      Enabled: false
      ChannelID: "1328232396583079986"
      ChannelName: "Total Members: {total-members}"
  Requests:
    Total:
      Enabled: false
      ChannelID: "1328232409937870909"
      ChannelName: "Total Requests: {total-requests}"
Locale:
  General:
    NoPermsMessage: Sorry, you don't have permissions to use this command!
    NotInTicketChannel: You're not in a ticket channel!
  TicketCreation:
    AlreadyOpenTitle: Ticket Already Open
    AlreadyOpenMsg: You may only have **{max} ticket(s)** open at a time.
    ticketCreatedTitle: Ticket Created
    ticketCreatedMsg: Your ticket has been created in
    cooldownEmbedMsgTitle: Cooldown
    cooldownEmbedMsg: You have to wait {time} before creating another ticket!
    selectCategory: Select a category...
    requiredRoleMissing: You don't have the required role to open a ticket in this category!
    requiredRoleTitle: Role Required
    linkingRequiredTitle: Account Linking Required
    linkingRequiredDescription: You need to link your account before creating a ticket.
    linkAccountButton: Link Account
  TicketManagement:
    CloseTicketButton: Close Ticket
    deletingTicketMsg: Deleting ticket in {time} seconds
    ticketUserAdd: Added **{user} ({username})** to the ticket.
    ticketUserRemove: Removed **{user} ({username})** from the ticket.
    ticketRenamed: This ticket has been renamed to **{newName}**!
    restrictTicketClose: You are not allowed to close this ticket!
    ticketPinned: This ticket has been pinned!
    ticketAlreadyPinned: This ticket is already pinned!
    reOpenButton: Re-Open
    transcriptButton: Transcript
    deleteTicketButton: Delete
    ticketClosedBy: This ticket was closed by {user} ({username})
    ticketReOpenedBy: This ticket has been re-opened by {user} ({username})
    ticketClosedByReason: |-
      This ticket was closed by {user} ({username})

      **Reason:**
      {reason}
    successReason: Successfully set reason for ticket closure!
  TicketClaiming:
    restrictTicketClaim: You are not allowed to claim this ticket!
    claimTicketButton: Claim
    unclaimTicketButton: Unclaim
    ticketClaimedBy: Claimed by
    ticketUnClaimedBy: Unclaimed by
    ticketClaimedTitle: Ticket Claimed
    ticketUnClaimedTitle: Ticket Unclaimed
    ticketNotClaimed: This ticket has not been claimed!
    ticketClaimed: |-
      ***The ticket has been claimed by{user}
      They will be assist you with your ticket shortly!***
    ticketUnClaimed: "***The ticket has been unclaimed by {user}, another staff member will be with you shortly.***"
    ticketDidntClaim: You did not claim this ticket, Only the user that claimed this ticket can unclaim it! ({user})
    claimTicketMsg: You successfully claimed this ticket!
    unclaimTicketMsg: You successfully unclaimed this ticket!
  TicketTranscript:
    ticketTranscriptCategory: Category
    ticketTranscript: Ticket Transcript
    ticketName: Ticket Name
    transcriptSaved: Transcript saved.
    transcriptSavedBy: Saved by {user}
    notAllowedTranscript: You are not allowed to create a transcript!
    notAllowedDelete: You are not allowed to delete this ticket!
    viewTranscriptButton: View Transcript
    dmTranscriptField: • Transcript
    dmTranscriptClickhere: Click here
  TicketCloseDM:
    ticketInformationCloseDM: • Ticket Information
    categoryCloseDM: "Category:"
    claimedByCloseDM: "Claimed by:"
    ticketClosedCloseDM: Ticket Closed
    notClaimedCloseDM: Not claimed
  TicketQuestions:
    ticketQuestionsTitle: Ticket Questions - {category}
    notAnswered: Not answered
    answeringQuestionsSuccess: Thank you for answering the questions!
  WorkingHours:
    outsideWorkingHoursTitle: Outside Working Hours
    outsideWorkingHours: You can only create tickets during the working hours! {startTime} to {endTime}
    outsideWorkingHoursMsg: You've created a ticket outside of our working hours, so please be aware that our response time may be slightly delayed. {startTime} to {endTime}
  Blacklist:
    RoleBlacklistedTitle: Blacklisted
    RoleBlacklistedMsg: Your role is blacklisted from creating tickets!
    alreadyBlacklisted: "{user} is already blacklisted!"
    successfullyBlacklisted: "{user} has been successfully **blacklisted** from creating tickets!"
    notBlacklisted: "{user} is not blacklisted!"
    successfullyUnblacklisted: "{user} has been successfully **unblacklisted** from creating tickets!"
    userBlacklistedTitle: Blacklisted
    userBlacklistedMsg: You are blacklisted from creating tickets!
  TicketRating:
    ticketRating: Ticket Rating
    totalReviews: "Total Reviews:"
    averageRating: "Average Rating:"
    selectReview: Select a review...
    explainWhyRating: Please explain why you are giving this rating
    ratingsStats: Reviews
    averageCompletionTime: "Avg. Completion Time:"
  UserLeft:
    userLeftTitle: User Left
    userLeftDescription: The user that created this ticket has left the server **({username})**
  Logs:
    userAddTitle: Ticket Logs | User Added
    userRemoveTitle: Ticket Logs | User Removed
    ticketCloseTitle: Ticket Logs | Ticket Closed
    ticketRenameTitle: Ticket Logs | Ticket Renamed
    logsExecutor: Executor
    logsTicket: Ticket
    logsUser: User
    logsTicketAuthor: Ticket Creator
    logsClosedBy: Closed by
    logsDeletedBy: Deleted by
    ticketClaimedLog: Ticket Logs | Ticket Claimed
    ticketUnClaimedLog: Ticket Logs | Ticket Unclaimed
    totalMessagesLog: "Total Messages:"
    ticketForceDeleted: Ticket Force Deleted
    reason: Reason
  Statistics:
    totalTickets: "Total Tickets:"
    openTickets: "Open Tickets:"
    totalClaims: "Total Claims:"
    guildStatistics: Guild Statistics
    statsTickets: Tickets
  Payments:
    PayPalInvoiceTitle: PayPal Invoice
    PayPalInvoiceMsg: Please click the button below to pay!
    PayPalSeller: "Seller:"
    PayPalUser: "User:"
    PayPalPrice: "Price:"
    PayPalService: "Service:"
    PayPalPayInvoice: Pay Invoice
    PayPalLogTitle: Ticket Logs | PayPal Invoice
    StripeInvoiceTitle: Stripe Invoice
    StripeLogTitle: Ticket Logs | Stripe Invoice
    cryptoTitle: Crypto Payment
    cryptoMessage: Scan the QR Code below or pay to the below address with the exact amount
    cryptoLogTitle: Ticket Logs | Crypto Payment
    cryptoLogAddress: Address
    cryptoQRCode: QR Code
  Suggestions:
    suggestionSubmit: Your suggestion has been submitted, Thank you!
    suggestionTitle: Suggestion
    suggestionStatsTitle: Suggestions
    suggestionsTotal: "Total Suggestions:"
    suggestionsTotalUpvotes: "Total Upvotes:"
    suggestionsTotalDownvotes: "Total Downvotes:"
    suggestionInformation: Information
    suggestionUpvotes: "Upvotes:"
    suggestionDownvotes: "Downvotes:"
    suggestionFrom: "From:"
    suggestionStatus: "Status:"
    newSuggestionTitle: New Suggestion
    suggestionVoteResetTitle: Vote Reset
    suggestionVoteReset: Your vote on [this]({link}) suggestion has been reset!
    suggestionNoVoteTitle: No Vote
    suggestionNoVote: You haven't voted for [this]({link}) suggestion!
    suggestionDownvotedTitle: Suggestion Downvoted
    suggestionDownvoted: You successfully downvoted [this]({link}) suggestion!
    suggestionAlreadyVotedTitle: Already Voted
    suggestionAlreadyVoted: You have already voted on [this]({link}) suggestion! You can press the Reset Vote button to change your vote.
    suggestionUpvotedTitle: Suggestion Upvoted
    suggestionUpvoted: You successfully upvoted [this]({link}) suggestion!
    suggestionAcceptedTitle: Suggestion Accepted
    suggestionAccepted: You successfully accepted [this]({link}) suggestion!
    suggestionDeniedTitle: Suggestion Denied
    suggestionDenied: You successfully denied [this]({link}) suggestion!
    suggestionNoPerms: You are not allowed to accept or deny suggestions!
    suggestionCantVoteTitle: Can't vote
    suggestionCantVote: You can't vote for [this]({link}) suggestion because it has already been accepted or denied!
HelpCommand:
  Appearance:
    EmbedColor: ""
    FooterTimestamp: true
    GuildIcon: true
    Title: "{botName}'s Commands List"
    FooterMsg: "{guildName}"
    FooterIcon: ""
  Categories:
    General:
      Name: " | General —"
      ShowCount: true
    Ticket:
      Name: " | Ticket —"
      ShowCount: true
    Utility:
      Name: " | Utility —"
      ShowCount: true
ButtonCustomization:
  Emojis:
    deleteTicket: ""
    reOpenTicket: ""
    createTranscript: ""
    closeTicket: ""
    ticketCreated: ""
    ticketClaim: ""
  Colors:
    deleteTicket: Secondary
    reOpenTicket: Secondary
    createTranscript: Secondary
    closeTicket: Danger
    ticketClaim: Success
    ticketUnclaim: Primary
    ticketConfirmClosure: Success
    ticketCancelClosure: Danger
DatabaseMaintenance:
  AutoCleanup:
    Tickets:
      enabled: true
      time: 6
    Reviews:
      enabled: true
      time: 6
    WeeklyStats:
      enabled: true
      time: 3
