const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, ButtonBuilder, EmbedBuilder, StringSelectMenuBuilder, ButtonStyle } = require("discord.js");
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const commands = yaml.load(fs.readFileSync('./commands.yml', 'utf8'))
const moment = require('moment-timezone');
const ticketPanelModel = require("../../models/ticketPanelModel");

module.exports = {
    enabled: commands.Ticket.Panel.Enabled,
    data: new SlashCommandBuilder()
        .setName('panel')
        .setDescription(commands.Ticket.Panel.Description),
    async execute(interaction, client) {
      await interaction.deferReply({ ephemeral: true });

      if(!interaction.member.permissions.has("Administrator")) return interaction.editReply({ content: config.Locale.NoPermsMessage, ephemeral: true });
      let startTimestamp = "Working hours are disabled!";
      let endTimestamp = "Working hours are disabled!";

      if(config.WorkingHours.Enabled) {
        const workingHoursRegex = /^(\d{1,2}:\d{2})-(\d{1,2}:\d{2})$/;
        const workingHoursMatch = config.WorkingHours.WorkingHours.match(workingHoursRegex);

        if (!workingHoursMatch) {
          console.log('\x1b[31m%s\x1b[0m', `[ERROR] Invalid working hours configuration (format), Contact support and provide your config.yml file.`)
        }

        const currentTime = moment().tz(config.WorkingHours.Timezone);
        const startDate = currentTime.format('YYYY-MM-DD');

        const startTime = moment.tz(startDate + ' ' + workingHoursMatch[1], 'YYYY-MM-DD H:mm', config.WorkingHours.Timezone);
        const endTime = moment.tz(startDate + ' ' + workingHoursMatch[2], 'YYYY-MM-DD H:mm', config.WorkingHours.Timezone);

        startTimestamp = startTime.unix();
        endTimestamp = endTime.unix();
      }

      let workingHoursEmbedLocale = config.TicketPanelSettings.Embed.Description.replace(/{workingHours-startTime}/g, `<t:${startTimestamp}:t>`).replace(/{workingHours-endTime}/g, `<t:${endTimestamp}:t>`);
      const ticketEmbed = new EmbedBuilder()
      if(config.TicketPanelSettings.Embed.Title) ticketEmbed.setTitle(config.TicketPanelSettings.Embed.Title)
      ticketEmbed.setDescription(workingHoursEmbedLocale)
      if(config.TicketPanelSettings.Embed.Color) ticketEmbed.setColor(config.TicketPanelSettings.Embed.Color)
      if(!config.TicketPanelSettings.Embed.Color) ticketEmbed.setColor(config.EmbedColors)
      if(config.TicketPanelSettings.Embed.CustomThumbnailURL) ticketEmbed.setThumbnail(config.TicketPanelSettings.Embed.CustomThumbnailURL)
      if(config.TicketPanelSettings.Embed.Footer.Enabled && config.TicketPanelSettings.Embed.Footer.text) ticketEmbed.setFooter({ text: `${config.TicketPanelSettings.Embed.Footer.text}` })
      if(config.TicketPanelSettings.Embed.Footer.Enabled && config.TicketPanelSettings.Embed.Footer.text && config.TicketPanelSettings.Embed.Footer.CustomIconURL) ticketEmbed.setFooter({ text: `${config.TicketPanelSettings.Embed.Footer.text}`, iconURL: `${config.TicketPanelSettings.Embed.Footer.CustomIconURL}` })
      if(config.TicketPanelSettings.Embed.Timestamp) ticketEmbed.setTimestamp()

      const imageEmbed = new EmbedBuilder()
      if(config.TicketPanelSettings.Embed.Color) imageEmbed.setColor(config.TicketPanelSettings.Embed.Color)
      if(config.TicketPanelSettings.Embed.PanelImage) imageEmbed.setImage(config.TicketPanelSettings.Embed.PanelImage)
      
      const createTicket = new ButtonBuilder().setCustomId('ticketUserCheck').setLabel(`Create a Ticket`).setStyle(ButtonStyle.Secondary)
      const sRow = new ActionRowBuilder().addComponents(createTicket);
      interaction.editReply({ content: `Successfully sent the ticket panel to this channel!`, ephemeral: true })
      interaction.channel.send({ embeds: [ticketEmbed, imageEmbed], components: [sRow] }).then(async function(msg) {
          const newPanel = new ticketPanelModel({
            guildID: config.GuildID,
            msgID: msg.id,
          });
          await newPanel.save();
      })

    }
  }