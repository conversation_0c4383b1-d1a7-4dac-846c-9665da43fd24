const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const ticketModel = require("../../models/ticketModel");
const fs = require('fs');
const yaml = require("js-yaml");

// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');

module.exports = {
  enabled: true,
  data: new SlashCommandBuilder()
    .setName("reply")
    .setDescription("Reply to the ticket user from this channel.")
    .addStringOption(option =>
      option.setName("message")
        .setDescription("Message to send to the user.")
        .setRequired(true)
    ),

  async execute(interaction) {
    await interaction.deferReply({ ephemeral: false });

    // Fetch the ticket data
    const ticketData = await ticketModel.findOne({ channelID: interaction.channel.id });

    if (!ticketData || !ticketData.userID) {
      return interaction.followUp({ content: "❌ Could not find the ticket owner.", ephemeral: true });
    }

    // Determine the support roles based on the ticket's button
    const ticketButton = ticketData.button; // e.g., "TicketButton1"
    if (!ticketButton || !config[ticketButton]) {
      return interaction.followUp({ content: "❌ Ticket configuration not found.", ephemeral: true });
    }

    const supportRoles = config[ticketButton].SupportRoles || [];

    if (!supportRoles || supportRoles.length === 0) {
      return interaction.followUp({ content: "❌ No support roles configured for this ticket type.", ephemeral: true });
    }

    // Check if the user has at least one of the support roles
    const hasSupportRole = supportRoles.some(role => interaction.member.roles.cache.has(role));

    if (!hasSupportRole) {
      return interaction.followUp({ content: "❌ You do not have permission to use this command.", ephemeral: true });
    }

    const user = await interaction.client.users.fetch(ticketData.userID).catch(() => null);

    if (!user) {
      return interaction.followUp({ content: "❌ User not found. They might have left the server.", ephemeral: true });
    }

    // Get the staff's message input
    const staffMessage = interaction.options.getString("message");

    // Create the embed message for the user
    const userEmbed = new EmbedBuilder()
      .setAuthor({ name: "Staff Response", iconURL: `https://cdn.discordapp.com/attachments/1329591447468507247/1329979874739224728/CARZWaY.png?ex=68156bc7&is=68141a47&hm=8bc5fe2fd9ceaf6be3aabef5da5462beae464d8ee3101e81f972a3c1ff6f591c&` })
      .setDescription(`***Response Received.***\n \`\`\`${staffMessage}\`\`\``)
      .setColor("#FFFFFF")
      .setTimestamp();

    try {
      // Send DM to the user
      await user.send({ embeds: [userEmbed] });

      // Confirm in the ticket channel
      const ticketEmbed = new EmbedBuilder()
        .setColor("#FFFFFF")
        .setDescription(`> ***Message sent to <@${ticketData.userID}>***\n> \`\`\`${staffMessage}\`\`\``)
        .setTimestamp();

      await interaction.followUp({ embeds: [ticketEmbed], ephemeral: false });

    } catch (error) {
      console.error("❌ Error sending DM:", error);
      return interaction.followUp({ content: "❌ Failed to send the message. The user may have DMs disabled.", ephemeral: true });
    }
  }
};