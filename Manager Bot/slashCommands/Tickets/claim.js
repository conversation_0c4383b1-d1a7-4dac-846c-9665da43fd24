const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, Embed<PERSON>uilder, ButtonBuilder, ButtonStyle, ActionRowBuilder, PermissionFlagsBits, WebhookClient } = require("discord.js");
const fs = require('fs');
const ticketModel = require("../../models/ticketModel");
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const utils = require("../../utils");
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` })
const ticketTimers = new Map();

function startInactivityTimer(channelID, client) {
    if (ticketTimers.has(channelID)) {
        clearTimeout(ticketTimers.get(channelID)); // Reset if already running
    }

    const timer = setTimeout(async () => {
        const ticketDB = await ticketModel.findOne({ channelID });
        if (!ticketDB || !ticketDB.claimed) return;

        const lastMessageTime = new Date(ticketDB.lastMessageSent || 0);
        const now = new Date();
        const timeDiff = (now - lastMessageTime) / (1000 * 60); // Convert to minutes

        if (timeDiff >= 20) { // 20-minute inactivity check
            await ticketModel.updateOne({ channelID }, { $set: { claimed: false, claimUser: null } });

            const ticketChannel = client.channels.cache.get(channelID);
            if (ticketChannel) {
                const unclaimEmbed = new EmbedBuilder()
                    .setTitle("⏳ Ticket Unclaimed Due to Inactivity")
                    .setColor("Yellow")
                    .setDescription("This ticket was unclaimed because there was no activity for 20 minutes.")
                    .setTimestamp();

                await ticketChannel.send({ embeds: [unclaimEmbed] });

                // ✅ Update ticket embed & buttons
                try {
                    const msg = await ticketChannel.messages.fetch(ticketDB.msgID);
                    const embed = msg.embeds[0];

                    embed.fields[0] = { name: `${config.Locale.ticketClaimedBy}`, value: `> ${config.Locale.ticketNotClaimed}` };

                    const ticketDeleteButton = new ButtonBuilder()
                        .setCustomId('closeTicket')
                        .setLabel(config.Locale.CloseTicketButton)
                        .setStyle(ButtonStyle.Danger);

                    const ticketClaimButton = new ButtonBuilder()
                        .setCustomId('ticketclaim')
                        .setLabel(config.Locale.claimTicketButton)
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(false);

                    const row = new ActionRowBuilder().addComponents(ticketDeleteButton, ticketClaimButton);
                    await msg.edit({ embeds: [embed], components: [row] });

                } catch (error) {
                    console.error("❌ Failed to update ticket message after unclaiming:", error);
                }
            }
        }
    }, 20 * 60 * 1000); // 20 minutes

    ticketTimers.set(channelID, timer);
}

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("claim")
        .setDescription("Claim a ticket as a support member.")
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages), // Staff-only command

    async execute(interaction) {
        await interaction.deferReply({ ephemeral: true });

        const ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
        if (!ticketDB) {
            return interaction.editReply({ content: "❌ This channel is not linked to a valid ticket.", ephemeral: true });
        }

        if (!config.ClaimingSystem.Enabled) {
            return interaction.editReply({ content: "❌ Ticket claiming is disabled in the configuration!", ephemeral: true });
        }

        let supportRole = await utils.checkIfUserHasSupportRoles(interaction);
        if (!supportRole) {
            return interaction.editReply({ content: config.Locale.restrictTicketClaim, ephemeral: true });
        }

        let embedClaimVar = config.Locale.ticketClaimed.replace(/{user}/g, `<@!${interaction.user.id}>`);
        const embed = new EmbedBuilder()
            .setTitle(config.Locale.ticketClaimedTitle)
            .setColor("Green")
            .setDescription(embedClaimVar)
            .setTimestamp()
            .setFooter({ text: `${config.Locale.ticketClaimedBy} ${interaction.user.username}`, iconURL: `${interaction.user.displayAvatarURL({ dynamic: true })}` });

        await interaction.channel.send({ embeds: [embed] });

        try {
            const msg = await interaction.channel.messages.fetch(ticketDB.msgID);
            const embed = msg.embeds[0];

            if (!embed.fields) embed.fields = [];
            embed.fields[0] = { name: `${config.Locale.ticketClaimedBy}`, value: `> <@!${interaction.user.id}> (${interaction.user.username})` };

            const ticketDeleteButton = new ButtonBuilder()
                .setCustomId('closeTicket')
                .setLabel(config.Locale.CloseTicketButton)
                .setStyle(ButtonStyle.Danger);

            const ticketClaimButton = new ButtonBuilder()
                .setCustomId('ticketclaim')
                .setLabel(config.Locale.claimTicketButton)
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(true);

            const ticketUnClaimButton = new ButtonBuilder()
                .setCustomId('ticketunclaim')
                .setLabel(config.Locale.unclaimTicketButton)
                .setStyle(config.ButtonColors.ticketUnclaim);

            let row = new ActionRowBuilder().addComponents(ticketDeleteButton, ticketClaimButton, ticketUnClaimButton);
            await msg.edit({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error("❌ Failed to fetch or edit ticket message:", error);
        }

        await interaction.channel.permissionOverwrites.edit(interaction.user, {
            SendMessages: true,
            ViewChannel: true,
            AttachFiles: true,
            EmbedLinks: true,
            ReadMessageHistory: true
        });

        await ticketModel.updateOne(
            { channelID: interaction.channel.id },
            { $set: { claimed: true, claimUser: interaction.user.id, lastMessageSent: new Date() } }
        );

        await interaction.editReply({ content: config.Locale.claimTicketMsg, ephemeral: false });

        // ✅ Start inactivity timer for 20 minutes
        startInactivityTimer(interaction.channel.id, interaction.client);
    }
};
