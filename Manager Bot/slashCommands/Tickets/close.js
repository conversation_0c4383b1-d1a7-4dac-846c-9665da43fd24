const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, EmbedBuilder } = require("discord.js");
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const commands = yaml.load(fs.readFileSync('./commands.yml', 'utf8'))
const utils = require("../../utils.js");
const ticketModel = require("../../models/ticketModel");

module.exports = {
    enabled: commands.Ticket.Close.Enabled,
    data: new SlashCommandBuilder()
        .setName('close')
        .setDescription(commands.Ticket.Close.Description),
    async execute(interaction, client) {
    const ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });
    if(!ticketDB) return interaction.reply({ content: config.Locale.NotInTicketChannel, ephemeral: true })

    let supportRole = await utils.checkIfUserHasSupportRoles(interaction)

    if (config.TicketSettings.RestrictTicketClose && !supportRole) {
      return interaction.reply({ content: config.Locale.restrictTicketClose, ephemeral: true });
    }
    if (interaction.guildId !== config.GuildID) {
      return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
  }

    // set closerUserID in the tickets db
    await ticketModel.updateOne({ channelID: interaction.channel.id }, { $set: { closeUserID: interaction.user.id, closedAt: Date.now() } });

    await client.emit('ticketClose', interaction);

    }

}