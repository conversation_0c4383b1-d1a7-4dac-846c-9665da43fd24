const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, EmbedBuilder, ButtonBuilder, WebhookClient } = require("discord.js");
const yaml = require("js-yaml")
const ms = require('ms');
const fs = require('fs');
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const UserData = require('../../models/userData')
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` });

module.exports = {
    enabled: true,
        data: new SlashCommandBuilder()
        .setName('timeout')
        .setDescription('timeout a user for breaking rules.')
        .addUserOption((option) => option.setName('user').setDescription('User').setRequired(true))
        .addStringOption((option) => option.setName('time').setDescription('Length of timeout (1m, 1h, 1d, etc)').setRequired(true))
        .addStringOption((option) => option.setName('reason').setDescription('Reason').setRequired(false)),
    async execute(interaction, client) {
        if (!interaction.member.permissions.has("Administrator")) return interaction.reply({ content: "No permission", ephemeral: true })
        const user = interaction.options.getUser("user");
        const reason = interaction.options.getString("reason");
        const time = interaction.options.getString("time");
        const member = interaction.guild.members.cache.get(user.id);
        let convertedTime = ms(time);
        member.timeout(convertedTime, reason);
        let userData = await UserData.findOne({ userId: user.id });

        if (!userData) {
            userData = new UserData({
                userId: user.id,
                warns: 0,
                messages: 0,
                kicks: 0,
                bans: 0,
                timeouts: 1,
                note: "None",
            });
        } else {
            userData.timeouts++;
        }

        await userData.save();

        const embed = new EmbedBuilder()
        .setTitle(`*Timed Out*`)
            .setColor("#CCCCCC")
            .setDescription('You were timed out in ``' + interaction.guild.name + '``')
            .addFields([
                { name: `Details:`, value: `\`\`User:\`\` <@${user.id}> (${user.tag}) \n\`\`Reason:\`\` ${reason} \n\`\`Staff:\`\` <@${interaction.user.id}>` },
            ])
            .setTimestamp()
            .setFooter({ text: `${user.tag}`, iconURL: `${user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 })}` })

        member.send({ embeds: [embed] });
        logsHook.send({ embeds: [embed] });
        interaction.reply({ content: `Timed out ${user.tag} for ${reason}`, ephemeral: true });
    }
}