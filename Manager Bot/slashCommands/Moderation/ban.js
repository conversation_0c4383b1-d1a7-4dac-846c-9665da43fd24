const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, EmbedBuilder, ButtonBuilder, WebhookClient } = require("discord.js");
const yaml = require("js-yaml")
const fs = require('fs');
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const UserData = require('../../models/userData');
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` });

module.exports = {
    enabled: true,
        data: new SlashCommandBuilder()
        .setName('ban')
        .setDescription('Ban a user for breaking rules.')
        .addUserOption((option) => option.setName('user').setDescription('User').setRequired(true))
        .addStringOption((option) => option.setName('reason').setDescription('Reason').setRequired(true)),
    async execute(interaction, client) {
        if (!interaction.member.permissions.has("Administrator")) return interaction.reply({ content: "No permission", ephemeral: true })
        let user = interaction.options.getUser("user");
        let reason = interaction.options.getString("reason");
        let member = interaction.guild.members.cache.get(user.id)
        let userData = await UserData.findOne({ userId: user.id });

        if (!userData) {
            userData = new UserData({
                userId: user.id,
                warns: 0,
                messages: 0,
                kicks: 0,
                bans: 1,
                timeouts: 0,
                note: "None",
            });
        } else {
            userData.bans++;
        }

        await userData.save();
        const Embed = new EmbedBuilder()
            .setTitle(`*<@!${user.id}> has been banned.*`)
            .setColor("CCCCCC")
            .addFields([
                { name: `Details:`, value: `\`\`User:\`\` <@${user.id}> (${user.tag}) \n\`\`Reason:\`\` ${reason} \n\`\`Banned By:\`\` <@${interaction.user.id}>` },
            ])
            .setTimestamp()
            .setFooter({ text: `${user.tag}`, iconURL: `${user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 })}` })
            interaction.guild.members.ban(user.id, { reason: reason })
            .then(() => {
                interaction.reply({ content: `Successfully banned ${user.tag}!`, ephemeral: true });
                logsHook.send({ embeds: [Embed] });
            })
            .catch((error) => {
                console.error(`Error banning user ${user.tag}:`, error);
                interaction.reply({ content: "Failed to ban the user.", ephemeral: true });
            });
    }
}