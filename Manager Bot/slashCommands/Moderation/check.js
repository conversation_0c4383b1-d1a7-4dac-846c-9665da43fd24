const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, EmbedBuilder, ButtonBuilder, WebhookClient, ContextMenuCommandBuilder, ApplicationCommandType } = require("discord.js");
const moment = require('moment');
const UserData = require('../../models/userData');
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` });

module.exports = {
    enabled: true,
        data: new SlashCommandBuilder()
        .setName('check')
        .setDescription('Check a users information')
        .addUserOption((option) => option.setName('user').setDescription('User').setRequired(true)),
    async execute(interaction, client) {
        if(!interaction.member.permissions.has("Administrator")) return interaction.reply({ content: "No permission", ephemeral: true })
        const user = interaction.options.getUser("user");
        let userData = await UserData.findOne({ userId: user.id });

        if (!userData) {
            userData = new UserData({
                userId: user.id,
                warns: 0,
                messages: 0,
                kicks: 0,
                bans: 1,
                timeouts: 0,
                note: "None",
            });
            return interaction.reply({ content: "User not found in database", ephemeral: true });
        }

        let member = interaction.guild.members.cache.get(user.id);
        let avatarurl = user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 });
        let historyembed = new EmbedBuilder()
            .setColor("#CCCCCC")
            .setTitle(`${user.tag}'s history`)
            .setThumbnail(avatarurl)
            .setDescription(`**User Information:**\n\`\`Name:\`\` <@!${user.id}>\n\`\`Joined Server:\`\` ${moment(member.joinedAt).format('DD/MM/YY')}\n\`\`Total Messages:\`\` ${userData.messages}\n\`\`Note:\`\` ${userData.note}`)
            .addFields([
                { name: `Warnings`, value: `\`\`\`${userData.warns}\`\`\``, inline: true },
                { name: `Timeouts`, value: `\`\`\`${userData.timeouts}\`\`\``, inline: true },
                { name: `Kicks`, value: `\`\`\`${userData.kicks}\`\`\``, inline: true },
            ])
            .setTimestamp()
        .setFooter({ text: `${interaction.guild.name}` })
        interaction.reply({ embeds: [historyembed], ephemeral: true })
    }
}