const { SlashCommandBuilder } = require('@discordjs/builders');
const { EmbedBuilder, WebhookClient } = require("discord.js");
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` });

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('clear')
        .setDescription('Clear messages from a channel.')
        .addIntegerOption(option => option.setName('amount').setDescription('Amount of messages').setRequired(true)),
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true })
        if (!interaction.member.permissions.has("MANAGE_MESSAGES")) return interaction.editReply({ content: "No permission", ephemeral: true });

        const amount = interaction.options.getInteger("amount");
        if (amount > 100) return interaction.editReply("You can only delete up to 100 messages at a time.");
        if (amount < 1) return interaction.editReply("You must delete at least 1 message.");

        if (amount > 0) {
            const messages = await interaction.channel.bulkDelete(amount, false).catch(err => {
                console.error(err);
                return interaction.editReply("There was an error trying to delete messages in this channel!");
            });

            if (messages && messages.size > 0) {
                const embed = new EmbedBuilder()
                    .setTitle("Purged Messages")
                    .setDescription(`${messages.size} messages have been deleted from <#${interaction.channel.id}>`)
                    .setColor('#CCCCCC')
                    .setTimestamp()
                    .setFooter({ text: interaction.guild.name, iconURL: interaction.guild.iconURL() });

                await interaction.editReply({ embeds: [embed] });
            } else {
                await interaction.editReply("No messages were deleted.");
            }
        }
    }
};
