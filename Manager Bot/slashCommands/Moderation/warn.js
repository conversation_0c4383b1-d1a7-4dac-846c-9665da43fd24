const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, EmbedBuilder, ButtonBuilder, WebhookClient } = require("discord.js");
const yaml = require("js-yaml")
const fs = require('fs');
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const UserData = require('../../models/userData')
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` });

module.exports = {
    enabled: true,
        data: new SlashCommandBuilder()
        .setName('warn')
        .setDescription('Warn a user for breaking rules.')
        .addUserOption((option) => option.setName('user').setDescription('User').setRequired(true))
        .addStringOption((option) => option.setName('reason').setDescription('Reason').setRequired(true)),
    async execute(interaction, client) {
        if (!interaction.member.permissions.has("Administrator")) return interaction.reply({ content: "No permission", ephemeral: true })
        const user = interaction.options.getUser("user")
        const reason = interaction.options.getString("reason")
        let userData = await UserData.findOne({ userId: user.id });

        if (!userData) {
            userData = new UserData({
                userId: user.id,
                warns: 1,
                messages: 0,
                kicks: 0,
                bans: 0,
                timeouts: 0,
                note: "None",
            });
        } else {
            userData.warns++;
        }

        await userData.save();
        const logEmbed = new EmbedBuilder()
            .setTitle(`*Moderation Action*`)
            .setColor("#CCCCCC")
            .addFields([{ name: `Action:`, value: "``Warning``" }, { name: `Details:`, value: `\`\`User:\`\` <@!${user.id}>\n\`\`Staff:\`\` <@!${interaction.user.id}>\n\`\`Reason:\`\` ${reason}` },])
            .setTimestamp()
            .setFooter({ text: interaction.guild.name, iconURL: `${interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 })}` })
        interaction.reply({ content: `<@!${user.id}> has been warned`, embeds: [logEmbed] })
        logsHook.send({ embeds: [logEmbed] })
    }
}