const { SlashCommandBuilder } = require('@discordjs/builders');
const Discord = require ("discord.js")
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const commands = yaml.load(fs.readFileSync('./commands.yml', 'utf8'))
const utils = require("../../utils.js");
const guildModel = require("../../models/guildModel");

module.exports = {
    enabled: commands.General.Stats.Enabled,
    data: new SlashCommandBuilder()
        .setName('stats')
        .setDescription(commands.General.Stats.Description),
    async execute(interaction, client) {
      await interaction.deferReply();
        let statsDB = await guildModel.findOne({ guildID: config.GuildID });

        const statsEmbed = new Discord.EmbedBuilder()
        statsEmbed.setTitle(`*Statistics*`)
        if(interaction.guild.iconURL()) statsEmbed.setThumbnail(interaction.guild.iconURL() || "")
        statsEmbed.setColor(config.EmbedColors)

        statsEmbed.addFields([
          { name: `***Tickets***`, value: `> ${config.Locale.totalTickets} \`\`${statsDB.totalTickets.toLocaleString('en-US')}\`\`\n> ${config.Locale.openTickets} \`\`${statsDB.openTickets}\`\`\n> ${config.Locale.totalClaims} \`\`${statsDB.totalClaims.toLocaleString('en-US')}\`\`` },
        ]);

        statsEmbed.setTimestamp()
        statsEmbed.setFooter({ text: `Requested by: ${interaction.user.username}`, iconURL: `${interaction.user.displayAvatarURL({ dynamic: true })}` })
        interaction.editReply({ embeds: [statsEmbed] })

    }

}