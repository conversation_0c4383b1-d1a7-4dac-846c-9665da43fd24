// Wotry (7656119822070575) - [Header/Title Style, like in the screenshot]

const { EmbedBuilder, SlashCommandBuilder } = require("discord.js");
const fetch = require("node-fetch");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

// Discord Information
// • User: <@430483970>
// • Discord ID: 430483970

// Steam Information
// • Steam ID: 7656119822070575
// • Steam Profile: Click Me (Link Style)

module.exports = {
  enabled: true,
  data: new SlashCommandBuilder()
    .setName("search-user")
    .setDescription("Check to see if a user is linked")
    .addStringOption((option) =>
      option
        .setName("steamid")
        .setDescription("Steam 64 ID of user you want to check")
        .setRequired(false)
    )
    .addUserOption((option) =>
      option
        .setName("user")
        .setDescription("Discord user you want to check links for")
        .setRequired(false)
    ),

  // Link Information
  // • Linked Date: 4 years ago

  async execute(interaction) {
    const embed = new EmbedBuilder();
    const steamID = interaction.options.getString("steamid");
    const discordUser = interaction.options.getUser("user");

    // ✅ If no input is provided, return total verified users
    if (!steamID && !discordUser) {
      try {
        const res = await fetch(
          `${config.WEBSITE_URL}api?action=count&secret=${config.SECRET_KEY}`
        );
        const response = await res.json();

        return interaction.reply({
          embeds: [embed.setDescription(`There are currently ${response.Total} users verified!`)],
        });
      } catch (err) {
        console.error("Error fetching user count:", err);
        return interaction.reply({
          content: "❌ Failed to fetch user count.",
          ephemeral: true,
        });
      }
    }

    // ✅ If a Discord user is provided, search by Discord ID
    if (discordUser) {
      try {
        const res = await fetch(
          `${config.WEBSITE_URL}api?action=findByDiscord&id=${discordUser.id}&secret=${config.SECRET_KEY}`
        );
        const response = await res.text();

        if (response.toLowerCase().includes("no users"))
          return interaction.reply("❌ No linked user found.");

        let steamID = response;
        let resultEmbed = new EmbedBuilder()
          .setAuthor({
            name: "Link Results | Steam Found",
            iconURL: config.LOGO_URL,
          })
          .setColor("#4286f4")
          .setDescription(
            `Located the Steam ID **${steamID}** for the Discord ID **${discordUser.tag}**.`
          )
          .addFields(
            { name: "Steam ID", value: steamID, inline: false },
            { name: "Discord ID", value: discordUser.id, inline: false },
            {
              name: "Links",
              value: `[[BattleMetrics]](https://www.battlemetrics.com/players?filter%5Bsearch%5D=${steamID})\n[[Steam Profile]](https://steamcommunity.com/profile/${steamID})`,
              inline: false,
            }
          )
          .setFooter({
            text: config.SERVER_NAME,
            iconURL: config.LOGO_URL,
          });

        return interaction.reply({ embeds: [resultEmbed] });
      } catch (err) {
        console.error("Error fetching Steam ID:", err);
        return interaction.reply({
          content: "❌ Error retrieving user data.",
          ephemeral: true,
        });
      }
    }

    // ✅ If a Steam ID is provided, search by Steam ID
    if (steamID) {
      try {
        const res = await fetch(
          `${config.WEBSITE_URL}api?action=findBySteam&id=${steamID}&secret=${config.SECRET_KEY}`
        );
        const response = await res.text();

        if (response.toLowerCase().includes("no users"))
          return interaction.reply("❌ No linked user found.");

        let discordID = response;
        let resultEmbed = new EmbedBuilder()
          .setAuthor({
            name: "Link Results | Discord Found",
            iconURL: config.LOGO_URL,
          })
          .setColor("#4286f4")
          .setDescription(
            `Located the Discord ID **${discordID}** for the Steam ID **${steamID}**.`
          )
          .addFields(
            { name: "Steam ID", value: steamID, inline: false },
            { name: "Discord ID", value: discordID, inline: false },
            {
              name: "Links",
              value: `[[BattleMetrics]](https://www.battlemetrics.com/players?filter%5Bsearch%5D=${steamID})\n[[Steam Profile]](https://steamcommunity.com/profile/${steamID})`,
              inline: false,
            }
          )
          .setFooter({
            text: config.SERVER_NAME,
            iconURL: config.LOGO_URL,
          });

        return interaction.reply({ embeds: [resultEmbed] });
      } catch (err) {
        console.error("Error fetching Discord ID:", err);
        return interaction.reply({
          content: "❌ Error retrieving user data.",
          ephemeral: true,
        });
      }
    }

    // Useful Information
    // • BM: Here (Link Style)
    // • Tebex: Here (Link Style)
  },
};