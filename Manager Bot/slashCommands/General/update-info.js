const { SlashCommandBuilder } = require('@discordjs/builders');
const Discord = require ("discord.js")
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const utils = require("../../utils.js");
const Users = require("../../models/Users");
const logsHook = new Discord.WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` })

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('update-info')
        .setDescription(`Update information assosiated with your account.`),
    async execute(interaction, client) {
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
        await interaction.deferReply({ ephemeral: true });
        interaction.editReply({ content: `*Making sure you have an account.*` })

        const userInter = interaction.user
        const user = await Users.findOne({ discord_id: userInter.id })
        if (!user) return interaction.editReply({ content: `We have checked our systems, it seems you do not have an account. Please link your discord and/or make an account!`})
    
        if (user) {
            interaction.editReply({ content: `We have checked our systems, it seems you do have an account. Give us a few seconds.` });
            await delay(1000);
            interaction.editReply({ content: `We have checked our systems, it seems you do have an account. Give us a few seconds..` });
            await delay(1000);
            interaction.editReply({ content: `We have checked our systems, it seems you do have an account. Give us a few seconds...` });
            await delay(2000);
            interaction.editReply({ content: `Updating your information...` });

            const NewProfilePicture = userInter.avatarURL();
            const NewUsername = userInter.username;
            const OldUsername = user.discord_username
            const OldProfilePicture = user.discord_pfp

            if (NewUsername === OldUsername && NewProfilePicture === OldProfilePicture) return interaction.editReply({ content: 'No changes detected. Your username and profile picture remain the same.' });
            await delay(2000);

            user.discord_username = NewUsername
            user.discord_pfp = NewProfilePicture
            await user.save();

            const embed = new Discord.EmbedBuilder()
            .setTitle(`*Success*`)
            .setDescription(`***We have updated the following information based on the account linked to <@!${userInter.id}> ***\n \`\`\`New Username: ${NewUsername} \nNew Profile Picture: ${NewProfilePicture} \n\nPrevious Username: ${OldUsername} \nPrevious Profile Picture: ${OldProfilePicture} \`\`\` `)
            .setThumbnail(NewProfilePicture)
            .setTimestamp()

            interaction.editReply({ content: ``, embeds: [embed] });
            if (NewUsername !== OldUsername && NewProfilePicture !== OldProfilePicture) logsHook.send({ embeds: [embed] });
        }

    }

}