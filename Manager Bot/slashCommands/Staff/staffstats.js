const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const SharedStaffStats = require('../../models/sharedStaffStats');
const StaffManagerIntegration = require('../../utils/staffManagerIntegration');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('staffstats')
        .setDescription('View staff activity statistics from both bots')
        .addUserOption(option =>
            option.setName('staff')
                .setDescription('Specific staff member to view (leave empty for top performers)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('timeframe')
                .setDescription('Time period to view')
                .setRequired(false)
                .addChoices(
                    { name: 'This Week', value: 'week' },
                    { name: 'This Month', value: 'month' },
                    { name: 'All Time', value: 'all' }
                )),

    async execute(interaction) {
        await interaction.deferReply();

        try {
            const targetUser = interaction.options.getUser('staff');
            const timeframe = interaction.options.getString('timeframe') || 'week';
            const staffIntegration = new StaffManagerIntegration(interaction.client);

            if (targetUser) {
                // Show specific staff member stats
                await showIndividualStats(interaction, targetUser, timeframe, staffIntegration);
            } else {
                // Show top staff stats
                await showTopStaffStats(interaction, timeframe, staffIntegration);
            }

        } catch (error) {
            console.error('Error in staffstats command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while fetching staff statistics.',
                ephemeral: true
            });
        }
    }
};

async function showIndividualStats(interaction, user, timeframe, staffIntegration) {
    try {
        // Check if user is staff
        const isStaff = await staffIntegration.isStaffMember(user.id);
        if (!isStaff) {
            return await interaction.editReply({
                content: '❌ The specified user is not a staff member.',
                ephemeral: true
            });
        }

        // Get staff stats
        const stats = await staffIntegration.getStaffStats(user.id);
        if (!stats) {
            return await interaction.editReply({
                content: '❌ No statistics found for this staff member.',
                ephemeral: true
            });
        }

        // Build stats based on timeframe
        let displayStats = {
            tickets: 0,
            messages: 0,
            bans: 0,
            voice: 0,
            ingame: 0
        };

        switch (timeframe) {
            case 'week':
                displayStats.tickets = stats.tickets?.weekly || 0;
                displayStats.messages = stats.messages?.weekly || 0;
                displayStats.bans = stats.bans?.weekly || 0;
                displayStats.voice = stats.voice?.weeklyMinutes || 0;
                displayStats.ingame = stats.ingame?.weeklyMinutes || 0;
                break;
            case 'month':
                displayStats.tickets = stats.tickets?.monthly || 0;
                displayStats.messages = stats.messages?.monthly || 0;
                displayStats.bans = stats.bans?.monthly || 0;
                displayStats.voice = stats.voice?.monthlyMinutes || 0;
                displayStats.ingame = stats.ingame?.monthlyMinutes || 0;
                break;
            case 'all':
                displayStats.tickets = stats.tickets?.total || 0;
                displayStats.messages = stats.messages?.total || 0;
                displayStats.bans = stats.bans?.total || 0;
                displayStats.voice = stats.voice?.totalMinutes || 0;
                displayStats.ingame = stats.ingame?.totalMinutes || 0;
                break;
        }

        const embed = new EmbedBuilder()
            .setTitle(`📊 Staff Statistics - ${user.displayName}`)
            .setColor('#4CAF50')
            .setThumbnail(user.displayAvatarURL())
            .addFields([
                { 
                    name: '🎫 Tickets Claimed', 
                    value: `${displayStats.tickets}`, 
                    inline: true 
                },
                { 
                    name: '💬 Messages Sent', 
                    value: `${displayStats.messages}`, 
                    inline: true 
                },
                { 
                    name: '🔨 Bans Issued', 
                    value: `${displayStats.bans}`, 
                    inline: true 
                },
                { 
                    name: '🎤 Voice Activity', 
                    value: `${Math.round(displayStats.voice / 60)} hours`, 
                    inline: true 
                },
                { 
                    name: '🎮 In-Game Activity', 
                    value: `${Math.round(displayStats.ingame / 60)} hours`, 
                    inline: true 
                },
                { 
                    name: '⏱️ Total Activity', 
                    value: `${Math.round((displayStats.voice + displayStats.ingame) / 60)} hours`, 
                    inline: true 
                }
            ])
            .setFooter({ 
                text: `Timeframe: ${timeframe.charAt(0).toUpperCase() + timeframe.slice(1)} | Data from both Manager Bot and Staff Manager` 
            })
            .setTimestamp();

        // Add last activity info
        const lastActivities = [];
        if (stats.tickets?.lastClaimed) {
            lastActivities.push(`🎫 Last ticket: ${new Date(stats.tickets.lastClaimed).toLocaleDateString()}`);
        }
        if (stats.messages?.lastMessage) {
            lastActivities.push(`💬 Last message: ${new Date(stats.messages.lastMessage).toLocaleDateString()}`);
        }
        if (stats.bans?.lastBan) {
            lastActivities.push(`🔨 Last ban: ${new Date(stats.bans.lastBan).toLocaleDateString()}`);
        }

        if (lastActivities.length > 0) {
            embed.addFields([{
                name: '📅 Recent Activity',
                value: lastActivities.join('\n'),
                inline: false
            }]);
        }

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error showing individual stats:', error);
        await interaction.editReply({
            content: '❌ An error occurred while fetching individual staff stats.',
            ephemeral: true
        });
    }
}

async function showTopStaffStats(interaction, timeframe, staffIntegration) {
    try {
        // Get all staff stats
        const allStats = await staffIntegration.getAllStaffStats();
        
        if (allStats.length === 0) {
            return await interaction.editReply({
                content: '❌ No staff statistics found.',
                ephemeral: true
            });
        }

        // Process stats based on timeframe and sort
        const processedStats = allStats.map(stats => {
            let displayStats = {
                id: stats.staffDiscordId,
                tickets: 0,
                messages: 0,
                bans: 0,
                voice: 0,
                ingame: 0
            };

            switch (timeframe) {
                case 'week':
                    displayStats.tickets = stats.tickets?.weeklyClaimed || 0;
                    displayStats.messages = stats.messages?.weekly || 0;
                    displayStats.bans = stats.bans?.weekly || 0;
                    displayStats.voice = stats.voice?.weeklyMinutes || 0;
                    displayStats.ingame = stats.ingame?.weeklyMinutes || 0;
                    break;
                case 'month':
                    displayStats.tickets = stats.tickets?.monthlyClaimed || 0;
                    displayStats.messages = stats.messages?.monthly || 0;
                    displayStats.bans = stats.bans?.monthly || 0;
                    displayStats.voice = stats.voice?.monthlyMinutes || 0;
                    displayStats.ingame = stats.ingame?.monthlyMinutes || 0;
                    break;
                case 'all':
                    displayStats.tickets = stats.tickets?.totalClaimed || 0;
                    displayStats.messages = stats.messages?.total || 0;
                    displayStats.bans = stats.bans?.total || 0;
                    displayStats.voice = stats.voice?.totalMinutes || 0;
                    displayStats.ingame = stats.ingame?.totalMinutes || 0;
                    break;
            }

            displayStats.totalActivity = displayStats.tickets + displayStats.messages + displayStats.bans;
            displayStats.totalHours = Math.round((displayStats.voice + displayStats.ingame) / 60);

            return displayStats;
        }).filter(stats => stats.totalActivity > 0 || stats.totalHours > 0);

        // Sort by total activity
        processedStats.sort((a, b) => b.totalActivity - a.totalActivity);

        const embed = new EmbedBuilder()
            .setTitle(`📊 Top Staff Performance - ${timeframe.charAt(0).toUpperCase() + timeframe.slice(1)}`)
            .setColor('#2196F3')
            .setDescription('Staff performance rankings based on combined activity')
            .setTimestamp();

        // Add top performers
        const topPerformers = processedStats.slice(0, 10).map((staff, index) => {
            return `${index + 1}. <@${staff.id}> - 🎫${staff.tickets} 💬${staff.messages} 🔨${staff.bans} ⏱️${staff.totalHours}h`;
        }).join('\n');

        if (topPerformers) {
            embed.addFields([{
                name: '🏆 Top Performers',
                value: topPerformers,
                inline: false
            }]);
        }

        // Add summary statistics
        const totalStats = processedStats.reduce((acc, staff) => ({
            tickets: acc.tickets + staff.tickets,
            messages: acc.messages + staff.messages,
            bans: acc.bans + staff.bans,
            hours: acc.hours + staff.totalHours
        }), { tickets: 0, messages: 0, bans: 0, hours: 0 });

        embed.addFields([
            { name: '📈 Total Tickets', value: `${totalStats.tickets}`, inline: true },
            { name: '📈 Total Messages', value: `${totalStats.messages}`, inline: true },
            { name: '📈 Total Bans', value: `${totalStats.bans}`, inline: true },
            { name: '📈 Total Hours', value: `${totalStats.hours}`, inline: true },
            { name: '👥 Active Staff', value: `${processedStats.length}`, inline: true },
            { name: '📊 Data Source', value: 'Manager Bot + Staff Manager', inline: true }
        ]);

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('Error showing top staff stats:', error);
        await interaction.editReply({
            content: '❌ An error occurred while fetching staff statistics.',
            ephemeral: true
        });
    }
}
