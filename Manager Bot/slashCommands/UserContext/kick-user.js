const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, EmbedBuilder, ButtonBuilder, WebhookClient,  ContextMenuCommandBuilder, ApplicationCommandType } = require("discord.js");
const yaml = require("js-yaml")
const fs = require('fs');
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const UserData = require('../../models/userData')
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` });

module.exports = {
    enabled: true,
        data: new ContextMenuCommandBuilder()
        .setName('Kick')
        .setType(ApplicationCommandType.User),
    async execute(interaction, client) {
        if (!interaction.member.roles.cache.has(config.StaffRole)) return interaction.reply({ content: "You do not have permission to use this command.", ephemeral: true });
        const user = interaction.targetUser
        const member = interaction.guild.members.cache.get(user.id);
        let userData = await UserData.findOne({ userId: user.id });
        if (interaction.guildId !== config.GuildID) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        if (!userData) {
            userData = new UserData({
                userId: user.id,
                warns: 0,
                messages: 0,
                kicks: 1,
                bans: 0,
                timeouts: 0,
                note: "None",
            });
        } else {
            userData.kicks++;
        }

        await userData.save();
        const embed = new EmbedBuilder()
            .setTitle(`*Moderation Action*`)
            .setColor("#CCCCCC")
            .addFields([
                { name: `Details:`, value: `\`\`User:\`\` <@${user.id}> (${user.tag}) \n\`\`Reason:\`\` Reason can not be set due to context menu being used. \n\`\`Staff:\`\` <@${interaction.user.id}>` },
            ])
            .setTimestamp()
            .setFooter({ text: `${user.tag}`, iconURL: `${user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 })}` })
        member.send({ embeds: [embed] })
        interaction.reply({ content: `Kicked ${user.tag} for ${reason}`, ephemeral: true });
        setTimeout(() => {
            logsHook.send({ embeds: [embed] });
            member.kick(reason)
        }, 3000);
    }
}