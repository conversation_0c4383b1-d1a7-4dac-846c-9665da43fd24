const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, EmbedBuilder, ButtonBuilder, WebhookClient, ContextMenuCommandBuilder, ApplicationCommandType } = require("discord.js");
const moment = require('moment');
const UserData = require('../../models/userData');
const logsHook = new WebhookClient({ url: `https://discord.com/api/webhooks/1335816241901735936/lAuqzvIMrXFZDRyNnq5jA-FNR5-YupdpR4fmj_2oay6_vL2-z1h7m3JyhnrEYkS6LyKe` });

module.exports = {
    enabled: true,
        data: new ContextMenuCommandBuilder()
        .setName('Check')
        .setType(ApplicationCommandType.User),
}