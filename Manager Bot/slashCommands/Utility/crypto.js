const { SlashCommandBuilder } = require('@discordjs/builders');
const { Discord, ActionRowBuilder, EmbedBuilder, ButtonBuilder } = require("discord.js");
const fs = require('fs');
const yaml = require("js-yaml")
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const commands = yaml.load(fs.readFileSync('./commands.yml', 'utf8'))
const fetch = require("node-fetch");
const ticketModel = require("../../models/ticketModel");

module.exports = {
    enabled: commands.Utility.Crypto.Enabled,
    data: new SlashCommandBuilder()
        .setName('crypto')
        .setDescription(commands.Utility.Crypto.Description)
        .addUserOption((option) => option.setName('user').setDescription('User').setRequired(true))
        .addStringOption((option) => option.setName('currency').setDescription('Crypto Currency to pay in').addChoices(
            { name: 'BTC', value: 'BTC' }, 
            { name: 'ETH', value: 'ETH' }, 
            { name: 'USDT', value: 'USDT' },
            { name: 'LTC', value: 'LTC' },
        ).setRequired(true))
                .addNumberOption((option) => option.setName('price').setDescription(`Price in ${config.CryptoSettings.Currency}`).setRequired(true))
                .addStringOption(option => option.setName('service').setDescription('Service').addChoices(
                  { name: 'API', value: 'API' },
              ).setRequired(true))
                .addStringOption((option) => option.setName('address').setDescription('Wallet Address')),
    async execute(interaction, client) {
      await interaction.deferReply({ ephemeral: true });
      const ticketDB = await ticketModel.findOne({ channelID: interaction.channel.id });

        if(config.CryptoSettings.Enabled === false) return interaction.editReply({ content: "This command has been disabled in the config!", ephemeral: true })
        if (config.CryptoSettings.OnlyInTicketChannels && !ticketDB) return interaction.editReply({ content: config.Locale.NotInTicketChannel, ephemeral: true })
    
        let doesUserHaveRole = false
        for(let i = 0; i < config.CryptoSettings.AllowedRoles.length; i++) {
            role = interaction.guild.roles.cache.get(config.CryptoSettings.AllowedRoles[i]);
            if(role && interaction.member.roles.cache.has(config.CryptoSettings.AllowedRoles[i])) doesUserHaveRole = true;
          }
        if(doesUserHaveRole === false) return interaction.editReply({ content: config.Locale.NoPermsMessage, ephemeral: true })
        if (interaction.guildId !== config.GuildID) {
          return interaction.editReply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
      }


        let user = interaction.options.getUser("user");
        let currency = interaction.options.getString("currency")
        let price = interaction.options.getInteger("price");
        let service = interaction.options.getString("service");
        let address = interaction.options.getString("address");
    
        let address2 = ""

        if(address) address2 = address
        if(!address) address2 = address

        if(!address && currency === "BTC") address2 = config.CryptoAddresses.BTC
        if(!address && currency === "ETH") address2 = config.CryptoAddresses.ETH
        if(!address && currency === "USDT") address2 = config.CryptoAddresses.USDT
        if(!address && currency === "LTC") address2 = config.CryptoAddresses.LTC
    
        if(currency === "BTC" && !config.CryptoAddresses.BTC) return interaction.editReply({ content: "BTC address has not been specified in the config!", ephemeral: true })
        if(currency === "ETH" && !config.CryptoAddresses.ETH) return interaction.editReply({ content: "ETH address has not been specified in the config!", ephemeral: true })
        if(currency === "USDT" && !config.CryptoAddresses.USDT) return interaction.editReply({ content:"USDT address has not been specified in the config!", ephemeral: true })
        if(currency === "LTC" && !config.CryptoAddresses.LTC) return interaction.editReply({ content: "LTC address has not been specified in the config!", ephemeral: true })


        // TESTING
        const fromCurrency = currency;
        const toCurrency = config.CryptoSettings.Currency;
        const conversionMethod = client.cryptoConvert[toCurrency][fromCurrency];
        const amount = price
        const convertedAmount = conversionMethod(amount);

        if(currency === "BTC") cryptoFullName = "bitcoin"
        if(currency === "ETH") cryptoFullName = "ethereum"
        if(currency === "USDT") cryptoFullName = "tether"
        if(currency === "LTC") cryptoFullName = "litecoin"

        if(service === "API") serviceMessage = "Your payment for your API key has been generated.\n> Please proceed with the payment and we will generate your API once completed."

        const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setStyle('Link')
                .setURL(`https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=${cryptoFullName.toLowerCase()}:${address2}?amount=${convertedAmount}`) 
                .setLabel(config.Locale.cryptoQRCode))
    
        const embed = new EmbedBuilder()
        .setTitle(`${service} Payment (${currency.toUpperCase()})`)
        .setColor(config.EmbedColors)
        .setDescription(`> • ***${config.Locale.suggestionInformation}***\n> \`\`\`Client: ${user.username}\n> ${config.Locale.PayPalPrice} ${convertedAmount} (${price} ${config.CryptoSettings.Currency})\`\`\` `)
        .addFields([
            { name: `• ***Services***`, value: `> \`\`\`${serviceMessage}\`\`\`` },
            { name: `• ***${config.Locale.cryptoLogAddress}***`, value: ` || ${address2} || ` },
          ])
        .setFooter({ text: `${user.username}`, iconURL: `${user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 })}` })
        .setTimestamp()

        interaction.editReply({ content: "Successfully created a crypto payment!" });
        interaction.channel.send({ content:`||<@!${user.id}>||`, embeds: [embed], components: [row] })
    
        let logsChannel; 
        if(!config.cryptoPayments.ChannelID) logsChannel = interaction.guild.channels.cache.get(config.TicketSettings.LogsChannelID);
        if(config.cryptoPayments.ChannelID) logsChannel = interaction.guild.channels.cache.get(config.cryptoPayments.ChannelID);

        const log = new EmbedBuilder()
        .setColor("Green")
        .setTitle(config.Locale.cryptoLogTitle)
        .addFields([
            { name: `• ${config.Locale.logsExecutor}`, value: `> <@!${interaction.user.id}>\n> ${interaction.user.username}` },
            { name: `• ${config.Locale.PayPalUser}`, value: `> <@!${user.id}>\n> ${user.username}` },
            { name: `• ${config.Locale.PayPalPrice}`, value: `> ${config.CryptoSettings.CurrencySymbol}${price}\n> ${price} ${currency}` },
            { name: `• ${config.Locale.PayPalService}`, value: `> ${service}` },
          ])
        .setTimestamp()
        .setThumbnail(interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 }))
        .setFooter({ text: `${interaction.user.username}`, iconURL: `${interaction.user.displayAvatarURL({ format: 'png', dynamic: true, size: 1024 })}` })
        if (logsChannel && config.cryptoPayments.Enabled) logsChannel.send({ embeds: [log] })
    

    }

}