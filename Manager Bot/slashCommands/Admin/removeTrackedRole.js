const { SlashCommandBuilder } = require("discord.js");
const TrackedRole = require("../../models/TrackedRole");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("removetrackedrole")
        .setDescription("Remove a tracked role from status updates.")
        .addRoleOption(option =>
            option.setName("role")
                .setDescription("The role to remove from tracking")
                .setRequired(true)),

    async execute(interaction) {
        if (!interaction.member.permissions.has("Administrator")) {
            return interaction.reply({ content: "❌ You need **Administrator** permissions to use this command.", ephemeral: true });
        }

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const role = interaction.options.getRole("role");

        let existingRole = await TrackedRole.findOne({ roleID: role.id });
        if (!existingRole) {
            return interaction.reply({ content: "⚠️ This role is not currently being tracked!", ephemeral: true });
        }

        await TrackedRole.deleteOne({ roleID: role.id });

        return interaction.reply({ content: `✅ Stopped tracking the **${role.name}** role.` });
    }
};
