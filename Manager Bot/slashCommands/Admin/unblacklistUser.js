const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, WebhookClient } = require("discord.js");
const Blacklist = require("../../models/blacklistUser");
const fs = require("fs");
const yaml = require("js-yaml");
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');

// Initialize webhooks array with a null check
const webhooks = Array.isArray(config.webhookURLs) ? config.webhookURLs.map(url => new WebhookClient({ url })) : [];

async function sendToAllWebhooks(embed) {
    // If there are no webhooks configured, just log a message and return
    if (webhooks.length === 0) {
        console.log("No webhooks configured for unblacklist notifications");
        return;
    }

    // Send to all configured webhooks
    for (const webhook of webhooks) {
        if (webhook) {
            await webhook.send({ embeds: [embed] }).catch(err => console.error(`❌ Webhook Error: ${err.message}`));
        }
    }
}

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("unblacklist")
        .setDescription("Remove a user from the blacklist and unban them.")
        .addStringOption(option =>
            option.setName("userid")
                .setDescription("The Discord ID of the user to unblacklist")
                .setRequired(true)
        ),
    async execute(interaction) {
        if (!interaction.member.permissions.has("Administrator")) {
            return interaction.reply({ content: "You need Administrator permissions to use this command.", ephemeral: true });
        }

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const userID = interaction.options.getString("userid");

        // Check if user is blacklisted
        const existingBlacklist = await Blacklist.findOne({ userID });
        if (!existingBlacklist) {
            return interaction.reply({ content: `⚠️ This user is not blacklisted.`, ephemeral: true });
        }

        // Remove from database
        await Blacklist.deleteOne({ userID });

        // ✅ **Unban from mapped guilds**
        let unbannedGuilds = [];

        // Check if guildBlacklistMapping exists and is an object
        if (config.guildBlacklistMapping && typeof config.guildBlacklistMapping === 'object') {
            for (const [guildID, guildName] of Object.entries(config.guildBlacklistMapping)) {
                const guild = interaction.client.guilds.cache.get(guildID);
                if (!guild) continue;

                // Check if the user is banned in the guild
                const bans = await guild.bans.fetch().catch(() => null);
                if (bans && bans.has(userID)) {
                    await guild.bans.remove(userID, "User unblacklisted").catch(() => null);
                    unbannedGuilds.push(guildName);
                }
            }
        }

        // ✅ **Logging**
        const embed = new EmbedBuilder()
            .setTitle("User Unblacklisted")
            .setColor("Green")
            .setDescription(`**User ID:** ${userID}`)
            .setTimestamp();

        await sendToAllWebhooks(embed);
        return interaction.reply({ embeds: [embed] });
    }
};
