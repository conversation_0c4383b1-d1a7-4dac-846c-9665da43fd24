const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, Modal<PERSON>uilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, StringSelectMenuBuilder, EmbedBuilder, AttachmentBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("embed")
        .setDescription("Sending linking embed"),
    async execute(interaction) {

        const linkingURL = config.LinkingSystem.linkingWebsite;
       
        const embed = new EmbedBuilder()
            .setTitle("Account Linking Instructions")
            .setDescription('```Click the "Link your Account" button. \n\nStep 1: Login with Steam. \n\nStep 2: Login with Discord \n\nYou have completed the linking process! ```')
            .setColor("#FFFFFF");

        const button = new ButtonBuilder()
            .setLabel("Link Your Account")
            .setStyle(ButtonStyle.Link)
            .setURL(linkingURL);

        const row = new ActionRowBuilder().addComponents(button);

        await interaction.channel.send({ embeds: [embed], components: [row] });
        await interaction.reply({ content: "Embed has been sent.", ephemeral: true });
    }
};
