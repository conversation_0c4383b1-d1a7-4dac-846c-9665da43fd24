const { <PERSON>lash<PERSON>ommandBuilder, EmbedBuilder, WebhookClient, PermissionFlagsBits } = require("discord.js");
const Blacklist = require("../../models/blacklistUser");
const fs = require("fs");
const yaml = require("js-yaml");
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');

// Initialize webhooks array with a null check
const webhooks = Array.isArray(config.webhookURLs) ? config.webhookURLs.map(url => new WebhookClient({ url })) : [];

async function sendToAllWebhooks(embed) {
    // If there are no webhooks configured, just log a message and return
    if (webhooks.length === 0) {
        console.log("No webhooks configured for blacklist notifications");
        return;
    }

    // Send to all configured webhooks
    for (const webhook of webhooks) {
        if (webhook) {
            await webhook.send({ embeds: [embed] }).catch(err => console.error(`❌ Webhook Error: ${err.message}`));
        }
    }
}

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("blacklist")
        .setDescription("Blacklist a user from specific servers.")
        .addStringOption(option =>
            option.setName("userid")
                .setDescription("The Discord ID of the user to blacklist")
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName("reason")
                .setDescription("Reason for the blacklist")
                .setRequired(false)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator), // Requires Admin permissions
    async execute(interaction) {
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            return interaction.reply({ content: "You need **Administrator** permissions to use this command.", ephemeral: true });
        }

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const userID = interaction.options.getString("userid");
        const reason = interaction.options.getString("reason") || "No reason provided";

        // ✅ Check if the user is already blacklisted
        const existingBlacklist = await Blacklist.findOne({ userID });
        if (existingBlacklist) {
            return interaction.reply({ content: `**User is already blacklisted** for: ${existingBlacklist.reason}`, ephemeral: true });
        }

        // ✅ Save user to the blacklist database
        await Blacklist.create({ userID, reason, userWhoBlacklisted: interaction.user.id, alreadyLogged: false });

        // ✅ Loop through mapped guilds and ban user **only from those**
        // Check if guildBlacklistMapping exists and is an object
        if (config.guildBlacklistMapping && typeof config.guildBlacklistMapping === 'object') {
            for (const [guildID] of Object.entries(config.guildBlacklistMapping)) {
                const guild = interaction.client.guilds.cache.get(guildID);
                if (!guild) {
                    continue; // Skip if guild is not found
                }

                const member = await guild.members.fetch(userID).catch(() => null);
                if (member) {
                    await member.ban(`Blacklisted: ${reason}`).catch(() => {
                    });
                }
            }
        }

        // ✅ Logging Embed
        const embed = new EmbedBuilder()
            .setTitle("User Blacklisted")
            .setColor("Red")
            .setDescription(`**User ID:** ${userID}\n**Reason:** ${reason}`)
            .setTimestamp();

        await sendToAllWebhooks(embed);
        return interaction.reply({ embeds: [embed] });
    }
};
