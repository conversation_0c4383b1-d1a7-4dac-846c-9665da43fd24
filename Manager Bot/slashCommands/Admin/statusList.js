const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const TrackedUser = require("../../models/UserCountry");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("statuslist")
        .setDescription("Displays the real-time status of tracked users."),

    async execute(interaction) {

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const users = await TrackedUser.find();
        if (users.length === 0) {
            return interaction.reply({ content: "No users are currently being tracked.", ephemeral: true });
        }

        const embed = new EmbedBuilder()
            .setTitle("User Status Tracker")
            .setColor("Blue")
            .setTimestamp();

        let description = "";
        for (const user of users) {
            const member = await interaction.guild.members.fetch(user.userID).catch(() => null);
            if (!member) continue;

            let status = "❔ Unknown";
            switch (member.presence?.status) {
                case "online": status = "🟢 Online"; break;
                case "idle": status = "🟠 Idle"; break;
                case "dnd": status = "🔴 Do Not Disturb"; break;
                case "offline": status = "⚫ Offline"; break;
            }

            description += `:flag_${user.country.toLowerCase()}: | <@${member.user.id}> ${status}:\n`;
        }

        embed.setDescription(description || "No tracked users found.");
        interaction.reply({ embeds: [embed] });
    }
};
