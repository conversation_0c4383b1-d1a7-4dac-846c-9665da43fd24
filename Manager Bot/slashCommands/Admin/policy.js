const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("policy")
        .setDescription("Send the policy acceptance embed"),
    async execute(interaction) {
        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const embed = new EmbedBuilder()
            .setTitle("Policy Agreement")
            .setDescription(config.PolicyMessage)
            .setColor("#00f0c9")
            .setFooter({ text: "Your agreement will be recorded." });

        const button = new ButtonBuilder()
            .setCustomId("acceptPolicy")
            .setLabel("Accept Policy")
            .setStyle(ButtonStyle.Success);

        const row = new ActionRowBuilder().addComponents(button);

        await interaction.reply({ content: `Policy embed has been sent!`, ephemeral: true})

        await interaction.channel.send({ embeds: [embed], components: [row] });
    },
};
