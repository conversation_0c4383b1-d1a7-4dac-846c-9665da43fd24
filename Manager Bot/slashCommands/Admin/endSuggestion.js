const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PermissionFlagsBits } = require("discord.js");
const Suggestion = require("../../models/suggestion");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("endsuggestion")
        .setDescription("Ends a suggestion by marking it as closed.")
        .addStringOption(option =>
            option.setName("message_id")
                .setDescription("The message ID of the suggestion to close")
                .setRequired(true)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages),

    async execute(interaction) {
        const messageID = interaction.options.getString("message_id");
        const guildID = interaction.guild.id;

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const suggestion = await Suggestion.findOne({ messageID, guildID });

        if (!suggestion) {
            return interaction.reply({ content: "❌ Suggestion not found in this server!", ephemeral: true });
        }

        // ✅ Check if the user is the author or an admin
        if (interaction.user.id !== suggestion.userID && !interaction.member.permissions.has(PermissionFlagsBits.ManageMessages)) {
            return interaction.reply({ content: "❌ You do not have permission to close this suggestion!", ephemeral: true });
        }

        // ✅ Update status in the database
        suggestion.status = "Closed";
        await suggestion.save();

        // ✅ Edit the original embed
        const channel = interaction.guild.channels.cache.get(suggestion.channelID);
        if (!channel) {
            return interaction.reply({ content: "❌ Suggestion channel not found!", ephemeral: true });
        }

        try {
            const suggestionMessage = await channel.messages.fetch(messageID);

            // ✅ Remove all reactions
            await suggestionMessage.reactions.removeAll().catch(error => console.error("❌ Failed to remove reactions:", error));

            const embed = EmbedBuilder.from(suggestionMessage.embeds[0])
                .setColor("#FFFFFF")
                .setFooter({ text: "Suggestion Closed" })
                .addFields(
                    { name: "Upvotes", value: `${suggestion.upvotes}`, inline: true },
                    { name: "Downvotes", value: `${suggestion.downvotes}`, inline: true }
                );

            await suggestionMessage.edit({ embeds: [embed] });
            return interaction.reply({ content: "Suggestion has been closed and reactions have been removed.", ephemeral: true });
        } catch (error) {
            console.error("❌ Error updating suggestion:", error);
            return interaction.reply({ content: "❌ Failed to update the suggestion message.", ephemeral: true });
        }
    }
};
