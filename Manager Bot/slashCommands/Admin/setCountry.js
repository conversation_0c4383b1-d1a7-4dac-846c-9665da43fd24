const { SlashCommandBuilder } = require("discord.js");
const UserCountry = require("../../models/UserCountry");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("setcountry")
        .setDescription("Set a user's country for the status tracker.")
        .addUserOption(option =>
            option.setName("user")
                .setDescription("The user to assign a country to")
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName("country")
                .setDescription("Country name (e.g., US, Canada, UK)")
                .setRequired(true)
        ),
    async execute(interaction) {
        if (!interaction.member.permissions.has("Administrator")) {
            return interaction.reply({ content: "You need Administrator permissions to use this command.", ephemeral: true });
        }

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const user = interaction.options.getUser("user");
        const country = interaction.options.getString("country");

        await UserCountry.findOneAndUpdate(
            { userID: user.id },
            { country },
            { upsert: true, new: true }
        );

        return interaction.reply({ content: `✅ Set country for ${user.username} to **${country}**.`, ephemeral: true });
    }
};
