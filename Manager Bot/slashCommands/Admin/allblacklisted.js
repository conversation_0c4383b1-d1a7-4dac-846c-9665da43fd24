const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, <PERSON>bedBuilder, PermissionFlagsBits, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");
const Blacklist = require("../../models/blacklistUser");
const moment = require("moment");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("blacklistinfo")
        .setDescription("View all blacklisted users and their details."),
    async execute(interaction) {
        if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
            return interaction.reply({ content: "❌ You need **Administrator** permissions to use this command.", ephemeral: true });
        }

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const blacklistedUsers = await Blacklist.find();

        if (blacklistedUsers.length === 0) {
            return interaction.reply({ content: "✅ There are **no blacklisted users**.", ephemeral: true });
        }

        // 🔹 **Pagination Setup**
        const itemsPerPage = 25; // Change this if you want more per page
        let currentPage = 0;

        function generateEmbed(page) {
            const embed = new EmbedBuilder()
                .setTitle("Blacklisted Users")
                .setColor("Red")
                .setFooter({ text: `Page ${page + 1} of ${Math.ceil(blacklistedUsers.length / itemsPerPage)}` })
                .setTimestamp();

            const start = page * itemsPerPage;
            const end = start + itemsPerPage;
            const paginatedUsers = blacklistedUsers.slice(start, end);

            paginatedUsers.forEach((user, index) => {
                embed.addFields({
                    name: `🔹 **User ${start + index + 1}**`,
                    value: `\`\`\`User ID: ${user.userID}\nReason: ${user.reason}\nBlacklisted By: ${user.userWhoBlacklisted || "Unknown"}\nDate: ${moment(user.timestamp).format("MMMM Do YYYY, h:mm:ss A")}\`\`\``,
                    inline: false
                });
            });

            return embed;
        }

        // 🔹 **Pagination Buttons**
        const row = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId("previous")
                .setLabel("⬅️ Previous")
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === 0),
            new ButtonBuilder()
                .setCustomId("next")
                .setLabel("➡️ Next")
                .setStyle(ButtonStyle.Primary)
                .setDisabled(blacklistedUsers.length <= itemsPerPage)
        );

        const message = await interaction.reply({ embeds: [generateEmbed(currentPage)], components: [row], fetchReply: true });

        // 🔹 **Button Interaction Handling**
        const collector = message.createMessageComponentCollector({ time: 60000 });

        collector.on("collect", async (buttonInteraction) => {
            if (buttonInteraction.user.id !== interaction.user.id) {
                return buttonInteraction.reply({ content: "❌ You cannot interact with this menu.", ephemeral: true });
            }

            if (buttonInteraction.customId === "previous" && currentPage > 0) {
                currentPage--;
            } else if (buttonInteraction.customId === "next" && currentPage < Math.ceil(blacklistedUsers.length / itemsPerPage) - 1) {
                currentPage++;
            }

            await buttonInteraction.update({
                embeds: [generateEmbed(currentPage)],
                components: [
                    new ActionRowBuilder().addComponents(
                        new ButtonBuilder()
                            .setCustomId("previous")
                            .setLabel("⬅️ Previous")
                            .setStyle(ButtonStyle.Primary)
                            .setDisabled(currentPage === 0),
                        new ButtonBuilder()
                            .setCustomId("next")
                            .setLabel("➡️ Next")
                            .setStyle(ButtonStyle.Primary)
                            .setDisabled(currentPage === Math.ceil(blacklistedUsers.length / itemsPerPage) - 1)
                    )
                ]
            });
        });

        collector.on("end", () => {
            message.edit({ components: [] }).catch(() => {});
        });
    }
};
