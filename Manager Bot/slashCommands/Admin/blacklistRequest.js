const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require("discord.js");
const BlacklistRequest = require("../../models/BlacklistRequest");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("blacklistrequest")
        .setDescription("Request a user to be blacklisted.")
        .addStringOption(option =>
            option.setName("userid")
                .setDescription("The Discord ID of the user to blacklist")
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName("reason")
                .setDescription("Reason for the blacklist request")
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName("evidence")
                .setDescription("Link to evidence (screenshots, videos, etc.)")
                .setRequired(true)
        )
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers), // Requires Mod+ permissions

    async execute(interaction) {
        const userID = interaction.options.getString("userid");
        const reason = interaction.options.getString("reason");
        const evidence = interaction.options.getString("evidence");

        if (interaction.guildId !== config.BotSettings.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        // ✅ **Save request to database**
        await BlacklistRequest.create({ userID, reason, evidence, requestedBy: interaction.user.id });

        // ✅ **Create an embed for review**
        const embed = new EmbedBuilder()
            .setTitle("Blacklist Request")
            .setColor("Orange")
            .setDescription(`A blacklist request has been submitted.`)
            .addFields(
                { name: "User ID", value: userID, inline: true },
                { name: "Reason", value: reason, inline: false },
                { name: "Evidence", value: `[View Evidence](${evidence})`, inline: false },
                { name: "Requested By", value: `<@${interaction.user.id}>`, inline: true }
            )
            .setTimestamp();

        // ✅ **Add Accept & Decline buttons**
        const row = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId(`accept_blacklist_${userID}`)
                .setLabel("✅ Accept")
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId(`decline_blacklist_${userID}`)
                .setLabel("❌ Decline")
                .setStyle(ButtonStyle.Danger)
        );

        await interaction.reply({ content: `||<@&1329591326324560044>||`, embeds: [embed], components: [row] });
    }
};
