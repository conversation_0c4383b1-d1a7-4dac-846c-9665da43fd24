const { SlashCommandBuilder } = require("discord.js");
const TrackedRole = require("../../models/TrackedRole");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("settrackedrole")
        .setDescription("Set a role to be tracked for status updates.")
        .addRoleOption(option =>
            option.setName("role")
                .setDescription("The role to track")
                .setRequired(true)),

    async execute(interaction) {
        if (!interaction.member.permissions.has("Administrator")) {
            return interaction.reply({ content: "❌ You need **Administrator** permissions to use this command.", ephemeral: true });
        }

        if (interaction.guildId !== config.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const role = interaction.options.getRole("role");

        let existingRole = await TrackedRole.findOne({ roleID: role.id });
        if (existingRole) {
            return interaction.reply({ content: "⚠️ This role is already being tracked!", ephemeral: true });
        }

        await TrackedRole.create({ roleID: role.id });

        return interaction.reply({ content: `✅ Now tracking the **${role.name}** role!` });
    }
};
