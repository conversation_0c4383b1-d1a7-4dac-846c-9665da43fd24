const { SlashCommandBuilder } = require("discord.js");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));
const Users = require("../../models/Users");

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("add-staff")
        .setDescription("Add Staff to a user on the panel")
        .addStringOption(option =>
            option.setName("discord_id")
                .setDescription("The Discord ID of the user")
                .setRequired(true)
        ),
    
    async execute(interaction) {
        if (interaction.guildId !== config.BotSettings.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const memberRoles = interaction.member.roles.cache.map(role => role.id);
        const hasPermission = config.LinkingSystem.keyRoles.some(roleId => memberRoles.includes(roleId));

        if (!hasPermission) {
            return interaction.reply({ content: `⛔ | You do not have permission to use this command.`, ephemeral: true });
        }

        const discordId = interaction.options.getString("discord_id");
        return addStaffToUser(discordId, interaction);
    },

    async prefixCommand(message, args) {
        if (!message.guild || message.author.bot) return;

        if (message.guild.id !== config.StaffGuild) {
            return message.reply("🔴 | This command can only be used in the specified guild.");
        }

        const memberRoles = message.member.roles.cache.map(role => role.id);
        const hasPermission = config.keyRoles.some(roleId => memberRoles.includes(roleId));

        if (!hasPermission) {
            return message.reply("⛔ | You do not have permission to use this command.");
        }

        if (!args[0]) {
            return message.reply("⚠️ | You must provide a **Discord ID** of the user.");
        }

        const discordId = args[0];
        return addStaffToUser(discordId, message);
    }
};

// ✅ Helper function to add staff
async function addStaffToUser(discordId, interaction) {
    try {
        const user = await Users.findOne({ discord_id: discordId });

        if (!user) {
            return interaction.reply
                ? interaction.reply({ content: `⚠️ | User not found in the database.`, ephemeral: true })
                : interaction.channel.send("⚠️ | User not found in the database.");
        }

        if (user.staff) {
            return interaction.reply
                ? interaction.reply({ content: `✅ | This user is already a staff member.`, ephemeral: true })
                : interaction.channel.send("✅ | This user is already a staff member.");
        }

        user.staff = true;
        await user.save();

        return interaction.reply
            ? interaction.reply({ content: `✅ | Successfully added **staff** status to the user!`, ephemeral: false })
            : interaction.channel.send("✅ | Successfully added **staff** status to the user!");
    } catch (error) {
        console.error("Error updating user staff status:", error);
        return interaction.reply
            ? interaction.reply({ content: `❌ | An error occurred while adding staff status.`, ephemeral: true })
            : interaction.channel.send("❌ | An error occurred while adding staff status.");
    }
}
