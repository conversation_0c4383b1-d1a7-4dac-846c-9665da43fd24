const { <PERSON><PERSON><PERSON><PERSON><PERSON>B<PERSON>er, Embed<PERSON><PERSON><PERSON>, PermissionFlagsBits } = require('discord.js');
// Use the config adapter for backward compatibility with the new config structure
const config = require('../../utils/configAdapter');
const BanMonitorConfig = require('../../models/banMonitorConfig');
const BanModel = require('../../models/banModel');

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName('banmonitor')
        .setDescription('Manage the BattleMetrics ban monitoring system')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Check the current status of the ban monitoring system'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('enable')
                .setDescription('Enable the ban monitoring system'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('disable')
                .setDescription('Disable the ban monitoring system'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Configure the ban monitoring system')
                .addStringOption(option =>
                    option.setName('banlist_id')
                        .setDescription('The BattleMetrics ban list ID to monitor')
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('interval')
                        .setDescription('Check interval in seconds (default: 60)')
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('color')
                        .setDescription('Hex color for embeds (default: from config)')
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('server_name')
                        .setDescription('Server name to display in embeds')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('webhooks')
                .setDescription('Manage webhooks for ban notifications')
                .addStringOption(option =>
                    option.setName('action')
                        .setDescription('Action to perform')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Add Staff Webhook', value: 'add_staff' },
                            { name: 'Add Public Webhook', value: 'add_public' },
                            { name: 'Remove Staff Webhook', value: 'remove_staff' },
                            { name: 'Remove Public Webhook', value: 'remove_public' },
                            { name: 'List All Webhooks', value: 'list' }
                        ))
                .addStringOption(option =>
                    option.setName('webhook_url')
                        .setDescription('The webhook URL (required for add actions)')
                        .setRequired(false))
                .addIntegerOption(option =>
                    option.setName('index')
                        .setDescription('The webhook index to remove (required for remove actions)')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('recent')
                .setDescription('Show recent bans')
                .addIntegerOption(option =>
                    option.setName('count')
                        .setDescription('Number of recent bans to show (default: 5, max: 10)')
                        .setRequired(false))),
    async execute(interaction, client) {
        await interaction.deferReply({ ephemeral: true });
        
        // Get or create ban monitor config
        let banConfig = await BanMonitorConfig.findOne();
        if (!banConfig) {
            banConfig = new BanMonitorConfig({
                enabled: false,
                banListId: '',
                staffWebhooks: [],
                publicWebhooks: [],
                embedColor: config.EmbedColors,
                serverName: config.SERVER_NAME
            });
            await banConfig.save();
        }
        
        const subcommand = interaction.options.getSubcommand();
        
        switch (subcommand) {
            case 'status':
                await handleStatusCommand(interaction, banConfig);
                break;
                
            case 'enable':
                banConfig.enabled = true;
                await banConfig.save();
                await interaction.editReply({ content: '✅ Ban monitoring system has been enabled.' });
                break;
                
            case 'disable':
                banConfig.enabled = false;
                await banConfig.save();
                await interaction.editReply({ content: '✅ Ban monitoring system has been disabled.' });
                break;
                
            case 'config':
                await handleConfigCommand(interaction, banConfig);
                break;
                
            case 'webhooks':
                await handleWebhooksCommand(interaction, banConfig);
                break;
                
            case 'recent':
                await handleRecentCommand(interaction);
                break;
        }
    }
};

/**
 * Handle the status subcommand
 */
async function handleStatusCommand(interaction, banConfig) {
    const banCount = await BanModel.countDocuments();
    
    const embed = new EmbedBuilder()
        .setTitle('Ban Monitor Status')
        .setColor(banConfig.embedColor || config.EmbedColors)
        .addFields(
            { name: 'Status', value: banConfig.enabled ? '✅ Enabled' : '❌ Disabled', inline: true },
            { name: 'Ban List ID', value: banConfig.banListId || 'Not set', inline: true },
            { name: 'Check Interval', value: `${banConfig.checkInterval / 1000} seconds`, inline: true },
            { name: 'Last Checked', value: banConfig.lastChecked ? `<t:${Math.floor(banConfig.lastChecked.getTime() / 1000)}:R>` : 'Never', inline: true },
            { name: 'Tracked Bans', value: `${banCount}`, inline: true },
            { name: 'Staff Webhooks', value: `${banConfig.staffWebhooks.length}`, inline: true },
            { name: 'Public Webhooks', value: `${banConfig.publicWebhooks.length}`, inline: true }
        )
        .setTimestamp();
        
    await interaction.editReply({ embeds: [embed] });
}

/**
 * Handle the config subcommand
 */
async function handleConfigCommand(interaction, banConfig) {
    const banListId = interaction.options.getString('banlist_id');
    const interval = interaction.options.getString('interval');
    const color = interaction.options.getString('color');
    const serverName = interaction.options.getString('server_name');
    
    let changes = [];
    
    if (banListId) {
        banConfig.banListId = banListId;
        changes.push(`Ban List ID set to \`${banListId}\``);
    }
    
    if (interval) {
        const intervalMs = parseInt(interval) * 1000;
        if (!isNaN(intervalMs) && intervalMs >= 10000) { // Minimum 10 seconds
            banConfig.checkInterval = intervalMs;
            changes.push(`Check interval set to ${interval} seconds`);
        } else {
            return await interaction.editReply({ content: '❌ Invalid interval. Must be at least 10 seconds.' });
        }
    }
    
    if (color) {
        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (hexColorRegex.test(color)) {
            banConfig.embedColor = color;
            changes.push(`Embed color set to \`${color}\``);
        } else {
            return await interaction.editReply({ content: '❌ Invalid color. Must be a valid hex color (e.g., #CCCCCC).' });
        }
    }
    
    if (serverName) {
        banConfig.serverName = serverName;
        changes.push(`Server name set to \`${serverName}\``);
    }
    
    if (changes.length === 0) {
        return await interaction.editReply({ content: 'No changes were made to the configuration.' });
    }
    
    await banConfig.save();
    await interaction.editReply({ content: `✅ Configuration updated:\n${changes.join('\n')}` });
}

/**
 * Handle the webhooks subcommand
 */
async function handleWebhooksCommand(interaction, banConfig) {
    const action = interaction.options.getString('action');
    const webhookUrl = interaction.options.getString('webhook_url');
    const index = interaction.options.getInteger('index');
    
    switch (action) {
        case 'add_staff':
            if (!webhookUrl) {
                return await interaction.editReply({ content: '❌ Webhook URL is required for this action.' });
            }
            
            if (!webhookUrl.startsWith('https://discord.com/api/webhooks/')) {
                return await interaction.editReply({ content: '❌ Invalid webhook URL. Must be a Discord webhook URL.' });
            }
            
            banConfig.staffWebhooks.push(webhookUrl);
            await banConfig.save();
            await interaction.editReply({ content: '✅ Staff webhook added successfully.' });
            break;
            
        case 'add_public':
            if (!webhookUrl) {
                return await interaction.editReply({ content: '❌ Webhook URL is required for this action.' });
            }
            
            if (!webhookUrl.startsWith('https://discord.com/api/webhooks/')) {
                return await interaction.editReply({ content: '❌ Invalid webhook URL. Must be a Discord webhook URL.' });
            }
            
            banConfig.publicWebhooks.push(webhookUrl);
            await banConfig.save();
            await interaction.editReply({ content: '✅ Public webhook added successfully.' });
            break;
            
        case 'remove_staff':
            if (index === undefined) {
                return await interaction.editReply({ content: '❌ Webhook index is required for this action.' });
            }
            
            if (index < 0 || index >= banConfig.staffWebhooks.length) {
                return await interaction.editReply({ content: '❌ Invalid webhook index.' });
            }
            
            banConfig.staffWebhooks.splice(index, 1);
            await banConfig.save();
            await interaction.editReply({ content: '✅ Staff webhook removed successfully.' });
            break;
            
        case 'remove_public':
            if (index === undefined) {
                return await interaction.editReply({ content: '❌ Webhook index is required for this action.' });
            }
            
            if (index < 0 || index >= banConfig.publicWebhooks.length) {
                return await interaction.editReply({ content: '❌ Invalid webhook index.' });
            }
            
            banConfig.publicWebhooks.splice(index, 1);
            await banConfig.save();
            await interaction.editReply({ content: '✅ Public webhook removed successfully.' });
            break;
            
        case 'list':
            const embed = new EmbedBuilder()
                .setTitle('Ban Monitor Webhooks')
                .setColor(banConfig.embedColor || config.EmbedColors)
                .setTimestamp();
                
            if (banConfig.staffWebhooks.length > 0) {
                const staffWebhooksList = banConfig.staffWebhooks.map((webhook, i) => 
                    `${i}: ${webhook.substring(0, 40)}...`
                ).join('\n');
                
                embed.addFields({ name: 'Staff Webhooks', value: staffWebhooksList || 'None' });
            } else {
                embed.addFields({ name: 'Staff Webhooks', value: 'None configured' });
            }
            
            if (banConfig.publicWebhooks.length > 0) {
                const publicWebhooksList = banConfig.publicWebhooks.map((webhook, i) => 
                    `${i}: ${webhook.substring(0, 40)}...`
                ).join('\n');
                
                embed.addFields({ name: 'Public Webhooks', value: publicWebhooksList || 'None' });
            } else {
                embed.addFields({ name: 'Public Webhooks', value: 'None configured' });
            }
            
            await interaction.editReply({ embeds: [embed] });
            break;
    }
}

/**
 * Handle the recent subcommand
 */
async function handleRecentCommand(interaction) {
    const count = Math.min(interaction.options.getInteger('count') || 5, 10);
    
    const recentBans = await BanModel.find()
        .sort({ createdAt: -1 })
        .limit(count);
        
    if (recentBans.length === 0) {
        return await interaction.editReply({ content: 'No bans have been recorded yet.' });
    }
    
    const embed = new EmbedBuilder()
        .setTitle('Recent Bans')
        .setColor(config.EmbedColors)
        .setTimestamp();
        
    for (const ban of recentBans) {
        const expiresText = ban.expiresAt 
            ? `<t:${Math.floor(ban.expiresAt.getTime() / 1000)}:R>`
            : 'Permanent';
            
        embed.addFields({
            name: `Ban ID: ${ban.banId}`,
            value: `**Player:** [${ban.steamId}](https://steamcommunity.com/profiles/${ban.steamId})\n` +
                   `**Server:** ${ban.serverName}\n` +
                   `**Reason:** ${ban.reason}\n` +
                   `**Expires:** ${expiresText}\n` +
                   `**Detected:** <t:${Math.floor(ban.createdAt.getTime() / 1000)}:R>`,
            inline: false
        });
    }
    
    await interaction.editReply({ embeds: [embed] });
}
