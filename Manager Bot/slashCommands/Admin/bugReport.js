const { SlashCommandBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, StringSelectMenuBuilder, EmbedBuilder, AttachmentBuilder } = require("discord.js");
const fs = require("fs");
const yaml = require("js-yaml");
const config = yaml.load(fs.readFileSync("./config.yml", "utf8"));

module.exports = {
    enabled: true,
    data: new SlashCommandBuilder()
        .setName("bugreport")
        .setDescription("Report a bug or issue"),
    async execute(interaction) {
        if (interaction.guildId !== config.BotSettings.StaffGuild) {
            return interaction.reply({ content: `🔴 | This command can only be used in the specified guild.`, ephemeral: true });
        }

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId("bug_type")
            .setPlaceholder("Select the type of bug")
            .addOptions([
                {
                    label: "In Game Plugin / Glitch",
                    description: "Report a game-related bug",
                    value: "ingame_bug",
                },
                {
                    label: "Discord Bot Glitch",
                    description: "Report a bug in the Discord bot",
                    value: "discord_bug",
                }
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        await interaction.reply({
            content: "Please select the type of bug you are reporting:",
            components: [row],
            ephemeral: true,
        });
    }
};
