/**
 * Bot Auto-Restart Script
 *
 * This script starts the bot and automatically restarts it if it crashes.
 * It also logs crashes and restarts to a file.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MAX_RESTARTS = 10; // Maximum number of restarts in a period
const RESTART_PERIOD = 60 * 60 * 1000; // 1 hour in milliseconds
const RESTART_DELAY = 5000; // 5 seconds delay before restarting
const LOG_FILE = path.join(__dirname, 'logs', 'bot-restarts.log');

// Ensure logs directory exists
if (!fs.existsSync(path.join(__dirname, 'logs'))) {
    fs.mkdirSync(path.join(__dirname, 'logs'));
}

// Track restarts
let restarts = [];

/**
 * Log a message to the console and to a file
 */
function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;

    console.log(logMessage);

    // Append to log file
    try {
        // Check if log file is too large
        if (fs.existsSync(LOG_FILE)) {
            const stats = fs.statSync(LOG_FILE);
            const fileSizeInMB = stats.size / (1024 * 1024);

            // If file is larger than 10MB, rotate it
            if (fileSizeInMB > 10) {
                const timestamp = new Date().toISOString().replace(/:/g, '-');
                fs.renameSync(LOG_FILE, `${LOG_FILE}.${timestamp}.old`);
                console.log(`Rotated bot restart log file (${fileSizeInMB.toFixed(2)}MB)`);
            }
        }

        fs.appendFileSync(LOG_FILE, logMessage + '\n');
    } catch (error) {
        console.error('Error writing to log file:', error.message);
    }
}

/**
 * Start the bot process
 */
function startBot() {
    // Clean up old restarts outside the restart period
    const now = Date.now();
    restarts = restarts.filter(time => now - time < RESTART_PERIOD);

    // Check if we've restarted too many times
    if (restarts.length >= MAX_RESTARTS) {
        log(`ERROR: Bot has crashed ${restarts.length} times in the last hour. Not restarting automatically.`);
        log('Please check the logs and restart manually when the issue is fixed.');
        process.exit(1);
    }

    // Add this restart to the list
    restarts.push(now);

    // Log the restart
    if (restarts.length > 1) {
        log(`Restarting bot (restart ${restarts.length} in the last hour)...`);
    } else {
        log('Starting bot...');
    }

    // Start the bot with --expose-gc flag to enable manual garbage collection
    // Using a much higher memory limit (16GB) since you have 64GB available
    const botProcess = spawn('node', ['--max-old-space-size=16384', '--expose-gc', 'index.js'], {
        stdio: 'inherit',
        env: {
            ...process.env
        }
    });

    // Handle process exit
    botProcess.on('exit', (code, signal) => {
        if (code === 0) {
            log('Bot exited cleanly.');
            process.exit(0);
        } else {
            const exitReason = signal
                ? `was killed by signal ${signal}`
                : `exited with code ${code}`;

            log(`Bot ${exitReason}. Restarting in ${RESTART_DELAY / 1000} seconds...`);

            // Wait before restarting
            setTimeout(startBot, RESTART_DELAY);
        }
    });

    // Handle process errors
    botProcess.on('error', (err) => {
        log(`Failed to start bot: ${err.message}`);

        // Wait before restarting
        setTimeout(startBot, RESTART_DELAY);
    });
}

// Start the bot
startBot();
